package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("company_lease_profile")
@ApiModel("企业租赁概况")
public class CompanyLeaseProfile {
    /** 主键 */
    @TableId
    @ApiModelProperty("主键ID")
    private String id;

    /** 企业id */
    @ApiModelProperty("企业ID")
    private String companyId;

    /** 入住时间 */
    @ApiModelProperty("入住时间")
    private LocalDate checkInTime;

//    /** 享受政策 */
//    @ApiModelProperty("享受政策")
//    private String enjoyPolicy;
//
//    /** 优惠政策 */
//    @ApiModelProperty("优惠政策")
//    private String preferentialPolicy;

    /** 有无投资协议（0否/1是） */
//    @ApiModelProperty("有无投资协议（0否/1是）")
//    private Boolean hasInvestmentAgreement;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private String updateBy;
}
