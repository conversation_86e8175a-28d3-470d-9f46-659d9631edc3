package com.quantchi.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;

import java.io.*;

/**
 * Word文档转PDF工具类
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
public class DocToPdfConverter {

    /**
     * 将Word文档转换为PDF
     *
     * @param wordInputStream Word文档输入流
     * @param pdfOutputStream PDF输出流
     * @throws Exception 转换异常
     */
    public static void convert(InputStream wordInputStream, OutputStream pdfOutputStream) throws Exception {
        log.info("开始将Word文档转换为PDF");
        
        // 加载Word文档
        XWPFDocument document = null;
        try {
            document = new XWPFDocument(wordInputStream);
            
            // 配置PDF选项
            PdfOptions options = PdfOptions.create();
            
            // 执行转换
            PdfConverter.getInstance().convert(document, pdfOutputStream, options);
            
            log.info("Word转PDF转换完成");
        } finally {
            // 关闭资源
            if (document != null) {
                document.close();
            }
        }
    }
}
