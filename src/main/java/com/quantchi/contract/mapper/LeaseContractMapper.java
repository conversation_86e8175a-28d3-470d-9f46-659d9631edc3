package com.quantchi.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.quantchi.contract.model.entity.LeaseContract;
import com.quantchi.contract.model.vo.LeaseContractListVO;
import com.quantchi.contract.model.vo.LeaseContractPageQuery;
import com.quantchi.contract.model.vo.LeaseContractSimpleVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 租赁合同 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
public interface LeaseContractMapper extends MPJBaseMapper<LeaseContract> {

    /**
     * 分页查询合同列表，支持合同编号和承租方名称模糊查询
     */
    List<LeaseContractListVO> pageLeaseContractList(@Param("offset") int offset, @Param("size") int size, @Param("query") LeaseContractPageQuery query);

    /**
     * union 查询合同总数
     */
    long countLeaseContractList(@Param("query") LeaseContractPageQuery query);

    /**
     * 根据lesseeId查询合同id和contractNo
     */
    List<LeaseContractSimpleVO> selectIdAndNoByLesseeId(@Param("lesseeId") String lesseeId);

    /**
     * 统计未来30天内合同到期的企业数量
     */
    long countExpiringCompanies(@Param("endDate") LocalDate endDate);

    /**
     * 统计指定园区未来30天内合同到期的企业数量
     * 
     * @param endDate 结束日期
     * @param parkId 园区ID
     * @return 到期企业数量
     */
    long countExpiringCompaniesByPark(@Param("endDate") LocalDate endDate, @Param("parkId") String parkId);
}
