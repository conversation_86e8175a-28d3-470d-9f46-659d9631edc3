package com.quantchi.contract.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;

@Data
@ApiModel("合同分页列表展示")
public class LeaseContractListVO {
    @ApiModelProperty("合同ID")
    private String id;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("承租方名称")
    private String lessee;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("租赁开始时间")
    private LocalDate leaseStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("租赁结束时间")
    private LocalDate leaseEndTime;

    @ApiModelProperty("合同金额")
    private Integer rentalCollect;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private LocalDate createTime;

    @ApiModelProperty("创建人")
    private String createUser;
}
