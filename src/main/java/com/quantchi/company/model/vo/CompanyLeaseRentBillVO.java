package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("企业租金账单信息")
public class CompanyLeaseRentBillVO {

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("租金账单明细")
    private List<RentBillItem> rentBills = new ArrayList<>();

    @Data
    @ApiModel("租金账单明细")
    public static class RentBillItem {

        @ApiModelProperty("合同ID")
        private String contractId;

        @ApiModelProperty("合同编号")
        private String contractNo;

        @ApiModelProperty("年份")
        private Integer year;

        @ApiModelProperty("季度")
        private Integer quarter;
        @ApiModelProperty("应收金额")

        private BigDecimal receivableAmount;
        @ApiModelProperty("已收金额")

        private BigDecimal receivedAmount;
        @ApiModelProperty("未收金额")

        private BigDecimal unreceivedAmount;
        @ApiModelProperty("对赌金额")

        private BigDecimal betAmount;

        @ApiModelProperty("折扣系数")
        private BigDecimal discount;

        @ApiModelProperty("减免金额")
        private BigDecimal reduction;
    }
}
