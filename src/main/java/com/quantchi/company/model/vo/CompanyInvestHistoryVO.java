package com.quantchi.company.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
public class CompanyInvestHistoryVO {

    @ApiModelProperty("被投企业ID")
    private String investedId;

    @ApiModelProperty("被投企业名称")
    private String investedName;

    @ApiModelProperty("被投企业经营状态")
    private String investedStatus;

    @ApiModelProperty("投资比例")
    private BigDecimal investRatio;

    @ApiModelProperty("投资金额描述")
    private String investAmount;

    @ApiModelProperty("被投企业成立日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date investedEstablishedDate;

    @ApiModelProperty("被投企业注册行业")
    private String investedIndustry;

    @ApiModelProperty("所属地区")
    private String investBelongArea;

    @ApiModelProperty("投资特征")
    private String investType;
}
