package com.quantchi.application.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.application.mapper.ApplicationDecorationMapper;
import com.quantchi.application.mapper.ApplicationMapper;
import com.quantchi.application.mapper.ApplicationMoveInMapper;
import com.quantchi.application.mapper.ApplicationMoveOutMapper;
import com.quantchi.application.model.bo.ApplicationQueryBO;
import com.quantchi.application.model.entity.Application;
import com.quantchi.application.model.entity.ApplicationDecoration;
import com.quantchi.application.model.entity.ApplicationMoveIn;
import com.quantchi.application.model.entity.ApplicationMoveOut;
import com.quantchi.application.model.enums.ApplicationStatus;
import com.quantchi.application.model.enums.ApplyType;
import com.quantchi.application.model.vo.ApplicationPageVO;
import com.quantchi.application.model.vo.ApplicationRecordVO;
import com.quantchi.application.service.IApplicationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.company.mapper.CompanyMapper;
import com.quantchi.company.model.entity.Company;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 申请 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ApplicationServiceImpl extends ServiceImpl<ApplicationMapper, Application> implements IApplicationService {

    private final ApplicationMoveInMapper applicationMoveInMapper;
    private final ApplicationMoveOutMapper applicationMoveOutMapper;
    private final ApplicationDecorationMapper applicationDecorationMapper;
    private final CompanyMapper companyMapper;

    /**
     * 分页查询办事记录
     *
     * @param queryBO 查询参数
     * @return 分页结果
     */
    @Override
    public ApplicationPageVO pageApplicationRecords(final ApplicationQueryBO queryBO) {
        // 构建查询条件
        LambdaQueryWrapper<Application> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加筛选条件，如果有的话
        if (queryBO.getApplyType() != null) {
            queryWrapper.eq(Application::getApplyType, queryBO.getApplyType());
        }
        
        if (queryBO.getStatus() != null) {
            queryWrapper.eq(Application::getStatus, queryBO.getStatus());
        }
        
        // 使用当前登录用户ID作为申请人ID
        String loginId = StpUtil.getLoginIdAsString();
        if (StringUtils.isNotBlank(loginId)) {
            queryWrapper.eq(Application::getApplicantId, loginId);
        }
        
        // 按申请时间降序排序，最新的申请排在前面
        queryWrapper.orderByDesc(Application::getApplyTime);
        
        // 执行分页查询
        Page<Application> page = new Page<>(queryBO.getPageNum(), queryBO.getPageSize());
        IPage<Application> resultPage = page(page, queryWrapper);
        
        // 构建返回结果
        ApplicationPageVO pageVO = new ApplicationPageVO();
        pageVO.setTotal(resultPage.getTotal());
        pageVO.setPageSize(queryBO.getPageSize());
        pageVO.setPageNum(queryBO.getPageNum());
        pageVO.setPages(resultPage.getPages());
        
        // 转换记录列表
        List<ApplicationRecordVO> records = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resultPage.getRecords())) {
            records = resultPage.getRecords().stream().map(this::convertToRecordVO).collect(Collectors.toList());
        }
        
        pageVO.setRecords(records);
        return pageVO;
    }
    
    /**
     * 将Application实体转换为ApplicationRecordVO
     *
     * @param application 申请实体
     * @return ApplicationRecordVO
     */
    private ApplicationRecordVO convertToRecordVO(final Application application) {
        ApplicationRecordVO recordVO = new ApplicationRecordVO();
        recordVO.setApplicationId(application.getId());
        recordVO.setApplicantName(getApplicantName(application));
        recordVO.setApplyType(application.getApplyType());
        recordVO.setApplyTypeName(application.getApplyType() != null ? ApplyType.getInfoByCode(application.getApplyType()) : "");
        recordVO.setApplyTime(application.getApplyTime());
        recordVO.setFinishTime(application.getFinishTime());
        recordVO.setStatus(application.getStatus());
        recordVO.setStatusName(application.getStatus() != null ? ApplicationStatus.getInfoByCode(application.getStatus()) : "");
        return recordVO;
    }
    
    /**
     * 根据申请类型和申请ID获取申请单位名称
     *
     * @param application 申请实体
     * @return 申请单位名称
     */
    private String getApplicantName(final Application application) {
        if (application == null || application.getApplyType() == null) {
            return "";
        }
        
        Integer applyType = application.getApplyType();
        String applicationId = application.getId();
        
        if (StringUtils.isBlank(applicationId)) {
            return "";
        }
        
        // 根据不同申请类型获取申请单位名称
        String companyId = "";

        if (ApplyType.MOVE_IN.getCode().equals(applyType)) {
            ApplicationMoveIn moveIn = applicationMoveInMapper.selectOne(
                    new LambdaQueryWrapper<ApplicationMoveIn>()
                            .eq(ApplicationMoveIn::getApplicationId, applicationId)
                            .last("limit 1")
            );
            if (moveIn != null) {
                companyId = moveIn.getCompanyId();
            }
        } else if (ApplyType.MOVE_OUT.getCode().equals(applyType)) {
            ApplicationMoveOut moveOut = applicationMoveOutMapper.selectOne(
                    new LambdaQueryWrapper<ApplicationMoveOut>()
                            .eq(ApplicationMoveOut::getApplicationId, applicationId)
                            .last("limit 1")
            );
            if (moveOut != null) {
                companyId = moveOut.getCompanyId();
            }
        } else {
            ApplicationDecoration decoration = applicationDecorationMapper.selectOne(
                    new LambdaQueryWrapper<ApplicationDecoration>()
                            .eq(ApplicationDecoration::getApplicationId, applicationId)
                            .last("limit 1")
            );
            if (decoration != null) {
                companyId = decoration.getCompanyId();
            }
        }

        if (StringUtils.isNotBlank(companyId)) {
            // 仅查询name字段并限制只返回一条记录
            Company company = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getId, companyId)
                            .select(Company::getName)
                            .last("limit 1")
            );
            return company != null ? company.getName() : "";
        }
        
        return "";
    }
    
    /**
     * 撤销办事申请
     *
     * @param applicationId 申请ID
     * @return 是否撤销成功
     */
    @Override
    public boolean cancelApplication(final String applicationId) {
        if (StringUtils.isBlank(applicationId)) {
            log.error("撤销办事申请失败：申请ID为空");
            return false;
        }
        
        // 查询申请信息
        Application application = getById(applicationId);
        if (application == null) {
            log.error("撤销办事申请失败：未找到申请记录，applicationId={}", applicationId);
            return false;
        }
        
        // 判断当前状态是否可撤销（只有处理中的申请可以撤销）
        if (!ApplicationStatus.PROCESSING.getCode().equals(application.getStatus())) {
            log.error("撤销办事申请失败：当前状态不可撤销，applicationId={}，status={}", 
                     applicationId, ApplicationStatus.getInfoByCode(application.getStatus()));
            return false;
        }
        
        // 设置申请状态为已撤销
        application.setStatus(ApplicationStatus.PROCESS_UNDO.getCode());
        application.setFinishTime(LocalDate.now());
        
        // 更新申请状态
        return updateById(application);
    }

    @Override
    public void downloadDecorationCommitment(HttpServletResponse response) throws Exception {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("utf-8");

            // 设置文件名
            String fileName = "装修承诺书.docx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);

            // 从resources/templates目录读取文件
            try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("templates/装修承诺书.docx")) {
                if (inputStream == null) {
                    log.error("装修承诺书模板文件不存在：templates/装修承诺书.docx");
                    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    response.setContentType("application/json;charset=utf-8");
                    response.getWriter().write("{\"success\":false,\"message\":\"模板文件不存在\"}");
                    return;
                }

                // 将文件内容写入响应流
                try (OutputStream outputStream = response.getOutputStream()) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();
                }

                log.info("装修承诺书下载成功，操作用户：{}", StpUtil.getLoginIdAsString());
            }

        } catch (IOException e) {
            log.error("下载装修承诺书失败，操作用户：{}", StpUtil.getLoginIdAsString(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"success\":false,\"message\":\"下载失败：" + e.getMessage() + "\"}");
            throw new Exception("下载装修承诺书失败：" + e.getMessage());
        }
    }
}
