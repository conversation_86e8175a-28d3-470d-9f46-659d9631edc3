package com.quantchi.company.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.company.model.bo.CompanyLeaseContractEditBO;
import com.quantchi.company.model.entity.Company;
import com.quantchi.company.model.entity.CompanyLeaseProfile;
import com.quantchi.company.mapper.CompanyLeaseProfileMapper;
import com.quantchi.company.model.vo.CompanyLeaseContractSimpleListVO;
import com.quantchi.company.model.vo.CompanyLeaseContractSimpleVO;
import com.quantchi.company.model.vo.CompanyLeaseContractVO;
import com.quantchi.company.model.vo.CompanyLeaseRentBillVO;
import com.quantchi.company.service.CompanyLeaseProfileService;
import com.quantchi.contract.mapper.LeaseContractZoneMapper;
import com.quantchi.contract.model.entity.LeaseContract;
import com.quantchi.contract.mapper.LeaseContractMapper;
import com.quantchi.contract.mapper.LeaseRentMapper;
import com.quantchi.contract.model.entity.LeaseContractZone;
import com.quantchi.contract.model.entity.LeaseRent;
import com.quantchi.sys.utils.LoginHelper;
import com.quantchi.zone.mapper.ZoneMapper;
import com.quantchi.company.mapper.CompanyMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quantchi.zone.model.entity.Building;
import com.quantchi.zone.model.entity.Floor;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.Zone;
import com.quantchi.zone.model.vo.ZoneDetailVO;
import com.quantchi.company.enums.ContractStatusEnum;
import com.quantchi.zone.service.impl.BuildingServiceImpl;
import com.quantchi.zone.service.impl.FloorServiceImpl;
import com.quantchi.zone.service.impl.ParkServiceImpl;
import com.quantchi.zone.service.impl.ZoneServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 
 *
 * <AUTHOR> @since 2022-02-11
 */
@Service
public class CompanyLeaseProfileServiceImpl extends ServiceImpl<CompanyLeaseProfileMapper, CompanyLeaseProfile> implements CompanyLeaseProfileService {

    @Autowired
    private CompanyLeaseProfileMapper companyLeaseProfileMapper;
    @Autowired
    private LeaseContractMapper leaseContractMapper;
    @Autowired
    private LeaseContractZoneMapper leaseContractZoneMapper;
    @Autowired
    private LeaseRentMapper leaseRentMapper;
    @Autowired
    private ZoneMapper zoneMapper;
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private ParkServiceImpl parkService;
    @Autowired
    private BuildingServiceImpl buildingService;
    @Autowired
    private FloorServiceImpl floorService;
    @Autowired
    private ZoneServiceImpl zoneService;

    @Value("${self.address}")
    private String downloadAddress;

    /**
     * 根据companyId查询合同及租赁概况信息
     * @param companyId 企业ID
     * @return 企业租赁合同信息VO
     */
    @Override
    public CompanyLeaseContractVO getLeaseContractInfo(String companyId) {
        CompanyLeaseContractVO vo = new CompanyLeaseContractVO();

        // 查询租赁合同
        List<LeaseContract> contractList = leaseContractMapper.selectList(
                new QueryWrapper<LeaseContract>().lambda().eq(LeaseContract::getLesseeId, companyId)
        );
        if (contractList.isEmpty()) {
            return vo;
        }
        // 取 leaseEndTime 最大的合同
        LeaseContract contract = contractList.stream()
            .filter(c -> c.getLeaseEndTime() != null)
            .max(java.util.Comparator.comparing(LeaseContract::getLeaseEndTime))
            .orElse(contractList.get(0));

        // 组装合同相关信息
        if (contract != null) {
            List<LeaseContractZone> leaseContractZones = leaseContractZoneMapper.selectList(Wrappers.lambdaQuery(LeaseContractZone.class)
                    .eq(LeaseContractZone::getContractId, contract.getId()));
            if (CollectionUtils.isNotEmpty(leaseContractZones)) {
                List<String> zoneIds = leaseContractZones.stream().map(LeaseContractZone::getZoneId).collect(Collectors.toList());
                List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).in(Zone::getId, zoneIds));
                getLocation(zones, vo);
            }

            // 格式化租赁期限
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy.MM.dd");
            if (contract.getLeaseStartTime() != null && contract.getLeaseEndTime() != null) {
                vo.setLeasePeriod(contract.getLeaseStartTime().format(fmt) + "-" + contract.getLeaseEndTime().format(fmt));
            }

            vo.setRentStandard(contract.getRentalStandard() == null ? null : contract.getRentalStandard().toString());
            vo.setTotalRent(contract.getRentalCollect() == null ? null : contract.getRentalCollect().doubleValue());
            vo.setPreferentialPolicy(contract.getRemark());
            vo.setHasInvestmentAgreement(StringUtils.isNotBlank(contract.getInvestAgreementName()));
            vo.setInvestAgreementName(contract.getInvestAgreementName());
            vo.setInvestAgreementPath(StringUtils.isNotBlank(contract.getInvestAgreementPath()) ?
                    downloadAddress + contract.getInvestAgreementPath() : null);
        }

        // 查询企业租赁概况
        List<CompanyLeaseProfile> profileList = companyLeaseProfileMapper.selectList(
                new QueryWrapper<CompanyLeaseProfile>().lambda().eq(CompanyLeaseProfile::getCompanyId, companyId)
        );
        if (profileList.isEmpty()) {
            return vo;
        }
        CompanyLeaseProfile profile = profileList.get(0);
        // 组装企业租赁概况信息
        if (profile != null) {
            vo.setCheckInTime(profile.getCheckInTime());
            vo.setRemark(profile.getRemark());
        }
        return vo;
    }

    private void getLocation(List<Zone> zones, CompanyLeaseContractVO vo) {
        Map<String, List<Zone>> zoneMap = zones.stream().collect(Collectors.groupingBy(Zone::getBuildId));
        int parkSize = (int) zones.stream().map(Zone::getParkId).distinct().count();
        StringBuilder parks = new StringBuilder();
        StringBuilder buildings = new StringBuilder();
        StringBuilder floors = new StringBuilder();
        StringBuilder locationInfo = new StringBuilder();
        AtomicReference<Double> area = new AtomicReference<>(0.0);
        zoneMap.forEach((buildId, zoneList) -> {
            Zone temp = zoneList.get(0);
            // parkId -> parkName
            if (StringUtils.isNotBlank(temp.getParkId())) {
                Park park = parkService.getById(temp.getParkId());
                if (park != null) {
                    parks.append(park.getParkName()).append("、");
                    if (parkSize > 1) {
                        buildings.append(park.getParkName()).append("-");
                        locationInfo.append(park.getParkName()).append("-");
                    }
                }
            }
            if (StringUtils.isNotBlank(temp.getBuildId())) {
                Building building = buildingService.getById(temp.getBuildId());
                if (building != null) {
                    buildings.append(building.getBuildingName()).append("、");
                    if (zoneMap.size() > 1) {
                        locationInfo.append(building.getBuildingName()).append("-");
                    }
                }
            }
            zoneList.forEach(zone -> {
                locationInfo.append(zone.getZoneName()).append("、");
                floors.append(floorService.list(Wrappers.lambdaQuery(Floor.class)
                        .select(Floor::getFloorName).eq(Floor::getId, zone.getFloorId())
                        .last("limit 1")).get(0).getFloorName()).append("、");
                area.accumulateAndGet(Double.valueOf(zone.getArea()), (current, inc) -> current + inc);
            });
            locationInfo.deleteCharAt(locationInfo.lastIndexOf("、"));
            locationInfo.append("；");
        });
        parks.deleteCharAt(parks.lastIndexOf("、"));
        buildings.deleteCharAt(buildings.lastIndexOf("、"));
        floors.deleteCharAt(floors.lastIndexOf("、"));
        locationInfo.deleteCharAt(locationInfo.lastIndexOf("；"));
        vo.setParkName(parks.toString());
        vo.setBuildingName(buildings.toString());
        vo.setFloorName(floors.toString());
        vo.setZone(locationInfo.toString());
        vo.setArea(area.get());
    }

    /**
     * 编辑企业租赁合同信息
     * @param editBO 企业租赁合同编辑参数
     * @return 是否编辑成功
     */
    @Override
    public boolean editLeaseContractInfo(CompanyLeaseContractEditBO editBO) {
        if (editBO == null || StringUtils.isBlank(editBO.getCompanyId())) {
            return false;
        }
        // 查询企业租赁概况
        List<CompanyLeaseProfile> profileList = companyLeaseProfileMapper.selectList(
            new QueryWrapper<CompanyLeaseProfile>().lambda()
                .eq(CompanyLeaseProfile::getCompanyId, editBO.getCompanyId())
        );
        if (!profileList.isEmpty()) {
            CompanyLeaseProfile profile = profileList.get(0);
            // 更新相关字段
            profile.setCheckInTime(editBO.getCheckInTime());
            profile.setRemark(editBO.getRemark());
            profile.setUpdateBy(LoginHelper.getUsername());
            profile.setUpdateTime(LocalDateTime.now());
            return companyLeaseProfileMapper.updateById(profile) > 0;
        }
        CompanyLeaseProfile profile = new CompanyLeaseProfile();
        profile.setId(UUID.randomUUID().toString().replace("-",""));
        profile.setCompanyId(editBO.getCompanyId());
        profile.setCheckInTime(editBO.getCheckInTime());
        profile.setRemark(editBO.getRemark());
        profile.setCreateBy(LoginHelper.getUsername());
        profile.setCreateTime(LocalDateTime.now());
        profile.setUpdateBy(LoginHelper.getUsername());
        profile.setUpdateTime(LocalDateTime.now());
        return companyLeaseProfileMapper.insert(profile) > 0;

    }

    /**
     * 查询企业租赁合同简要信息
     * @param companyId 企业ID
     * @return 合同简要信息VO，包括合同id、编号、名称、起止时间、状态、附件等
     */
    @Override
    public CompanyLeaseContractSimpleListVO getLeaseContractSimple(String companyId) {
        List<LeaseContract> contractList = leaseContractMapper.selectList(
                new QueryWrapper<LeaseContract>().lambda()
                        .eq(LeaseContract::getLesseeId, companyId)
        );
        List<CompanyLeaseContractSimpleVO> voList = new ArrayList<>();
        // 查询公司名称
        String companyName = null;
        Company company = companyMapper.selectById(companyId);
        if (company != null) {
            companyName = company.getName();
        }
        LocalDate now = LocalDate.now();
        for (LeaseContract contract : contractList) {
            CompanyLeaseContractSimpleVO vo = new CompanyLeaseContractSimpleVO();
            vo.setContractId(contract.getId());
            vo.setContractNo(contract.getContractNo());
            // 合同名称=公司名称+房屋租赁合同
            vo.setContractName((companyName != null ? companyName : "") + "房屋租赁合同");
            vo.setLeaseStartTime(contract.getLeaseStartTime());
            vo.setLeaseEndTime(contract.getLeaseEndTime());
            // 合同状态判断，1-生效中，0-已失效，使用枚举
            if (contract.getLeaseStartTime() != null && contract.getLeaseEndTime() != null
                    && !now.isBefore(contract.getLeaseStartTime()) && !now.isAfter(contract.getLeaseEndTime())) {
                vo.setContractStatus(ContractStatusEnum.ACTIVE.getCode());
            } else if (contract.getLeaseStartTime() != null && contract.getLeaseEndTime() != null
                    && now.isBefore(contract.getLeaseStartTime())) {
                vo.setContractStatus(ContractStatusEnum.INACTIVE.getCode());
            } else {
                vo.setContractStatus(ContractStatusEnum.INVALID.getCode());
            }
            vo.setContractScanCopyName(contract.getContractScanCopyName());
            vo.setContractScanCopyPath(contract.getContractScanCopyPath());
            voList.add(vo);
        }
        CompanyLeaseContractSimpleListVO result = new CompanyLeaseContractSimpleListVO();
        result.setContracts(voList);
        return result;
    }

    /**
     * 查询企业租金账单
     * @param companyId 企业ID
     * @return 企业租金账单VO，包含合同id及租金明细
     */
    @Override
    public List<CompanyLeaseRentBillVO> getCompanyLeaseRentBill(String companyId) {

        List<CompanyLeaseRentBillVO> res = new ArrayList<>();

        // 查询合同
        List<LeaseContract> contractList = leaseContractMapper.selectList(
                new QueryWrapper<LeaseContract>().lambda().eq(LeaseContract::getLesseeId, companyId)
        );
        if (contractList.isEmpty()) {
            return res;
        }

        Map<String, String> contractNoMap = contractList.stream().collect(Collectors.toMap(LeaseContract::getId, LeaseContract::getContractNo));


        // 查询租金明细
        QueryWrapper<LeaseRent> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(LeaseRent::getCompanyId, companyId);
        // 按年、季度排序
        wrapper.orderByAsc("year", "quarter");

        List<LeaseRent> rentList = leaseRentMapper.selectList(wrapper);

        if (rentList.isEmpty()) {
            return res;
        }

        List<CompanyLeaseRentBillVO.RentBillItem> items = new ArrayList<>();

        for (LeaseRent rent : rentList) {
            CompanyLeaseRentBillVO.RentBillItem item = new CompanyLeaseRentBillVO.RentBillItem();
            BeanUtils.copyProperties(rent, item);
            item.setContractNo(contractNoMap.get(item.getContractId()));
            items.add(item);
        }

        items.stream().collect(Collectors.groupingBy(CompanyLeaseRentBillVO.RentBillItem::getYear))
                .forEach((year, list) -> {
                    CompanyLeaseRentBillVO vo = new CompanyLeaseRentBillVO();
                    vo.setYear(year);
                    vo.setRentBills(list);
                    res.add(vo);
        });

        return res;
    }
}
