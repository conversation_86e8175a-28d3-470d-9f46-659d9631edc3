package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_leader")
@ApiModel(value = "CompanyLeader对象", description = "")
public class CompanyLeader implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录主键")
    private String id;

    @ApiModelProperty("企业ID")
    private String cid;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("人员名称")
    private String name;

    @ApiModelProperty("职位")
    private String position;

    @ApiModelProperty("量知职位排序")
    private String lzLevel;

    @ApiModelProperty("排序号")
    private String level;

    @ApiModelProperty("简介")
    private String description;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String COMPANY_NAME = "company_name";

    public static final String NAME = "name";

    public static final String POSITION = "position";

    public static final String LEVEL = "level";

    public static final String DESCRIPTION = "description";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
