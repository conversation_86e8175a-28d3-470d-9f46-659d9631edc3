package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 装修申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_decoration")
@ApiModel(value = "ApplicationDecoration对象", description = "装修申请表")
public class ApplicationDecoration implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private String id;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 公司ID */
    @ApiModelProperty("公司ID")
    private String companyId;

    /** 联系方式 */
    @ApiModelProperty("联系方式")
    private String contactInfo;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String COMPANY_ID = "company_id";

    public static final String CONTACT_INFO = "contact_info";

    public static final String CREATE_TIME = "create_time";

    public static final String CREATE_USER = "create_user";
}
