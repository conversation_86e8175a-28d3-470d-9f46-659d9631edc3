package com.quantchi.common.constant;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi://";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap://";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 成功标记
     */
    public static final Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    public static final Integer FAIL = 500;

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 限制登录ip redis key 前缀
     */
    public static final String LIMIT_LOGIN_IP_KEY = "limit_login_ip:";

    /**
     * 被其他ip挤掉的用户 redis key 前缀
     */
    public static final String PUSHED_USERNAME = "pushed_username:";

    /**
     * 验证码有效期（分钟）
     */
    public static final long CAPTCHA_EXPIRATION = 4;


    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 用于开关、在离线等判断（是）
     */
    public static final Integer INT_TRUE = 1;

    /**
     * 用于开关、在离线等判断（否）
     */
    public static final Integer INT_FALSE = 0;

    /**
     * 参数管理 cache name
     */
    public static final String SYS_CONFIG_CACHE = "sys-config";

    /**
     * 字典管理 cache name
     */
    public static final String SYS_DICT_CACHE = "sys-dict";

    /**
     * 系统用户授权缓存
     */
    public static final String SYS_AUTH_CACHE = "sys-authCache";

    /**
     * 登录成功为0
     */
    public static final String LOGIN_SUCCESS_STATUS = "0";

    /**
     * 登录失败为1
     */
    public static final String LOGIN_FAIL_STATUS = "1";
    public static final List<String> ORG = new ArrayList<>();
    public static final List<String> zhiXiaShiOfProvinceName = Arrays.asList("上海", "北京", "重庆", "天津");
    public static final List<String> zhiXiaShiOfCityName = Arrays.asList("上海市", "北京市", "重庆市", "天津市");
    /**
     * 上市企业标签
     */
    public static final List<String> COMPANY_LISTED_TAG = new ArrayList<>();
    //企业导航-企业标签(除上市企业外与企业库筛选保持一致)
    public static final List<String> COMPANY_NAVIGATION_TAG = new ArrayList<>();
    /**
     * 最新年份
     */
    public static final int NEWEST_YEAR = 2024;
    public final static BigDecimal BAI = new BigDecimal("100");
    public final static BigDecimal WAN = new BigDecimal("10000");
    public final static BigDecimal YI = new BigDecimal("*********");
    public final static BigDecimal WAN_YI = new BigDecimal("*********0000");
    /**
     * 通用成功标识
     */
    public static final String SUCCESS_FLAG = "1";
    /**
     * 通用失败标识
     */
    public static final String FAIL_FLAG = "0";

    static {
        ORG.add("浙江大学");
        ORG.add("中国科学技术大学");
        ORG.add("合肥工业大学");
        ORG.add("安徽大学");
        ORG.add("上海交通大学");
        ORG.add("复旦大学");
        ORG.add("同济大学");
        ORG.add("华东师范大学");
        ORG.add("华东理工大学");
        ORG.add("上海大学");
        ORG.add("东华大学");
        ORG.add("南京大学");
        ORG.add("东南大学");
        ORG.add("南京理工大学");
        ORG.add("南京航空航天大学");
        ORG.add("苏州大学");
        ORG.add("中国矿业大学");
        ORG.add("南京师范大学");
        ORG.add("江南大学");
        ORG.add("河海大学");

    }

    static {
        COMPANY_LISTED_TAG.add("A股");
        COMPANY_LISTED_TAG.add("B股");
        COMPANY_LISTED_TAG.add("港股");
        COMPANY_LISTED_TAG.add("中概股");
    }

    static {
        COMPANY_NAVIGATION_TAG.add("高新技术企业");
        COMPANY_NAVIGATION_TAG.add("专精特新企业");
        COMPANY_NAVIGATION_TAG.add("专精特新小巨人企业");
        COMPANY_NAVIGATION_TAG.add("企业技术中心");
        COMPANY_NAVIGATION_TAG.add("科技小巨人");
        COMPANY_NAVIGATION_TAG.add("独角兽企业");
        COMPANY_NAVIGATION_TAG.add("科技型中小企业");
        COMPANY_NAVIGATION_TAG.add("隐形冠军企业");
        COMPANY_NAVIGATION_TAG.add("雏鹰企业");
    }

    /**
     * 获取所属区域
     *
     * @param province
     * @param city
     * @return
     */
    public static String getBelongArea(String province, String city, String area) {
        if (province == null) {
            province = "";
        }
        if (city == null) {
            city = "";
        }
        if (area == null) {
            area = "";
        }
        if (!zhiXiaShiOfProvinceName.contains(province) && !zhiXiaShiOfCityName.contains(province)) {
            return province + city + area;
        }
        return city + area;
    }
}
