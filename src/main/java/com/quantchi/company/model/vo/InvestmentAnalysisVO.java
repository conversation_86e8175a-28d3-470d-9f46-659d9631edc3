package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("投资机构分析展示类")
public class InvestmentAnalysisVO {

    @ApiModelProperty("投资机构总数量")
    private int totalInvestmentInstitutions;

    @ApiModelProperty("VC/PE渗透率")
    private BigDecimal vcPePenetrationRate;

    @ApiModelProperty("主要投资阶段")
    private String mainInvestmentStage;

    @ApiModelProperty("主要投资领域")
    private String mainInvestmentField;

    @ApiModelProperty("投资机构详情")
    private List<InvestmentInstitutionDetail> investmentInstitutionDetails;

    @ApiModelProperty(value = "主要投资阶段", notes = "取前三数据，剩下的统一用'其他'指代")
    private List<NameCountVO> investmentStageList;

    @ApiModelProperty(value = "主要投资领域", notes = "取前三数据，剩下的统一用'其他'指代")
    private List<NameCountVO> investmentFieldList;

    @Data
    public static class InvestmentInstitutionDetail {

        @ApiModelProperty("投资机构名称")
        private String institutionName;

        @ApiModelProperty("标签")
        private List<String> tagList;

        @ApiModelProperty("专注阶段")
        private List<String> investmentRoundList;

        @ApiModelProperty("管理规模")
        private String managementScale;

        @ApiModelProperty("专注领域")
        private List<String> focusedFieldList;

        @ApiModelProperty("历史投资战绩-投资")
        private Integer historicalInvestment;

        @ApiModelProperty("历史投资战绩-退出")
        private Integer historicalOut;

        @ApiModelProperty("进入下一轮比例")
        private Double nextRoundRate;
    }

}

