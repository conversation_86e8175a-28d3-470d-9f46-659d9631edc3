package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_tag")
@ApiModel(value = "CompanyTag对象", description = "")
public class CompanyTag implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("企业ID")
    private String cid;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("统代")
    private String creditCode;

    @ApiModelProperty("标签有效期开始时间")
    private LocalDate validBegin;

    @ApiModelProperty("标签有效期结束时间")
    private LocalDate validEnd;

    private String authority;

    @ApiModelProperty("标签级别")
    private String level;

    @ApiModelProperty("标签ID")
    private String tagId;

    @ApiModelProperty("标签类型")
    private String tagType;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("发布年份")
    private String publishYear;

    @ApiModelProperty("企业关联此标签的来源")
    private String source;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String COMPANY_NAME = "company_name";

    public static final String CREDIT_CODE = "credit_code";

    public static final String VALID_BEGIN = "valid_begin";

    public static final String VALID_END = "valid_end";

    public static final String AUTHORITY = "authority";

    public static final String LEVEL = "level";

    public static final String TAG_ID = "tag_id";

    public static final String TAG_TYPE = "tag_type";

    public static final String TAG_NAME = "tag_name";

    public static final String PUBLISH_YEAR = "publish_year";

    public static final String SOURCE = "source";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
