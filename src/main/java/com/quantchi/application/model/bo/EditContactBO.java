package com.quantchi.application.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 编辑企业联系人请求参数
 */
@Data
@ApiModel("编辑企业联系人请求参数")
public class EditContactBO {

    @ApiModelProperty(value = "联系人ID", required = true)
    @NotBlank(message = "联系人ID不能为空")
    private String contactId;

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "手机号")
    private String contactInfo;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "在职状态", required = true, notes = "0-离职，1-在职")
    private Integer jobStatus;

    @ApiModelProperty("备注")
    private String remark;

}
