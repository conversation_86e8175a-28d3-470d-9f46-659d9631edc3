package com.quantchi.application.service;

import com.quantchi.application.model.entity.ApplicationMoveOut;
import com.quantchi.application.model.bo.ApplicationMoveOutBO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.application.model.vo.ApplicationMoveOutVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 迁出申请内容表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IApplicationMoveOutService extends IService<ApplicationMoveOut> {

    /**
     * 迁出申请提交，包含BO转Entity、主表及子表保存等逻辑
     * @param bo 迁出申请BO
     */
    void submitMoveOutApplication(ApplicationMoveOutBO bo);

    ApplicationMoveOutVO applicationMoveOutDetail(String applicationId);
    
    /**
     * 下载迁出申请表
     * @param applicationId 申请ID
     * @param response HTTP响应
     * @throws Exception 处理异常
     */
    void downloadMoveOutApplication(String applicationId, HttpServletResponse response) throws Exception;
}
