package com.quantchi.application.model.vo;

import com.quantchi.application.model.bo.EquityStructureBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 入驻申请业务对象
 */
@Data
@ApiModel(value = "ApplicationMoveInVO", description = "入驻申请业务对象")
public class ApplicationMoveInVO {

    @ApiModelProperty("入驻类型: 0-新注册; 1-迁址")
    private Integer moveInType;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 审批人ID */
    @ApiModelProperty("审批人ID")
    private Long approver;

    /** 审批人姓名 */
    @ApiModelProperty("审批人姓名")
    private String approverName;

    /** 申请编号 */
    @ApiModelProperty("申请编号")
    private String applyCode;

    /** 申请时间 */
    @ApiModelProperty("申请时间")
    private LocalDate applyTime;

    /** 申请时间 */
    @ApiModelProperty("完成时间")
    private LocalDate finishTime;

    /** 申请类型 */
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private Integer applyType;

    /** 申请类型 */
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private String applyTypeCN;

    /** 当前状态 */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private Integer status;

    /** 当前状态 */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private String statusCN;

    @ApiModelProperty("公司名称")
    private String companyName;

    /** 公司ID */
    @ApiModelProperty("公司ID")
    private String companyId;

    @ApiModelProperty("实施项目")
    private String projectName;

    /**
     * 产业领域ID
     */
    @ApiModelProperty("产业领域ID")
    private String industryFieldId;

    /**
     * 所属类型ID
     */
    @ApiModelProperty("所属类型ID")
    private Integer typeId;

    /**
     * 产业领域
     */
    @ApiModelProperty("产业领域")
    private String industryField;

    /**
     * 所属类型
     */
    @ApiModelProperty("所属类型")
    private String type;

    /**
     * 成立时间
     */
    @ApiModelProperty("成立时间")
    private LocalDate establishmentDate;

    /**
     * 是否实际入驻
     */
    @ApiModelProperty("是否实际入驻: 0-否, 1-是")
    private Byte registration;

    /**
     * 目前地点
     */
    @ApiModelProperty("实际办公地址")
    private String actualLocation;

    /**
     * 项目来源
     */
    @ApiModelProperty("项目来源")
    private String projectSource;


    /**
     * 是否有经营异常（0否，1是）
     */
    @ApiModelProperty("是否有经营异常（0否，1是）")
    private Byte isAbnormalOperation;

    /**
     * 是否科技型中小企业（0否，1是）
     */
    @ApiModelProperty("是否科技型中小企业（0否，1是）")
    private Byte isTechEnterprise;
    /**
     * 是否高新产品（0否，1是）
     */
    @ApiModelProperty("是否高新产品（0否，1是）")
    private Byte isHighTechProduct;

    /**
     * 是否高新企业（0否，1是）
     */
    @ApiModelProperty("是否高新企业（0否，1是）")
    private Byte isHighTechEnterprise;

    /**
     * 上年度末注册资本
     */
    @ApiModelProperty("上年度末注册资本")
    private String registeredCapitalLastYear;

    /**
     * 上年度末从业人员
     */
    @ApiModelProperty("上年度末从业人员")
    private String employeesLastYear;

    /**
     * 上年度末知识产权
     */
    @ApiModelProperty("上年度末知识产权")
    private String intellectualPropertyLastYear;

    /**
     * 上年度实际总收入
     */
    @ApiModelProperty("上年度实际总收入")
    private String totalRevenueLastYear;

    /**
     * 上年度实际净利润
     */
    @ApiModelProperty("上年度实际净利润")
    private String netProfitLastYear;

    /**
     * 上年度实际纳税额
     */
    @ApiModelProperty("上年度实际纳税额")
    private String taxPaidLastYear;

    @ApiModelProperty("股权结构")
    private List<EquityStructureBO> equityStructureList;

    // 负责人信息字段（平铺）
    @ApiModelProperty("负责人姓名")
    private String leaderName;

    @ApiModelProperty("负责人性别")
    private String leaderGender;

    @ApiModelProperty("负责人国籍")
    private String leaderNationality;

    @ApiModelProperty("负责人年龄")
    private String leaderAge;

    @ApiModelProperty("负责人学历")
    private String leaderEducation;

    @ApiModelProperty("负责人学位")
    private String leaderDegree;

    @ApiModelProperty("负责人职称")
    private String leaderTitle;

    @ApiModelProperty("负责人专业")
    private String leaderMajor;

    @ApiModelProperty("法人代表毕业学校")
    private String leaderGraduateSchool;

    @ApiModelProperty("法人代表当前单位")
    private String leaderCurrentOrg;

    @ApiModelProperty("法人代表联系电话")
    private String leaderPhone;

    @ApiModelProperty("法人代表邮箱")
    private String leaderEmail;

    // 联系人信息字段（平铺）
    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系人性别")
    private String contactGender;

    @ApiModelProperty("联系人职务")
    private String contactPosition;

    @ApiModelProperty("联系人手机")
    private String contactMobile;

    @ApiModelProperty("联系人传真")
    private String contactFax;

    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("联系人邮箱")
    private String contactEmail;

    @ApiModelProperty("项目内容")
    private String projectContent;

    // 发展计划字段（平铺）
    /**
     * 第一年计划投资
     */
    @ApiModelProperty("第一年计划投资")
    private String investmentPlanFirstYear;

    /**
     * 前三年计划投资
     */
    @ApiModelProperty("前三年计划投资")
    private String investmentPlanThreeYears;

    /**
     * 第一年员工人数
     */
    @ApiModelProperty("第一年员工人数")
    private String employeesFirstYear;

    /**
     * 前三年员工人数
     */
    @ApiModelProperty("前三年员工人数")
    private String employeesThreeYears;

    /**
     * 第一年场地需求
     */
    @ApiModelProperty("第一年场地需求")
    private String siteDemandFirstYear;

    /**
     * 前三年场地需求
     */
    @ApiModelProperty("前三年场地需求")
    private String siteDemandThreeYears;

    /**
     * 第一年预计产值
     */
    @ApiModelProperty("第一年预计产值")
    private String outputValueFirstYear;

    /**
     * 前三年预计产值
     */
    @ApiModelProperty("前三年预计产值")
    private String outputValueThreeYears;

    // 团队描述字段（平铺）
    @ApiModelProperty("团队")
    private String team;

    @ApiModelProperty("技术")
    private String technology;

    @ApiModelProperty("产品")
    private String product;

    @ApiModelProperty("市场")
    private String market;

    @ApiModelProperty("融资")
    private String financing;

    @ApiModelProperty("所获荣誉")
    private String honors;

    @ApiModelProperty("审批进度")
    private List<ProcessVO> process;

}


