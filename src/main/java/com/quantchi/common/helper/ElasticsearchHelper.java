package com.quantchi.common.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldSort;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.search.HighlightField;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.SourceFilter;
import co.elastic.clients.elasticsearch.indices.*;
import co.elastic.clients.elasticsearch.indices.get_mapping.IndexMappingRecord;
import com.quantchi.common.model.ActualFieldAndType;
import com.quantchi.common.model.EsPageResult;
import com.quantchi.common.model.KeywordSearchProperty;
import com.quantchi.common.utils.ElasticsearchBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RequestOptions;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.StringReader;
import java.lang.reflect.Type;
import java.util.*;
import java.util.Collections;
import java.util.stream.Collectors;

import static com.quantchi.common.utils.ElasticsearchBuilder.*;

/**
 * es的帮助类，整体参考
 */
@Component
@Slf4j
public class ElasticsearchHelper implements InitializingBean {

    private static final double MIN_SCORE = 0.0001f;

    @Resource
    private ElasticsearchClient elasticsearchClient;

    public static Map<String, TypeMapping> mappings = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        // 初始化操作 - 使用 Elasticsearch 8 API
        try {
            // 获取索引映射
            final GetMappingRequest request = new GetMappingRequest.Builder().build();
            final GetMappingResponse response = elasticsearchClient.indices().getMapping(request);
            
            // 获取别名信息
            // 在 ES8 中使用 GetAliasRequest 获取别名信息
            GetAliasRequest aliasRequest =
                new co.elastic.clients.elasticsearch.indices.GetAliasRequest.Builder().build();
            GetAliasResponse aliasResponse =
                elasticsearchClient.indices().getAlias(aliasRequest);

            // 获取所有索引的映射
            Map<String, IndexMappingRecord> result = elasticsearchClient.indices().getMapping().result();

            Map<String, IndexMappingRecord> allMappings = result;
            for (Map.Entry<String, IndexMappingRecord> entry : allMappings.entrySet()) {
                mappings.put(entry.getKey(), entry.getValue().mappings());
            }

            log.info("初始化 Elasticsearch 客户端完成");
        } catch (Exception e) {
            log.error("初始化 Elasticsearch 客户端失败", e);
        }
    }

    /**
     * 手动刷新ES索引
     * @param indexName 索引名称
     * @return 是否刷新成功
     */
    public boolean refreshIndex(String indexName) {
        try {
            // 创建刷新请求 - 使用 ES8 API
            RefreshRequest request = new RefreshRequest.Builder()
                    .index(indexName)
                    .build();

            // 执行刷新操作
            RefreshResponse refreshResponse = elasticsearchClient.indices().refresh(request);

            // 获取刷新操作的分片结果
            int totalShards = refreshResponse.shards().total().intValue();
            int successfulShards = refreshResponse.shards().successful().intValue();
            int failedShards = refreshResponse.shards().failed().intValue();

            log.info("索引[{}]刷新完成，总分片数：{}，成功分片数：{}，失败分片数：{}",
                    indexName, totalShards, successfulShards, failedShards);

            return failedShards == 0;
        } catch (Exception e) {
            log.error("刷新索引[{}]失败", indexName, e);
            return false;
        }
    }
    
    /**
     * 获取一条数据 (ES8 API)
     * 使用 SearchRequest 获取单个文档，保持与原有代码的兼容性
     *
     * @param index 索引
     * @param id    id
     * @return 响应
     */
    @SuppressWarnings("unchecked")
    public SearchResponse<Map<String, Object>> get(final String index, final String id) {
        try {
            SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder();
            searchRequestBuilder.index(index);
            searchRequestBuilder.query(q -> q.ids(i -> i.values(id)));
            
            // 使用Map.class作为响应类型，确保类型兼容性
            return (SearchResponse<Map<String, Object>>) (SearchResponse<?>) elasticsearchClient.search(searchRequestBuilder.build(), Map.class);
        } catch (final Exception e) {
            log.error("get index:{},id:{},error:", index, id, e);
            return null;
        }
    }
    
    /**
     * 使用 GetRequest 获取单个文档，比使用 SearchRequest 更高效
     * 新代码应优先使用该方法
     *
     * @param index 索引
     * @param id    id
     * @return 响应
     */
    @SuppressWarnings("unchecked")
    public GetResponse<Map<String, Object>> getById(final String index, final String id) {
        try {
            // 使用 GetRequest 来获取单个文档，这比使用 SearchRequest 更高效
            GetRequest request = new GetRequest.Builder()
                .index(index)
                .id(id)
                .build();
            
            // 使用Map.class作为响应类型，确保类型兼容性
            return (GetResponse<Map<String, Object>>) (GetResponse<?>) elasticsearchClient.get(request, Map.class);
        } catch (final Exception e) {
            log.error("getById index:{},id:{},error:", index, id, e);
            return null;
        }
    }


    /**
     * 新增一条数据不指定id (ES8 API)
     *
     * @param index 索引
     * @param json  json数据
     * @return 响应
     */
    public IndexResponse index(final String index, final String json) {
        //获取到结果
        return index(index, null, json);
    }

    /**
     * 在指定id重新索引一条数据 (ES8 API)
     *
     * @param index 索引
     * @param id    id
     * @param json  json数据
     * @return 响应
     */
    public IndexResponse index(final String index, final String id, final String json) {
        try {
            // 创建 ES8 索引请求
            IndexRequest.Builder<Object> indexRequestBuilder = new IndexRequest.Builder<>()
                    .index(index)
                    .withJson(new StringReader(json));
            
            // 如果有 ID，设置 ID
            if (StringUtils.isNotEmpty(id)) {
                indexRequestBuilder.id(id);
                // 默认行为是更新或创建，不需要设置 create(true)
                // 如果设置 create(true)，当ID已存在时会抛出异常
            }
            
            // 执行索引请求
            return elasticsearchClient.index(indexRequestBuilder.build());
        } catch (final Exception e) {
            log.error("index index:{},id:{},json:{},error:", index, id, json, e);
            return null;
        }
    }
    
    /**
     * 在指定id创建一条数据，如果ID已存在则失败 (ES8 API)
     *
     * @param index 索引
     * @param id    id
     * @param json  json数据
     * @return 响应
     */
    public IndexResponse createIndex(final String index, final String id, final String json) {
        try {
            if (StringUtils.isEmpty(id)) {
                log.error("createIndex requires a non-empty ID");
                return null;
            }
            
            // 创建 ES8 索引请求
            IndexRequest.Builder<Object> indexRequestBuilder = new IndexRequest.Builder<>()
                    .index(index)
                    .id(id)
                    .withJson(new StringReader(json))
                    .opType(co.elastic.clients.elasticsearch._types.OpType.Create); // 设置仅创建模式
            
            // 执行索引请求
            return elasticsearchClient.index(indexRequestBuilder.build());
        } catch (final Exception e) {
            log.error("createIndex index:{},id:{},json:{},error:", index, id, json, e);
            return null;
        }
    }
    
    /**
     * 删除一条数据 (ES8 API)
     *
     * @param index 索引
     * @param id    id
     * @return 响应
     */
    public DeleteResponse delete(final String index, final String id) {
        final DeleteRequest.Builder deleteRequestBuilder = new DeleteRequest.Builder();
        deleteRequestBuilder.index(index);
        deleteRequestBuilder.id(id);
        DeleteResponse response = null;
        try {
            response = elasticsearchClient.delete(deleteRequestBuilder.build());
        } catch (final Exception e) {
            log.error("delete index:{},id:{},error:", index, id, e);
        }
        return response;
    }



    /**
     * 修改index一条数据信息
     *
     * @param index 索引名称
     * @param id    数据的id
     * @param map   被修改的字段和值
     * @return      更新响应
     */
    public UpdateResponse<Map<String, Object>> updateInfo(final String index, final String id, final Map<String, Object> map) throws IOException {
        try {
            // 使用 ES8 API 创建更新请求
            UpdateRequest<Map<String, Object>, Map<String, Object>> updateRequest =
                new UpdateRequest.Builder<Map<String, Object>, Map<String, Object>>()
                    .index(index)
                    .id(id)
                    .doc(map)
                    .build();
            
            // 执行更新操作
            return elasticsearchClient.update(updateRequest, Map.class);
        } catch (final Exception e) {
            log.error("update index:{},id:{},map:{},error:", index, id, map, e);
            throw e;
        }
    }


    /**
     * 设置分页
     *
     * @param searchRequest
     * @param from          起始页数
     * @param size          每页显示数量
     */
    public static void pagingSet(final SearchRequest.Builder searchRequest, Integer from, Integer size) {
        from = from == null || from <= 0 ? 0 : from - 1;
        size = size == null || size < 0 ? 5 : size;
        //分页数量不能超过每页20,防止恶意爬取数据
        //size = size > 20 ? 20 : size;
        searchRequest.from(from * size);
        searchRequest.size(size);
    }

    /**
     * 设置排序
     *
     * @param searchRequest 搜索请求构建器
     * @param sort          排序字段:排序方式 eg: publishDate:desc
     */
    public void addSort(final SearchRequest.Builder searchRequest, final String sort) {
        if (searchRequest == null) {
            log.warn("搜索请求为空，无法添加排序");
            return;
        }
        
        if (StringUtils.isBlank(sort)) {
            // 如果排序字符串为空，不进行任何操作
            return;
        }
        
        try {
            final String[] sortSplit = sort.split(":");
            if (sortSplit.length == 2) {
                final String field = sortSplit[0].trim();
                final String order = sortSplit[1].toLowerCase().trim();
                
                if (StringUtils.isBlank(field)) {
                    log.warn("排序字段为空，跳过排序设置");
                    return;
                }
                
                final FieldSort.Builder fieldSortBuilder = new FieldSort.Builder().field(field);

                if ("desc".equals(order)) {
                    fieldSortBuilder.order(SortOrder.Desc);
                    // 在 ES8 中，missing 参数需要使用 _last 或 _first 字符串
                    fieldSortBuilder.missing("_last");
                } else if ("asc".equals(order)) {
                    fieldSortBuilder.order(SortOrder.Asc);
                    fieldSortBuilder.missing("_first");
                } else {
                    log.warn("不支持的排序方式: {}，使用默认的降序排序", order);
                    fieldSortBuilder.order(SortOrder.Desc);
                    fieldSortBuilder.missing("_last");
                }

                // 创建 SortOptions 并添加到查询中
                SortOptions sortOption = new SortOptions.Builder().field(fieldSortBuilder.build()).build();
                searchRequest.sort(Collections.singletonList(sortOption));
            } else {
                log.warn("排序格式不正确: {}，应为 'field:order' 格式", sort);
            }
        } catch (Exception e) {
            log.error("添加排序时发生错误: {}", sort, e);
        }
    }
    
    /**
     * 设置排序 - 支持调用方自定义排序选项
     *
     * @param searchRequest 搜索请求构建器
     * @param field 排序字段
     * @param order 排序方式 ("asc" 或 "desc")
     * @param missingValue 缺失值处理 (可选，默认根据排序方式自动设置)
     */
    public void addSort(final SearchRequest.Builder searchRequest, final String field, final String order, final String missingValue) {
        if (searchRequest == null) {
            log.warn("搜索请求为空，无法添加排序");
            return;
        }
        
        if (StringUtils.isBlank(field)) {
            log.warn("排序字段为空，跳过排序设置");
            return;
        }
        
        try {
            final FieldSort.Builder fieldSortBuilder = new FieldSort.Builder().field(field);
            final String orderLower = StringUtils.isBlank(order) ? "desc" : order.toLowerCase().trim();
            
            if ("desc".equals(orderLower)) {
                fieldSortBuilder.order(SortOrder.Desc);
            } else if ("asc".equals(orderLower)) {
                fieldSortBuilder.order(SortOrder.Asc);
            } else {
                log.warn("不支持的排序方式: {}，使用默认的降序排序", order);
                fieldSortBuilder.order(SortOrder.Desc);
            }
            
            // 设置缺失值处理
            if (StringUtils.isNotBlank(missingValue)) {
                fieldSortBuilder.missing(missingValue);
            } else {
                // 默认缺失值处理
                fieldSortBuilder.missing("desc".equals(orderLower) ? "_last" : "_first");
            }
            
            // 创建 SortOptions 并添加到查询中
            SortOptions sortOption = new SortOptions.Builder().field(fieldSortBuilder.build()).build();
            searchRequest.sort(Collections.singletonList(sortOption));
        } catch (Exception e) {
            log.error("添加排序时发生错误: field={}, order={}", field, order, e);
        }
    }


    /**
     * 条件分页查询
     * {
     * "value": 10000,
     * "relation": "GREATER_THAN_OR_EQUAL_TO"
     * }
     * 总数上限有一万限制
     */
    public SearchResponse<Map<String, Object>> pageByFields(final String index, final String[] includedFields, final Query query, final Integer page, final Integer pageSize, final String sort) {
        final TimeInterval timer = new TimeInterval();
        // 初始化查询请求对象
        final SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder();
        searchRequestBuilder.index(index);
        searchRequestBuilder.query(query);
        searchRequestBuilder.trackTotalHits(b -> b.enabled(true));
        pagingSet(searchRequestBuilder, page, pageSize);
        if (ArrayUtil.isNotEmpty(includedFields)) {
            searchRequestBuilder.source(sourceConfig -> {
                final SourceFilter sourceFilter = SourceFilter.of(
                        sourceFilterBuilder -> sourceFilterBuilder
                                .includes(Arrays.asList(includedFields))
                );
                sourceConfig.filter(sourceFilter);
                return sourceConfig;
            });
        }
        if (StringUtils.isNotEmpty(sort)) {
            addSort(searchRequestBuilder, sort);
        }
        SearchResponse<Map<String, Object>> searchResponse = null;
        try {
            log.info("分页查询参数:{} {}", index, searchRequestBuilder);
            searchResponse = elasticsearchClient.search(searchRequestBuilder.build(), (Type) Map.class);
            log.info("查询{}耗时{}", index, timer.interval());
        } catch (final Exception e) {
            log.error("listByField, index:{},searchBuilder:{},error:", index, searchRequestBuilder, e);
        }
        return searchResponse;
    }

    /**
     * 分页查询
     */
    public SearchResponse<Map<String, Object>> pageByFields(final SearchRequest.Builder searchRequestBuilder, final String index) {
        final TimeInterval timeInterval = new TimeInterval();
        timeInterval.start("query");
        SearchResponse<Map<String, Object>> searchResponse = null;
        try {
            searchRequestBuilder.index(index);
            searchResponse = elasticsearchClient.search(searchRequestBuilder.build(), (Type) Map.class);
        } catch (final Exception e) {
            log.error("listByField, index:{},searchBuilder:{},error:", index, searchRequestBuilder, e);
        }
        log.info("pageByFields index:{} param:{} cost:{}", index, searchRequestBuilder,
                timeInterval.interval("query"));
        return searchResponse;
    }

    /**
     * 分页查询
     */
    public SearchResponse pageByFields(final SearchRequest.Builder searchRequestBuilder, final String index, final String keyword) {

        final TimeInterval timeInterval = new TimeInterval();
        timeInterval.start("query");
        SearchResponse<Map<String, Object>> searchResponse = null;
        try {
            searchRequestBuilder.index(index);
            if (StringUtils.isNotBlank(keyword)) {
                searchRequestBuilder.minScore(MIN_SCORE);
            }
            searchResponse = elasticsearchClient.search(searchRequestBuilder.build(), (Type) Map.class);
        } catch (final Exception e) {
            log.error("listByField, index:{},searchBuilder:{},error:", index, searchRequestBuilder, e);
        }
        log.info("pageByFields index:{} param:{} cost:{}", index, searchRequestBuilder,
                timeInterval.interval("query"));
        return searchResponse;
    }


    /**
     * 从原始的SearchResponse结果中构建需要分页结果，并高亮结果
     */
    public EsPageResult buildPageResultWithHighlight(final SearchResponse searchResponse,
                                                     final List<KeywordSearchProperty> keywordSearchList,
                                                     final String keyword,
                                                     final Boolean isHighlight,
                                                     final String esIndex) {
        final EsPageResult pageResult = new EsPageResult();
        if (searchResponse == null) {
            return pageResult;
        }
        try {
            List<Hit> hits = searchResponse.hits().hits();
            final List<Map<String, Object>> list = new ArrayList<>(hits.size());
            final List<String> alreadyHighlightFields = new ArrayList<>();
            for (final Hit<Map<String, Object>> hit : hits) {
                final Map<String, Object> sourceAsMap = hit.source();
                // 设置原本的名字
                final Object technology = sourceAsMap.get("technology");
                if (technology instanceof String) {
                    sourceAsMap.put("originalTechnology", technology);
                }
                final Object name = sourceAsMap.get("name");
                if (name instanceof String) {
                    sourceAsMap.put("originalName", name);
                }
                final Object code = sourceAsMap.get("code");
                if (code instanceof String) {
                    sourceAsMap.put("originalCode", code);
                }
                // 设置es算分
                if (hit.score() > 0.0f) {
                    sourceAsMap.put(ES_SCORE, hit.score());
                }
                Map<String, List<String>> highlightMap = hit.highlight();
                // 不需要高亮
                if (!isHighlight) {
                    list.add(sourceAsMap);
                    continue;
                }

                highlightMap.forEach((key, value) -> {
                    alreadyHighlightFields.add(key);
                    // 判断这个key字段是否为对象内的字段，是的话不能直接put，需要获取对应的字段
                    if (key.contains(".")) {
                        final String[] split = key.split("\\.");
                        final String objectKey = split[0];
                        final String actualKey = split[1];
                        final Object object = sourceAsMap.get(objectKey);
                        if (object instanceof Map) {
                            final Map objectMap = (Map) object;
                            final String fieldValue = (String) objectMap.get(actualKey);
                            objectMap.put(actualKey,
                                    replacementInfo(new StringBuilder(fieldValue), keyword, LABEL_BEFORE, LABEL_REAR));
                        } else if (object instanceof List) {
                            final List<Map> objectList = (List) object;
                            objectList.forEach(item -> {
                                final String fieldValue = (String) item.get(actualKey);
                                item.put(actualKey,
                                        replacementInfo(new StringBuilder(fieldValue), keyword, LABEL_BEFORE, LABEL_REAR));
                            });
                        }
                        sourceAsMap.put(objectKey, object);
                    } else {
                        final Object valueOfKey = sourceAsMap.get(key);
                        if (valueOfKey instanceof String) {
                            // 判断值类型，如果是字符串，直接替换
                            sourceAsMap.put(key, CollUtil.join(value, ","));
                        } else if (valueOfKey instanceof List) {
                            final List valueOfKeyList = (List) valueOfKey;
                            final List<String> targetValueList = new ArrayList<>();
                            valueOfKeyList.forEach(item -> {
                                if (item instanceof String) {
                                    targetValueList.add(ElasticsearchBuilder.replacementInfoForKeywordList(new StringBuilder((String) item), Collections.singletonList(keyword), LABEL_BEFORE, LABEL_REAR));
                                }
                            });
                            sourceAsMap.put(key, targetValueList);
                        }

                    }
                });

                // 获取原本需要高亮的字段列表，手动进行高亮
                if (StrUtil.isNotBlank(keyword)) {
                    final Set<String> allHighlightFields = keywordSearchList.stream()
                            .filter(KeywordSearchProperty::getIsHighlight)
                            .map(KeywordSearchProperty::getField)
                            .collect(Collectors.toSet());
                    allHighlightFields.forEach(field -> {
                        final ActualFieldAndType actualFieldAndType = getFieldTypeByMapping(esIndex, field);
                        if (alreadyHighlightFields.contains(field)) {
                            return;
                        }
                        final Object fieldObject = sourceAsMap.get(actualFieldAndType.getOuterField());
                        if (fieldObject instanceof String) {
                            sourceAsMap.put(field,
                                    replacementInfo(new StringBuilder((String) fieldObject), keyword, LABEL_BEFORE, LABEL_REAR));
                        } else if (fieldObject instanceof List) {
                            final List<String> fieldValueList = new ArrayList<>();
                            ((List) fieldObject).forEach(item -> {
                                if (item instanceof String) {
                                    fieldValueList.add(replacementInfo(new StringBuilder((String) item), keyword, LABEL_BEFORE, LABEL_REAR));
                                } else if (item instanceof Map) {
                                    final Map valueMap = (Map) item;
                                    final String innerField = actualFieldAndType.getInnerField();
                                    final Object innerFieldValue = (valueMap).get(innerField);
                                    valueMap.put(innerField, replacementInfo(new StringBuilder((String) innerFieldValue), keyword, "<font color=\"red\">", "</font>"
                                    ));
                                }
                            });
                            if (CollUtil.isNotEmpty(fieldValueList)) {
                                sourceAsMap.put(field, fieldValueList);
                            }
                        } else if (fieldObject instanceof Map) {
                            final Map valueMap = (Map) fieldObject;
                            final String innerField = actualFieldAndType.getInnerField();
                            final Object innerFieldValue = (valueMap).get(innerField);
                            valueMap.put(innerField, replacementInfo(new StringBuilder((String) innerFieldValue), keyword, "<font color=\"red\">", "</font>"
                            ));
                        }
                    });
                }

                // 技术的code不高亮，新增一个高亮字段
                if (sourceAsMap.containsKey("code")) {
                    sourceAsMap.put("highlightCode", sourceAsMap.get("code"));
                    sourceAsMap.put("code", sourceAsMap.get("originalCode"));
                }

                list.add(sourceAsMap);
            }
            pageResult.setList(list);
            pageResult.setTotal(searchResponse.hits().total().value());
            return pageResult;
        } catch (final Exception e) {
            log.error("构建分页查询结果失败", e);
            return pageResult;
        }
    }

    /**
     * 在某字符前后添加字段
     *
     * @param stringBuilder：原字符串
     * @param keyword：字符
     * @param before：在字符前需要插入的字段
     * @param rear：在字符后需要插入的字段
     * @return
     */
    public static String replacementInfo(final StringBuilder stringBuilder, final String keyword, final String before, final String rear) {
        if (CharSequenceUtil.isBlank(stringBuilder)) {
            return stringBuilder.toString();
        }
        //字符第一次出现的位置
        int index = stringBuilder.indexOf(keyword);
        while (index != -1) {
            stringBuilder.insert(index, before);
            stringBuilder.insert(index + before.length() + keyword.length(), rear);
            //下一次出现的位置，
            index = stringBuilder.indexOf(keyword, index + before.length() + keyword.length() + rear.length() - 1);
        }
        return stringBuilder.toString();
    }

    /**
     * 设置多个排序
     *
     * @param sort 排序字段:排序方式 eg: publishDate:desc;year:asc
     */
    public static void addMultiSort(final SearchRequest.Builder searchRequestBuilder, final String sort) {
        if (StrUtil.isBlank(sort)) {
            return;
        }
        final String[] sortSplit = sort.split(";");
        if (sortSplit.length < 1) {
            return;
        }
        
        // 创建一个排序选项列表，用于存储所有排序条件
        List<SortOptions> sortOptionsList = new ArrayList<>();
        
        for (final String sortString : sortSplit) {
            if (StrUtil.isBlank(sortString)) {
                continue;
            }
            final String[] split = sortString.split(":");
            if (split.length != 2) {
                continue;
            }
            final String field = split[0];
            final String order = split[1].toLowerCase().trim();
            final FieldSort.Builder fieldSortBuilder = new FieldSort.Builder().field(field);

            if (order.contains("desc")) {
                fieldSortBuilder.order(SortOrder.Desc);
                if (!Objects.equals("_score", field)) {
                    fieldSortBuilder.missing("_last");
                }
            } else if (order.contains("asc")) {
                fieldSortBuilder.order(SortOrder.Asc);
                // 修正：升序排序时应该使用 _first 而不是 _last
                fieldSortBuilder.missing("_first");
            }

            // 创建 SortOptions 并添加到列表中
            SortOptions sortOption = new SortOptions.Builder().field(fieldSortBuilder.build()).build();
            sortOptionsList.add(sortOption);
        }
        
        // 将所有排序选项添加到查询中
        if (!sortOptionsList.isEmpty()) {
            searchRequestBuilder.sort(sortOptionsList);
        }
    }

    /**
     * 总数统计
     *
     * @param indexName
     * @param queryBuilder
     * @return
     */
    public long countRequest(final String indexName, final BoolQuery.Builder queryBuilder) {
        final TimeInterval timeInterval = new TimeInterval();
        try {
            final CountRequest.Builder countRequestBuilder = new CountRequest.Builder();
            countRequestBuilder.index(indexName);
            countRequestBuilder.query(q -> q.bool(queryBuilder.build()));
            final CountResponse countResponse = elasticsearchClient.count(countRequestBuilder.build());
            log.info("countRequest:{} param:{}, cost{}", indexName, queryBuilder, timeInterval.interval());
            return countResponse.count();
        } catch (final Exception e) {
            log.error("ES查询失败", e);
        }
        return 0;
    }

    /**
     * 根据id获取es数据
     *
     * @param index
     * @param id
     * @return
     */
    public Map<String, Object> getDataById(final String index, final String id, final String[] includes, final String[] excludes) {
        try {
            final GetRequest.Builder getRequestBuilder = new GetRequest.Builder();
            getRequestBuilder.index(index);
            getRequestBuilder.id(id);

            if (!ArrayUtil.isEmpty(includes) || !ArrayUtil.isEmpty(excludes)) {
                if (!ArrayUtil.isEmpty(includes)) {
                    getRequestBuilder.sourceIncludes(Arrays.asList(includes));
                }
                if (!ArrayUtil.isEmpty(excludes)) {
                    getRequestBuilder.sourceExcludes(Arrays.asList(excludes));
                }
            }
            
            final GetResponse<Map> response = elasticsearchClient.get(getRequestBuilder.build(), Map.class);
            
            if (!response.found()) {
                return new HashMap<>(0);
            }
            
            final Map<String, Object> sourceMap = response.source();
            if (sourceMap != null) {
                sourceMap.entrySet().removeIf(entry -> entry.getValue() == null);
                return sourceMap;
            }
            return new HashMap<>(0);
        } catch (final IOException e) {
            log.error("get index:{},id:{},error:", index, id, e);
            return new HashMap<>(0);
        }
    }

    /**
     * 根据id获取es数据
     */
    public Map<String, Object> getDataByBoolQuery(final String index, final Query query, final String[] includes, final String[] excludes) {
        try {
            final SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder();
            searchRequestBuilder.index(index);
            searchRequestBuilder.query(query);

            // 合并 includes 和 excludes 配置
            if (!ArrayUtil.isEmpty(includes) || !ArrayUtil.isEmpty(excludes)) {
                searchRequestBuilder.source(sourceConfig -> {
                    SourceFilter.Builder sourceFilterBuilder = new SourceFilter.Builder();
                    
                    // 添加 includes 如果不为空
                    if (!ArrayUtil.isEmpty(includes)) {
                        sourceFilterBuilder.includes(Arrays.asList(includes));
                    }
                    
                    // 添加 excludes 如果不为空
                    if (!ArrayUtil.isEmpty(excludes)) {
                        sourceFilterBuilder.excludes(Arrays.asList(excludes));
                    }
                    
                    sourceConfig.filter(sourceFilterBuilder.build());
                    return sourceConfig;
                });
            }
            
            final SearchResponse<Map> response = elasticsearchClient.search(searchRequestBuilder.build(), Map.class);
            
            final List<Hit<Map>> hits = response.hits().hits();
            if (hits == null || hits.isEmpty()) {
                return new HashMap<>(0);
            }
            
            final Map<String, Object> sourceMap = hits.get(0).source();
            if (sourceMap != null) {
                sourceMap.entrySet().removeIf(entry -> entry.getValue() == null);
                return sourceMap;
            }
            return new HashMap<>(0);
        } catch (final IOException e) {
            log.error("get index:{},queryBuilder:{},error:", index, query, e);
            return new HashMap<>(0);
        }
    }

    public List<Map<String, Object>> getSourceMapList(final String[] includeFields, final BoolQuery.Builder boolQuery, final int pageNum, final int pageSize, final String index, final String sort) {
        final SearchRequest.Builder searchRequestBuilder = new SearchRequest.Builder();
        searchRequestBuilder.index(index);
        searchRequestBuilder.query(q -> q.bool(boolQuery.build()));
        searchRequestBuilder.trackTotalHits(b -> b.enabled(true));
        searchRequestBuilder.trackScores(true);

        if (includeFields != null && includeFields.length > 0) {
            searchRequestBuilder.source(sourceConfig -> {
                final SourceFilter sourceFilter = SourceFilter.of(
                        sourceFilterBuilder -> sourceFilterBuilder
                                .includes(Arrays.asList(includeFields))
                );
                sourceConfig.filter(sourceFilter);
                return sourceConfig;
            });
        }
        
        pagingSet(searchRequestBuilder, pageNum, pageSize);
        addSort(searchRequestBuilder, sort);
        
        // 获取结果
        final SearchResponse<Map<String, Object>> searchResponse = pageByFields(searchRequestBuilder, index);
        
        // 获取结果，格式化数据
        final List<Hit<Map<String, Object>>> hits = searchResponse.hits().hits();
        final List<Map<String, Object>> resultList = new ArrayList<>();
        
        for (final Hit<Map<String, Object>> hit : hits) {
            if (hit.source() != null) {
                resultList.add(hit.source());
            }
        }
        
        return resultList;
    }
    
    /**
     * 获取索引的映射信息
     *
     * @return 索引名称到映射元数据的映射
     */
    public static Map<String, TypeMapping> getMappings() {
        return mappings;
    }
}
