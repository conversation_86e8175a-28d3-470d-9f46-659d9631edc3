package com.quantchi.application.model.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum ProcessStatus {
    DISAGREE(0, "已驳回"),
    AGREE(1, "已同意");

    private final Integer code;
    private final String info;

    ProcessStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static String getInfoByCode(Integer code) {
        for (ProcessStatus status : ProcessStatus.values()) {
            if (status.getCode().equals(code)) {
                return status.getInfo();
            }
        }
        return "";
    }
}
