package com.quantchi.application.service;

import com.quantchi.application.model.bo.AuditProcessBO;
import com.quantchi.application.model.bo.AuditQueryBO;
import com.quantchi.application.model.bo.InitiateTransferBO;
import com.quantchi.application.model.vo.AuditOptionalListVO;
import com.quantchi.application.model.vo.AuditPageVO;
import com.quantchi.common.domain.CustomIndexNavSetting;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IAuditService {

    /**
     * 获取下拉框选项
     * @return 下拉框选项
     */
    List<CustomIndexNavSetting> optionalList();
    
    /**
     * 分页查询申请列表
     * @param queryBO 查询参数
     * @return 分页结果
     */
    AuditPageVO pageAuditRecords(AuditQueryBO queryBO);
    
    /**
     * 处理审批申请
     *
     * @param auditProcessBO 审批处理参数
     * @return 是否处理成功
     */
    boolean processAudit(AuditProcessBO auditProcessBO);

    /**
     * 导出审批记录
     *
     * @param queryBO 查询参数
     * @param response HTTP响应
     * @throws Exception 导出异常
     */
    void exportAuditRecords(AuditQueryBO queryBO, HttpServletResponse response) throws Exception;

    /**
     * 发起流转
     *
     * @param initiateTransferBO 发起流转参数
     * @return 是否流转成功
     */
    boolean initiateTransfer(InitiateTransferBO initiateTransferBO);
}
