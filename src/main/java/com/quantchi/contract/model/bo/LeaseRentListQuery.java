package com.quantchi.contract.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("租金明细列表查询参数")
public class LeaseRentListQuery {
    
    @ApiModelProperty("承租方名称/房号，模糊搜索")
    private String keyword;

    @ApiModelProperty("园区id")
    private String parkId;

    @ApiModelProperty("楼栋id")
    private String buildingId;

    @ApiModelProperty("缴纳状态")
    private Integer payStatus;

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("每页条数")
    private Integer pageSize;

    // 仅供内部统计用，不暴露接口文档
    private Integer yearLimit;
    private Integer quarterLimit;
}
