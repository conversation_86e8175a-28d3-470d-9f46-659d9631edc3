package com.quantchi.application.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 审批记录VO
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@ApiModel(value = "审批记录VO")
public class AuditRecordVO {

    /**
     * 申请ID
     */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /**
     * 申请单位
     */
    @ApiModelProperty("申请单位")
    private String applicantName;

    /**
     * 申请事项
     */
    @ApiModelProperty("申请事项类型：1-入驻；2-迁出；3-装修")
    private Integer applyType;

    /**
     * 申请事项名称
     */
    @ApiModelProperty("申请事项名称")
    private String applyTypeName;


    /** 申请人ID */
    @JsonIgnore
    private String applicantId;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applicant;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contactInfo;

    /**
     * 发起时间
     */
    @ApiModelProperty("发起时间")
    private LocalDate applyTime;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private LocalDate finishTime;

    /**
     * 当前状态
     */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private Integer status;

    /**
     * 当前状态名称
     */
    @ApiModelProperty("当前状态名称")
    private String statusName;
}
