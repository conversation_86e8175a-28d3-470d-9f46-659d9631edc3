package com.quantchi.contract.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.contract.model.bo.EditLeaseRentBO;
import com.quantchi.contract.model.bo.LeaseRentListQuery;
import com.quantchi.contract.model.entity.LeaseRent;
import com.quantchi.contract.model.vo.LeaseRentVO;
import com.quantchi.contract.model.vo.LeaseRentListVO;
import com.quantchi.contract.model.vo.LeaseRentYearVO;
import com.quantchi.contract.model.vo.LeaseRentContractVO;
import com.quantchi.contract.model.vo.LeaseRentDetailVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

public interface ILeaseRentService extends IService<LeaseRent> {
    /**
     * 根据合同id查询租金明细及扩展信息，扩展信息仅展示一次
     */
    LeaseRentDetailVO listByContractId(String contractId);

    /**
     * 根据合同id查询租金明细列表（按年份聚类）
     */
    List<LeaseRentYearVO> listByContractIdGroupByYear(String contractId);

    /**
     * 租金明细多条件分页查询
     */
    IPage<LeaseRentListVO> pageRentByCondition(LeaseRentListQuery query);

    /**
     * 租金明细多条件分页查询，按合同聚类
     */
    IPage<LeaseRentContractVO> pageRentByConditionGroupByContract(LeaseRentListQuery query);

    /**
     * 编辑租金信息
     */
    boolean editLeaseRent(EditLeaseRentBO bo);

    boolean editLeaseRentByCompany(EditLeaseRentBO bo);

    /**
     * 租金明细多条件查询所有数据（不分页）
     */
    List<LeaseRentListVO> listRentByCondition(LeaseRentListQuery query);

    /**
     * 导出租金明细Excel
     */
    void exportRentByCondition(LeaseRentListQuery query, HttpServletResponse response) throws Exception;

    BigDecimal getOriginalAmountByContractId(String contractId);
}