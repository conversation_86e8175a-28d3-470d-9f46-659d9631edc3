package com.quantchi.company.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@ApiModel(value = "股东信息")
public class CompanyShareholderVO {

    @ApiModelProperty("股东名称")
    private String shareholderName;

    @ApiModelProperty("股东类型")
    private String shareholderType;

    @ApiModelProperty("股东企业ID")
    private String shareholderId;

    @ApiModelProperty("持股比例")
    private String stockPercent;

    @ApiModelProperty("认缴金额描述（万元）")
    private String subscribedAmount;

    @ApiModelProperty("认缴时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date subscribedDate;

    @ApiModelProperty("实缴金额描述（万元）")
    private String paidAmount;

    @ApiModelProperty("实缴时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paidDate;

}
