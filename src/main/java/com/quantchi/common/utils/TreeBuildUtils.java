package com.quantchi.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.lang.tree.parser.NodeParser;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 扩展 hutool TreeUtil 封装系统树构建
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TreeBuildUtils extends TreeUtil {

    public static final String LABEL_BEFORE = "<font color=\"#fc4b4b\">";
    public static final String LABEL_REAR = "</font>";

    /**
     * 根据前端定制差异化字段
     */
    public static final TreeNodeConfig DEFAULT_CONFIG = TreeNodeConfig.DEFAULT_CONFIG.setNameKey("name");

    /**
     * 构建树结构，列表的第一个元素必须为root节点
     *
     * @param list
     * @param nodeParser
     * @param <T>
     * @param <K>
     * @return
     */
    public static <T, K> List<Tree<K>> build(final List<T> list, final NodeParser<T, K> nodeParser) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        final K k = ReflectUtils.invokeGetter(list.get(0), "parentId");
        return TreeUtil.build(list, k, DEFAULT_CONFIG, nodeParser);
    }

    public static List<Tree<String>> keywordSearch(final List<Tree<String>> searchList, final String keyWord) {
        if (CollUtil.isEmpty(searchList) || StrUtil.isEmpty(keyWord)) {
            return searchList;
        }
        final List<Tree<String>> resultList = new ArrayList<>();
        for (final Tree<String> entity : searchList) {
            // 判断是否包括keyWord
            final String name = (String) entity.getName();
            final String id = entity.getId();
            if (name.contains(keyWord) || id.contains(keyWord)) {
                final String highlightName = replacementInfo(new StringBuilder(name), keyWord, LABEL_BEFORE, LABEL_REAR);
                entity.setName(highlightName);
                entity.setChildren(keywordSearch(entity.getChildren(), keyWord));
                resultList.add(entity);
            } else {
                // 继续递归
                final List<Tree<String>> subList = keywordSearch(entity.getChildren(), keyWord);
                if (!CollUtil.isEmpty(subList)) {
                    // 把子节点列表加到节点上
                    entity.setChildren(subList);
                    resultList.add(entity);
                }
            }
        }
        return resultList;
    }

    public static String replacementInfo(final StringBuilder stringBuilder, final String keyword, final String before, final String rear) {
        if (CharSequenceUtil.isBlank(stringBuilder)) {
            return stringBuilder.toString();
        }
        //字符第一次出现的位置
        int index = stringBuilder.indexOf(keyword);
        while (index != -1) {
            stringBuilder.insert(index, before);
            stringBuilder.insert(index + before.length() + keyword.length(), rear);
            //下一次出现的位置，
            index = stringBuilder.indexOf(keyword, index + before.length() + keyword.length() + rear.length() - 1);
        }
        return stringBuilder.toString();
    }

    /**
     * 获取节点及其所有子孙节点的ID
     */
    public static Set<String> getChildrenIdOfNodes(final List<Tree<String>> treeList, final List<String> nodeIds) {
        final Set<String> result = new HashSet<>(nodeIds);
        for (final Tree<String> node : treeList) {
            if (nodeIds.contains(node.getId())) {
                result.addAll(getAllChildrenId(node));
            }
            if (CollUtil.isNotEmpty(node.getChildren())) {
                result.addAll(getChildrenIdOfNodes(node.getChildren(), nodeIds));
            }
        }
        return result;
    }

    public static Set<String> getAllChildrenId(final Tree<String> tree) {
        final Set<String> ids = new HashSet<>();
        if (tree == null || tree.getChildren() == null) {
            return ids;
        }
        for (final Tree<String> child : tree.getChildren()) {
            ids.add(child.getId());
            ids.addAll(getAllChildrenId(child)); //递归获取所有子孙节点的ID
        }
        return ids;
    }

}
