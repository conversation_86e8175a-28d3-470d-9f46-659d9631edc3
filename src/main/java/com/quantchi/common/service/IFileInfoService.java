package com.quantchi.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.common.model.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

public interface IFileInfoService extends IService<FileInfo> {

    String[] saveFile(MultipartFile file, String saveSubDir);

    void download(String fileId, boolean inline, HttpServletResponse response);

    FileInfo upload(@NotNull MultipartFile file, String saveSubDir) throws Exception;

    boolean delete(String fileId);
}
