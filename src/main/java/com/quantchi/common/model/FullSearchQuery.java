package com.quantchi.common.model;

import com.quantchi.common.domain.bo.PageBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/18 11:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("全量搜索查询条件")
public class FullSearchQuery extends PageBO {

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("是否需要纠错，默认为false")
    private Boolean isNeedCorrect = false;

}
