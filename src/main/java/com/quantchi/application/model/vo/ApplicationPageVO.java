package com.quantchi.application.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 办事记录分页结果对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@ApiModel(value = "ApplicationPageVO对象", description = "办事记录分页结果对象")
public class ApplicationPageVO {
    
    @ApiModelProperty("总记录数")
    private Long total;
    
    @ApiModelProperty("每页记录数")
    private Integer pageSize;
    
    @ApiModelProperty("当前页码")
    private Integer pageNum;
    
    @ApiModelProperty("总页数")
    private Long pages;
    
    @ApiModelProperty("办事记录列表")
    private List<ApplicationRecordVO> records;
}
