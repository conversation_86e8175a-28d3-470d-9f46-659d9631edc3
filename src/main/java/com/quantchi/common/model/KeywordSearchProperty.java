package com.quantchi.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("关键词搜索配置类")
@Data
public class KeywordSearchProperty {

    @ApiModelProperty("字段名称")
    private String field;

    @ApiModelProperty("字段拼音名称")
    private String pinyinField;

    @ApiModelProperty("字段展示名称")
    private String fieldName;

    @ApiModelProperty("字段类型")
    private String type;

    @ApiModelProperty("是否高亮")
    private Boolean isHighlight;

    @ApiModelProperty("搜索权重")
    private Float boost;

    @ApiModelProperty("列表展示字段")
    private List<String> requiredList;

    @ApiModelProperty("子项目")
    private List<KeywordSearchProperty> keywordSearchList;
}
