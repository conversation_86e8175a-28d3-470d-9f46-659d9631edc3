package com.quantchi.company.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.company.model.entity.DmDivision;
import com.quantchi.company.model.vo.DmDivisionWithCompeteInfoVO;

import java.util.List;

/**
 * <p>
 * 地区代码表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface IDmDivisionService extends IService<DmDivision> {

    /**
     * 获取地区树
     *
     * @return
     */
    List<DmDivisionWithCompeteInfoVO> divisionTree(final Boolean needArea, final String index);

    DmDivisionWithCompeteInfoVO divisionTreeOfGlobal();

    /**
     * 根据code获取地区详细信息
     *
     * @return
     */
    DmDivision getInfoById(final String id);

}
