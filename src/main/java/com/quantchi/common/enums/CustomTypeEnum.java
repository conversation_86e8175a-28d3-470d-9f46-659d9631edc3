package com.quantchi.common.enums;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/9/22 11:06
 */
@ApiModel("筛选项自定义类型")
@AllArgsConstructor
@Getter
public enum CustomTypeEnum {

    NORMAL(0, "精确值搜索"),
    DATE(1, "日期自定义，传入具体的起始日期"),
    AMOUNT(2, "金额自定义，传入下限和上限，单位万"),
    CASCADE(3, "级联搜索"),
    TEXT_BOX(4, "文本框输入");


    private final int type;

    private final String description;
}
