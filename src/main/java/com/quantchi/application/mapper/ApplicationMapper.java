package com.quantchi.application.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.application.model.entity.Application;
import com.quantchi.application.model.vo.AuditRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 申请 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Mapper
public interface ApplicationMapper extends BaseMapper<Application> {

    /**
     * 分页查询审批列表，联表查询申请单位
     *
     * @param page 分页参数
     * @param applyTypes 申请类型列表
     * @param statuses 状态列表
     * @param keyword 关键字
     * @return 审批记录列表
     */
    IPage<AuditRecordVO> pageAuditRecordsWithJoin(
            Page<AuditRecordVO> page,
            @Param("applyTypes") List<Integer> applyTypes,
            @Param("statuses") List<Integer> statuses,
            @Param("keyword") String keyword,
            @Param("approver") Long approver);

    /**
     * 查询审批记录列表（不分页，用于导出）
     * @param applyTypes 申请类型列表
     * @param statuses 状态列表
     * @param keyword 关键字
     * @param approver 审批人ID
     * @return 审批记录列表
     */
    List<AuditRecordVO> listAuditRecordsWithJoin(
            @Param("applyTypes") List<Integer> applyTypes,
            @Param("statuses") List<Integer> statuses,
            @Param("keyword") String keyword,
            @Param("approver") Long approver);
}
