package com.quantchi.contract.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("按合同聚类的租金明细")
public class LeaseRentContractVO {
    @ApiModelProperty("合同id")
    private String contractId;
    @ApiModelProperty("合同编号")
    private String contractNo;
    @ApiModelProperty("租金明细列表")
    private List<LeaseRentListVO> rents;
}