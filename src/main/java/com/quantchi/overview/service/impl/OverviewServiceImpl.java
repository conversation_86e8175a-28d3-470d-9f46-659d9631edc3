package com.quantchi.overview.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.contract.model.vo.ContractZoneItemVO;
import com.quantchi.overview.model.vo.OverviewVO;
import com.quantchi.overview.model.vo.NewCompanyPageVO;
import com.quantchi.overview.model.vo.NewCompanyVO;
import com.quantchi.overview.model.vo.ExpiringContractPageVO;
import com.quantchi.overview.model.vo.ExpiringContractVO;
import com.quantchi.overview.model.vo.MoveInTrendVO;
import com.quantchi.overview.model.bo.NewCompanyQueryBO;
import com.quantchi.overview.model.bo.ExpiringContractQueryBO;
import com.quantchi.overview.service.IOverviewService;
import com.quantchi.common.enums.EsIndexEnum;
import com.quantchi.common.helper.ElasticsearchHelper;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import com.quantchi.contract.mapper.LeaseContractMapper;
import com.quantchi.contract.mapper.LeaseRentMapper;
import com.quantchi.overview.mapper.OverviewMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.zone.model.entity.Building;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.Zone;
import com.quantchi.zone.service.IBuildingService;
import com.quantchi.zone.service.IParkService;
import com.quantchi.zone.service.IZoneService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.quantchi.common.domain.InMemoryCache;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Service
public class OverviewServiceImpl implements IOverviewService {

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private LeaseContractMapper leaseContractMapper;

    @Autowired
    private LeaseRentMapper leaseRentMapper;

    @Autowired
    private OverviewMapper overviewMapper;

    @Autowired
    private IParkService parkService;

    @Autowired
    private IBuildingService buildingService;

    @Autowired
    private IZoneService zoneService;

    /**
     * 获取园区概况数据
     * 
     * @param parkId 园区ID，可为空
     * @return 园区概况数据
     */
    @Override
    public OverviewVO getOverviewData(final String parkId) {
        final OverviewVO vo = new OverviewVO();

        // 获取园区名称（如果parkId不为空）
        final String parkName = StringUtils.isNotBlank(parkId) ? 
                InMemoryCache.getParkingMap().get(parkId) : null;

        // 1. 企业相关统计（ES查询）
        final String companyIndex = EsIndexEnum.COMPANY.getEsIndex();
        
        final BoolQuery.Builder companyQuery = new BoolQuery.Builder();
        // 如果parkName不为空，添加园区名称过滤条件
        if (StringUtils.isNotBlank(parkName)) {
            companyQuery.filter(f -> f.term(t -> t.field("park").value(parkName)));
        }
        companyQuery.must(m -> m.exists(e -> e.field("registration")));
        final long totalCompanyCount = elasticsearchHelper.countRequest(companyIndex, companyQuery);
        vo.setCompanyTotal(totalCompanyCount);
        
        final BoolQuery.Builder entityQuery = new BoolQuery.Builder();
        entityQuery.filter(f -> f.term(t -> t.field("registration").value("实体注册")));
        // 如果parkName不为空，添加园区名称过滤条件
        if (StringUtils.isNotBlank(parkName)) {
            entityQuery.filter(f -> f.term(t -> t.field("park").value(parkName)));
        }
        final long entityCompanyCount = elasticsearchHelper.countRequest(companyIndex, entityQuery);
        vo.setEntityCompanyCount(entityCompanyCount);
        
        final BoolQuery.Builder virtualQuery = new BoolQuery.Builder();
        virtualQuery.filter(f -> f.term(t -> t.field("registration").value("虚拟注册")));
        // 如果parkName不为空，添加园区名称过滤条件
        if (StringUtils.isNotBlank(parkName)) {
            virtualQuery.filter(f -> f.term(t -> t.field("park").value(parkName)));
        }
        final long virtualCompanyCount = elasticsearchHelper.countRequest(companyIndex, virtualQuery);
        vo.setVirtualCompanyCount(virtualCompanyCount);

        // 2. 合同即将到期企业数（MySQL）
        final LocalDate endDate = LocalDate.now().plusDays(30);
        final long expiringCompanies = StringUtils.isNotBlank(parkId) ? 
                leaseContractMapper.countExpiringCompaniesByPark(endDate, parkId) : 
                leaseContractMapper.countExpiringCompanies(endDate);
        vo.setContractExpiringCompanyCount(expiringCompanies);

        // 3. 租金统计（MySQL）
        if (StringUtils.isNotBlank(parkId)) {
            vo.setTotalReceivableRent(leaseRentMapper.sumReceivableByPark(parkId).divide(new BigDecimal("10000")));
            vo.setTotalReceivedRent(leaseRentMapper.sumReceivedByPark(parkId).divide(new BigDecimal("10000")));
            vo.setTotalUnreceivedRent(leaseRentMapper.sumUnreceivedByPark(parkId).divide(new BigDecimal("10000")));
            vo.setExpiringRent(leaseRentMapper.sumExpiringByPark(endDate, parkId).divide(new BigDecimal("10000")));
        } else {
            vo.setTotalReceivableRent(leaseRentMapper.sumReceivable().divide(new BigDecimal("10000")));
            vo.setTotalReceivedRent(leaseRentMapper.sumReceived().divide(new BigDecimal("10000")));
            vo.setTotalUnreceivedRent(leaseRentMapper.sumUnreceived().divide(new BigDecimal("10000")));
            vo.setExpiringRent(leaseRentMapper.sumExpiring(endDate).divide(new BigDecimal("10000")));
        }

        return vo;
    }

    /**
     * 分页查询新入驻企业列表
     *
     * @param queryBO 查询参数
     * @return 分页结果
     */
    @Override
    public NewCompanyPageVO pageNewCompanies(final NewCompanyQueryBO queryBO) {
        // 构建分页对象
        final Page<NewCompanyVO> page = new Page<>(queryBO.getPageNum(), queryBO.getPageSize());
        
        // 获取园区ID
        final String parkId = queryBO.getParkId();
        
        // 执行分页查询
        final IPage<NewCompanyVO> resultPage;
        if (StringUtils.isNotBlank(parkId)) {
            resultPage = overviewMapper.pageNewCompaniesByPark(page, parkId);
        } else {
            resultPage = overviewMapper.pageNewCompanies(page);
        }

        for (NewCompanyVO record : resultPage.getRecords()) {
            if (StringUtils.isBlank(record.getRoomNumber())) {
                continue;
            }
            StringBuilder locationInfo = new StringBuilder();
            if (record.getRoomNumber().contains(",")) {
                List<String> zoneIds = Arrays.stream(record.getRoomNumber().split(",")).collect(Collectors.toList());
                List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).in(Zone::getId, zoneIds));
                getLocation(locationInfo, zones);
            } else {
                List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).eq(Zone::getId, record.getRoomNumber()));
                getLocation(locationInfo, zones);
            }
            record.setRoomNumber(locationInfo.toString());
        }


        // 封装结果
        final NewCompanyPageVO pageVO = new NewCompanyPageVO();
        pageVO.setTotal(resultPage.getTotal());
        pageVO.setPageNum(queryBO.getPageNum());
        pageVO.setPageSize(queryBO.getPageSize());
        pageVO.setPages(resultPage.getPages());
        pageVO.setRecords(resultPage.getRecords());
        
        return pageVO;
    }

    private void getLocation(StringBuilder locationInfo, List<Zone> zones) {
        Map<String, List<Zone>> zoneMap = zones.stream().collect(Collectors.groupingBy(Zone::getBuildId));
        zoneMap.forEach((buildId, zoneList) -> {
            Zone temp = zoneList.get(0);
            // parkId -> parkName
            if (StringUtils.isNotBlank(temp.getParkId())) {
                Park park = parkService.getById(temp.getParkId());
                if (park != null) {
                    locationInfo.append(park.getParkName()).append("-");
                }
            }
            if (StringUtils.isNotBlank(temp.getBuildId())) {
                Building building = buildingService.getById(temp.getBuildId());
                if (building != null) {
                    locationInfo.append(building.getBuildingName()).append("-");
                }
            }
            zoneList.forEach(zone -> {
                locationInfo.append(zone.getZoneName()).append("、");
            });
            locationInfo.deleteCharAt(locationInfo.lastIndexOf("、"));
            locationInfo.append("；");
        });
        locationInfo.deleteCharAt(locationInfo.lastIndexOf("；"));
    }

    /**
     * 分页查询合同即将到期企业列表
     *
     * @param queryBO 查询参数
     * @return 分页结果
     */
    @Override
    public ExpiringContractPageVO pageExpiringContracts(final ExpiringContractQueryBO queryBO) {
        // 构建分页对象
        final Page<ExpiringContractVO> page = new Page<>(queryBO.getPageNum(), queryBO.getPageSize());
        
        // 获取园区ID
        final String parkId = queryBO.getParkId();
        
        // 获取当前日期
        final LocalDate currentDate = LocalDate.now();
        
        // 设置即将到期的天数范围（30天内）
        final LocalDate expiringDate = LocalDate.now().plusDays(30);
        
        // 执行分页查询
        final IPage<ExpiringContractVO> resultPage;
        if (StringUtils.isNotBlank(parkId)) {
            resultPage = overviewMapper.pageExpiringContractsByPark(page, currentDate, expiringDate, parkId);
        } else {
            resultPage = overviewMapper.pageExpiringContracts(page, currentDate, expiringDate);
        }

        for (ExpiringContractVO record : resultPage.getRecords()) {
            if (StringUtils.isBlank(record.getRoomNumber())) {
                continue;
            }
            StringBuilder locationInfo = new StringBuilder();
            if (record.getRoomNumber().contains(",")) {
                List<String> zoneIds = Arrays.stream(record.getRoomNumber().split(",")).collect(Collectors.toList());
                List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).in(Zone::getId, zoneIds));
                getLocation(locationInfo, zones);
            } else {
                List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).eq(Zone::getId, record.getRoomNumber()));
                getLocation(locationInfo, zones);
            }
            record.setRoomNumber(locationInfo.toString());
        }
        
        // 封装结果
        final ExpiringContractPageVO pageVO = new ExpiringContractPageVO();
        pageVO.setTotal(resultPage.getTotal());
        pageVO.setPageNum(queryBO.getPageNum());
        pageVO.setPageSize(queryBO.getPageSize());
        pageVO.setPages(resultPage.getPages());
        pageVO.setRecords(resultPage.getRecords());
        
        return pageVO;
    }

    /**
     * 查询当前年份企业入驻增长趋势
     *
     * @param parkId 园区ID，可为空
     * @return 每月入驻企业数量列表
     */
    @Override
    public List<MoveInTrendVO> queryMoveInTrend(final String parkId) {
        // 获取当前年份
        final int currentYear = LocalDate.now().getYear();
        
        // 执行查询
        final List<MoveInTrendVO> trendList;
        if (StringUtils.isNotBlank(parkId)) {
            trendList = overviewMapper.queryMoveInTrendByPark(currentYear, parkId);
        } else {
            trendList = overviewMapper.queryMoveInTrend(currentYear);
        }
        
        // 处理数据，确保1-12月份都有数据（没有入驻企业的月份显示0）
        final Map<String, MoveInTrendVO> monthMap = new HashMap<>(12);
        
        // 初始化12个月的数据
        for (int i = 1; i <= 12; i++) {
            final MoveInTrendVO vo = new MoveInTrendVO();
            vo.setMonth(i + "月");
            vo.setCompanyCount(0);
            monthMap.put(i + "月", vo);
        }
        
        // 填充查询结果
        if (CollectionUtils.isNotEmpty(trendList)) {
            for (final MoveInTrendVO vo : trendList) {
                monthMap.put(vo.getMonth(), vo);
            }
        }
        
        // 将Map转为List并按月份排序
        return monthMap.values().stream()
                .sorted(Comparator.comparingInt(vo -> Integer.parseInt(vo.getMonth().replace("月", ""))))
                .collect(Collectors.toList());
    }
}
