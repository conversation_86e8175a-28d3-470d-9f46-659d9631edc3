package com.quantchi.application.model.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 申请
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application")
@ApiModel(value = "Application对象", description = "申请")
public class Application implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 申请人ID */
    private String applicantId;

    /** 审批人ID */
    @ApiModelProperty("审批人ID")
    private Long approver;

    /** 申请类型 */
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private Integer applyType;

    /** 申请编号 */
    @ApiModelProperty("申请编号")
    private String applyCode;

    /** 申请时间 */
    @ApiModelProperty("申请时间")
    private LocalDate applyTime;

    /** 当前状态 */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private Integer status;

    /** 完成时间 */
    @ApiModelProperty("完成时间")
    private LocalDate finishTime;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createUser;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private String updateUser;

    public static final String ID = "id";

    public static final String APPLICANT_ID = "applicant_id";

    public static final String APPROVER = "approver";

    public static final String APPLY_TYPE = "apply_type";

    public static final String APPLY_CODE = "apply_code";

    public static final String APPLY_TIME = "apply_time";

    public static final String STATUS = "status";

    public static final String FINISH_TIME = "finish_time";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CREATE_USER = "create_user";

    public static final String UPDATE_USER = "update_user";

    public static final Integer MOVE_IN = 1;
    public static final Integer MOVE_OUT = 2;
    public static final Integer DECORATE = 3;

    public static final Integer PROCESS_GRANTED = 1;
    public static final Integer PROCESSING = 2;
    public static final Integer PROCESS_REFUSE = 3;
    public static final Integer PROCESS_UNDO = 4;

}
