package com.quantchi.contract.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 租赁合同
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Data
@ApiModel("租赁合同")
public class LeaseContractBO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("空间id")
    private List<String> zoneIds;

    @ApiModelProperty("承租方id")
    private String lesseeId;

    @ApiModelProperty("承租方名称")
    private String lessee;

    @ApiModelProperty("住所/注册地址")
    private String address;

    @ApiModelProperty("法人代表")
    private String legalPerson;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("通讯地址")
    private String contactAddress;

    @ApiModelProperty("邮政编码")
    private String postalCode;

    @ApiModelProperty("联系电话")
    private String tel;

    @ApiModelProperty("传真")
    private String fax;

    @ApiModelProperty("电子邮件")
    private String email;

    @ApiModelProperty("租赁期限")
    private Integer leaseTerm;

    @ApiModelProperty("租赁功能id")
    private Integer leaseFunctionId;

    @ApiModelProperty("租赁开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate leaseStartTime;

    @ApiModelProperty("租赁结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate leaseEndTime;

    @ApiModelProperty("租赁物交付日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate leaseDeliveryTime;

    @ApiModelProperty("租金标准")
    private Integer rentalStandard;

    @ApiModelProperty("减免后租赁期内合计收取乙方租金")
    private Integer rentalCollect;

    @ApiModelProperty("装修改造期限制id")
    private Integer decorationRestrictionId;

    @ApiModelProperty("装修改造开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate decorateStartTime;

    @ApiModelProperty("装修改造结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate decorateEndTime;

    @ApiModelProperty("水电费押金标准id")
    private Integer utilitiesDepositStandardId;

    @ApiModelProperty("水电费押金")
    private String utilitiesDeposit;

    @ApiModelProperty("物业费收费标准")
    private String propertyManageFeeStandard;

    @ApiModelProperty("物业费押金")
    private String propertyManageFeeDeposit;

    @ApiModelProperty("租赁保证金")
    private String leaseDeposit;

    @ApiModelProperty("首期租金支付时间")
    private Integer firstRentPayTimeId;

    @ApiModelProperty("办理手续时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate procedureHandleTime;

    @ApiModelProperty("办理手续截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate procedureHandleEndTime;

    @ApiModelProperty("房屋用途id")
    private Integer zoneFunctionId;

    @ApiModelProperty("房屋设计标准用电量id")
    private Integer powerConsumptionStandardId;

    @ApiModelProperty("续签水电押金")
    private String renewUtilitiesDeposit;

    @ApiModelProperty("续签物业管理费押金")
    private String renewPropertyManageDeposit;

    @ApiModelProperty("续签租赁保证金")
    private String renewLeaseDeposit;

    @ApiModelProperty("详细描述租赁期内各年度租金标准及租金总额")
    private String remark;

    public static final String ID = "id";

    public static final String LESSEE_ID = "lessee_id";

    public static final String LEASE_TERM = "lease_term";

    public static final String LEASE_FUNCTION_ID = "lease_function_id";

    public static final String LEASE_START_TIME = "lease_start_time";

    public static final String LEASE_END_TIME = "lease_end_time";

    public static final String LEASE_DELIVERY_TIME = "lease_delivery_time";

    public static final String RENTAL_STANDARD = "rental_standard";

    public static final String RENTAL_COLLECT = "rental_collect";

    public static final String DECORATION_RESTRICTION_ID = "decoration_restriction_id";

    public static final String DECORATE_START_TIME = "decorate_start_time";

    public static final String DECORATE_END_TIME = "decorate_end_time";

    public static final String UTILITIES_DEPOSIT_STANDARD_ID = "utilities_deposit_standard_id";

    public static final String UTILITIES_DEPOSIT = "utilities_deposit";

    public static final String PROPERTY_MANAGE_FEE_STANDARD = "property_manage_fee_standard";

    public static final String PROPERTY_MANAGE_FEE_DEPOSIT = "property_manage_fee_deposit";

    public static final String LEASE_DEPOSIT = "lease_deposit";

    public static final String FIRST_RENT_PAY_TIME_ID = "first_rent_pay_time_id";

    public static final String PROCEDURE_HANDLE_TIME = "procedure_handle_time";

    public static final String PROCEDURE_HANDLE_END_TIME = "procedure_handle_end_time";

    public static final String ZONE_FUNCTION_ID = "zone_function_id";

    public static final String POWER_CONSUMPTION_STANDARD_ID = "power_consumption_standard_id";

    public static final String RENEW_UTILITIES_DEPOSIT = "renew_utilities_deposit";

    public static final String RENEW_PROPERTY_MANAGE_DEPOSIT = "renew_property_manage_deposit";

    public static final String RENEW_LEASE_DEPOSIT = "renew_lease_deposit";

    public static final String REMARK = "remark";

    public static final String CREATE_USER = "create_user";

    public static final String UPDATE_USER = "update_user";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CONTRACT_SCAN_COPY_PATH = "contract_scan_copy_path";

    public static final String INVEST_AGREEMENT_PATH = "invest_agreement_path";
}
