package com.quantchi.contract.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 装修改造期限制
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("decorate_time_limit")
@ApiModel(value = "DecorateTimeLimit对象", description = "装修改造期限制")
public class DecorateTimeLimit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String timeLimit;

    public static final String ID = "id";

    public static final String TIME_LIMIT = "time_limit";
}
