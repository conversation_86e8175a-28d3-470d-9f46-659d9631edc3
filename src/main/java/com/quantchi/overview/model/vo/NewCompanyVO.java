package com.quantchi.overview.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 新入驻企业列表展示对象
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@ApiModel(value = "NewCompanyVO", description = "新入驻企业列表展示对象")
public class NewCompanyVO {

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("入驻时间")
    private LocalDate entryTime;

    @ApiModelProperty("合同起止时间")
    private String contractPeriod;

    @ApiModelProperty("入驻房号")
    private String roomNumber;
    
    @ApiModelProperty("合同金额")
    private Integer contractAmount;
}
