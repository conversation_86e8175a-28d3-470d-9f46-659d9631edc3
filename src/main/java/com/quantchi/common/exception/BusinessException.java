package com.quantchi.common.exception;


import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2019/4/22 11:54
 * 异常消息处理
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private String msg;

    private int code = 500;


    public BusinessException() {
    }

    public BusinessException(final String msg) {
        super(msg);
        this.msg = msg;
    }

    public BusinessException(final String msg, final Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    public BusinessException(final int code, final String msg) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public BusinessException(final String msg, final int code, final Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

}
