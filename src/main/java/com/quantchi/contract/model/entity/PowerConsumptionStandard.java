package com.quantchi.contract.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 房屋设计标准用电量
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("power_consumption_standard")
@ApiModel(value = "PowerConsumptionStandard对象", description = "房屋设计标准用电量")
public class PowerConsumptionStandard implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("房屋设计标准用电量")
    private String standard;

    public static final String ID = "id";

    public static final String STANDARD = "standard";
}
