//package com.quantchi;
//
//import com.baomidou.mybatisplus.generator.FastAutoGenerator;
//import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
//public class PrimaryCodeGenerator {
//
//    private static final String MYSQL_URL = "**********************************************************************************************************************************************************************************************************************************************************************";
//    private static final String MYSQL_USER = "nanjing_complex";
//    private static final String MYSQL_PWD = "!&OlTNgvhf";
//    private static final String PARENT_PACKAGE = "com.quantchi";
//    private static final String MODULE_NAME = "contract";
//    private static final String OUT_FILE_PATH = System.getProperty("user.dir") + "/src/main/java";
//
//    public static void main(final String[] args) {
//        List<String> tables = Arrays.asList("lease_contract_zone");
//        FastAutoGenerator.create(MYSQL_URL, MYSQL_USER, MYSQL_PWD)
//                .globalConfig(builder -> {
//                    builder.author("jjw") // 设置作者
//                            .enableSwagger() // 开启 swagger 模式
//                            .outputDir(OUT_FILE_PATH); // 指定输出目录
//                })
//                .packageConfig(builder -> {
//                    builder.parent(PARENT_PACKAGE) // 设置父包名
//                            .moduleName(MODULE_NAME) // 设置父包模块名
//                            .entity("model.entity")
//                            .mapper("mapper");
//                })
//                .strategyConfig(builder ->
//                        builder.addInclude(tables)
//                        .controllerBuilder()
//                        .enableRestStyle()
//                        .entityBuilder()
//                        .enableColumnConstant()
//                        .enableChainModel()
//                        .enableLombok())
//                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
//                .execute();
//    }
//
//}