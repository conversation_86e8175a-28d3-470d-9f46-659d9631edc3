package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_shareholder")
@ApiModel(value = "CompanyShareholder对象", description = "")
public class CompanyShareholder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录主键")
    private String id;

    @ApiModelProperty("企业ID")
    private String cid;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("股东名称")
    private String shareholderName;

    @ApiModelProperty("股东类型")
    private String shareholderType;

    @ApiModelProperty("股东企业ID")
    private String shareholderId;

    @ApiModelProperty("持股比例")
    private BigDecimal stockPercent;

    @ApiModelProperty("认缴金额描述")
    private BigDecimal subscribedAmount;

    @ApiModelProperty("认缴时间")
    private Date subscribedDate;

    @ApiModelProperty("实缴时间")
    private Date paidDate;

    @ApiModelProperty("实缴金额描述")
    private BigDecimal paidAmount;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private Date createTime;

    @ApiModelProperty("数据修改时间")
    private Date updateTime;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String COMPANY_NAME = "company_name";

    public static final String SHAREHOLDER_NAME = "shareholder_name";

    public static final String SHAREHOLDER_TYPE = "shareholder_type";

    public static final String SHAREHOLDER_ID = "shareholder_id";

    public static final String STOCK_PERCENT = "stock_percent";

    public static final String SUBSCRIBED_AMOUNT = "subscribed_amount";

    public static final String SUBSCRIBED_DATE = "subscribed_date";

    public static final String PAID_DATE = "paid_date";

    public static final String PAID_AMOUNT = "paid_amount";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
