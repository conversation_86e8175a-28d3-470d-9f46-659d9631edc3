package com.quantchi.overview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.overview.model.vo.ExpiringContractVO;
import com.quantchi.overview.model.vo.MoveInTrendVO;
import com.quantchi.overview.model.vo.NewCompanyVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 园区总览 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface OverviewMapper extends BaseMapper<Object> {
    
    /**
     * 分页查询新入驻企业列表
     *
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<NewCompanyVO> pageNewCompanies(Page<NewCompanyVO> page);
    
    /**
     * 根据园区ID分页查询新入驻企业列表
     *
     * @param page 分页参数
     * @param parkId 园区ID
     * @return 分页结果
     */
    IPage<NewCompanyVO> pageNewCompaniesByPark(Page<NewCompanyVO> page, @Param("parkId") String parkId);
    
    /**
     * 分页查询合同即将到期企业列表
     *
     * @param page 分页参数
     * @param currentDate 当前日期
     * @param expiringDate 即将到期日期
     * @return 分页结果
     */
    IPage<ExpiringContractVO> pageExpiringContracts(Page<ExpiringContractVO> page, 
            @Param("currentDate") LocalDate currentDate, 
            @Param("expiringDate") LocalDate expiringDate);
    
    /**
     * 根据园区ID分页查询合同即将到期企业列表
     *
     * @param page 分页参数
     * @param currentDate 当前日期
     * @param expiringDate 即将到期日期
     * @param parkId 园区ID
     * @return 分页结果
     */
    IPage<ExpiringContractVO> pageExpiringContractsByPark(Page<ExpiringContractVO> page, 
            @Param("currentDate") LocalDate currentDate, 
            @Param("expiringDate") LocalDate expiringDate,
            @Param("parkId") String parkId);
    
    /**
     * 查询当前年份企业入驻增长趋势（按月统计）
     *
     * @param year 年份
     * @return 每月入驻企业数量列表
     */
    List<MoveInTrendVO> queryMoveInTrend(@Param("year") Integer year);
    
    /**
     * 根据园区ID查询当前年份企业入驻增长趋势（按月统计）
     *
     * @param year 年份
     * @param parkId 园区ID
     * @return 每月入驻企业数量列表
     */
    List<MoveInTrendVO> queryMoveInTrendByPark(@Param("year") Integer year, @Param("parkId") String parkId);
}
