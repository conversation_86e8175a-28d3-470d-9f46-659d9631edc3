package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_stock")
@ApiModel(value = "CompanyStock对象", description = "")
public class CompanyStock implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键,key")
    private String id;

    @ApiModelProperty("关联企业库id")
    private String cid;

    @ApiModelProperty("上市股板")
    private String stockType;

    @ApiModelProperty("交易代码")
    private String stockCode;

    @ApiModelProperty("交易所")
    private String exchange;

    @ApiModelProperty("证券简称")
    private String shortName;

    @ApiModelProperty("证券全称")
    private String name;

    @ApiModelProperty("上市时间")
    private LocalDate ipoDate;

    @ApiModelProperty("ISIN代码")
    private String isin;

    @ApiModelProperty("上市板块")
    private String plate;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String STOCK_TYPE = "stock_type";

    public static final String STOCK_CODE = "stock_code";

    public static final String EXCHANGE = "exchange";

    public static final String SHORT_NAME = "short_name";

    public static final String NAME = "name";

    public static final String IPO_DATE = "ipo_date";

    public static final String ISIN = "isin";

    public static final String PLATE = "plate";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
