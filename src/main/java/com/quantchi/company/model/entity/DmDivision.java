package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 地区代码表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("dm_division")
@ApiModel(value = "DmDivision对象", description = "地区代码表")
public class DmDivision extends BaseTime implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("省市区编码")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("上级编码")
    private String parentId;

    @ApiModelProperty("省市区名称")
    private String name;

    @ApiModelProperty("省市区简称")
    private String shortName;

    @ApiModelProperty("省市区编号")
    private String code;

    @ApiModelProperty("省市区层级位置")
    private Integer level;

    @ApiModelProperty("经度")
    private String lng;

    @ApiModelProperty("维度")
    private String lat;

    @ApiModelProperty("城市群")
    private String cityGroup;

    @ApiModelProperty(value = "子区域")
    @TableField(exist = false)
    private List<DmDivision> children = new ArrayList<>();

    public DmDivision() {
    }

    public DmDivision(String id, String name) {
        this.id = id;
        this.name = name;
    }
}
