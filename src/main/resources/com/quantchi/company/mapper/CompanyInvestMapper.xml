<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.company.mapper.CompanyInvestMapper">
    <select id="getAllInvestByCid" resultType="com.quantchi.company.model.entity.CompanyInvest">
        SELECT
	        cid,
	        company_name,
	        province_code,
	        city_code,
	        area_code,
	        invest_ratio,
	        invested_id,
	        invested_name,
	        invested_status,
	        invested_established_date,
	        invest_province_code,
	        invest_city_code,
			invest_area_code,
		    invested_industry
        FROM
	        company_invest
        WHERE
	        is_valid = 1
	        AND cid = #{id}
	        <if test="provinceId != null and provinceId != ''">
				AND invest_province_code = #{provinceId}
			</if>
			<if test="cityId != null and cityId != ''">
				AND invest_city_code = #{cityId}
			</if>
		<if test="type == 1">
			GROUP BY invested_name
		</if>
	    UNION ALL
        SELECT
	        cid,
	        company_name,
	        province_code,
	        city_code,
	        area_code,
	        invest_ratio,
	        invested_id,
	        invested_name,
	        invested_status,
	        invested_established_date,
	        invest_province_code,
	        invest_city_code,
			invest_area_code,
			invested_industry
        FROM
	        company_invest
        WHERE
	        is_valid = 1
            AND cid IN (
                SELECT invested_id
                FROM company_invest
                WHERE is_valid = 1 AND cid = #{id} AND invested_id IS NOT NULL
            )
            <if test="provinceId != null and provinceId != ''">
				AND invest_province_code = #{provinceId}
			</if>
			<if test="cityId != null and cityId != ''">
				AND invest_city_code = #{cityId}
			</if>
		<if test="type == 1">
			GROUP BY invested_name
		</if>
    </select>

	<select id="getDirectInvestByCid" resultType="com.quantchi.company.model.entity.CompanyInvest">
        SELECT
	        cid,
	        company_name,
	        province_code,
	        city_code,
	        area_code,
	        invest_ratio,
	        invested_id,
	        invested_name,
	        invested_status,
	        invested_established_date,
	        invest_province_code,
	        invest_city_code,
			invest_area_code,
		    invested_industry
        FROM
	        company_invest
        WHERE
	        is_valid = 1
	        AND cid = #{id}
	        <if test="provinceId != null and provinceId != ''">
				AND invest_province_code = #{provinceId}
			</if>
			<if test="cityId != null and cityId != ''">
				AND invest_city_code = #{cityId}
			</if>
		<if test="type == 1">
			ORDER BY invested_name
		</if>
	</select>

    <select id="getAllInvestForIndustryByCid" resultType="com.quantchi.company.model.bo.CompanyFundIndustryInvestBO">
        SELECT a.*, b.nation_industry_2 nationIndustry2, b.nation_industry_3 nationIndustry3
        FROM (
            SELECT
	            cid,
	            company_name companyName,
	            invest_ratio,
	            invested_id investedId,
	            invested_name investedName,
	            DATE_FORMAT(invested_established_date, '%Y-%m-%d') investedEstablishedDate
            FROM
	            company_invest
            WHERE
	            is_valid = 1
	            AND cid = #{id}
	        <if test="type == 1">
				GROUP BY invested_name
			</if>
	        UNION ALL
            SELECT
	            cid,
	            company_name companyName,
	            invest_ratio,
	            invested_id investedId,
	            invested_name investedName,
	            DATE_FORMAT(invested_established_date, '%Y-%m-%d') investedEstablishedDate
	        FROM
	            company_invest
            WHERE
	            is_valid = 1
                AND cid IN (
                    SELECT invested_id
                    FROM company_invest
                    WHERE is_valid = 1 AND cid = #{id} AND invested_id IS NOT NULL
                )
            <if test="type == 1">
				GROUP BY invested_name
			</if>
        )a LEFT JOIN company b ON a.investedId = b.id
    </select>

	<select id="getBigCategoryByCid" resultType="java.lang.String">
		SELECT nation_industry_2
		FROM company
		WHERE is_valid = 1 AND id = #{id}
	</select>

	<select id="getRegistCapiValueListByCidList" resultType="java.util.Map">
		SELECT id, regist_capi_value_cal
		FROM company
		WHERE is_valid = 1
		AND id IN (
			<foreach collection="idList" item="id" separator=",">
				#{id}
			</foreach>
		)
	</select>

	<select id="getMaxCityCodeRecord" resultType="com.quantchi.company.model.vo.NameCountVO">
        SELECT city_code as name, COUNT(*) as count
        FROM company_invest
        WHERE cid = #{companyId}
        GROUP BY city_code
        ORDER BY count DESC
    </select>

	<select id="getMaxIndustryRecord" resultType="com.quantchi.company.model.vo.NameCountVO">
		SELECT invested_industry as name, COUNT(*) as count
		FROM company_invest
		WHERE cid = #{companyId}
		GROUP BY invested_industry
		ORDER BY count DESC
	</select>
</mapper>