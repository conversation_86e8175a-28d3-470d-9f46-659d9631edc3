package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_contact")
@ApiModel(value = "ApplicationContact对象", description = "")
public class ApplicationContact implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 申请ID */
    private String applicationId;

    /** 企业ID */
    @ApiModelProperty("企业ID")
    private String companyId;

    /** 姓名 */
    @ApiModelProperty("姓名")
    private String name;

    /** 性别 */
    @ApiModelProperty("性别")
    private String gender;

    /** 职务 */
    @ApiModelProperty("职务")
    private String position;

    /** 电话 */
    @ApiModelProperty("电话")
    private String telephone;

    /** 手机 */
    @ApiModelProperty("手机")
    private String mobile;

    /** 邮箱 */
    @ApiModelProperty("邮箱")
    private String email;

    /** 传真 */
    @ApiModelProperty("传真")
    private String fax;

    /** 在职状态 */
    @ApiModelProperty("在职状态：0-离职，1-在职")
    private Integer jobStatus;

    @ApiModelProperty("备注")
    private String remark;


    /** 创建时间 */
    private LocalDateTime createTime;

    /** 创建人 */
    private String createUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String COMPANY_ID = "company_id";

    public static final String NAME = "name";

    public static final String GENDER = "gender";

    public static final String POSITION = "position";

    public static final String TELEPHONE = "telephone";

    public static final String MOBILE = "mobile";

    public static final String EMAIL = "email";

    public static final String FAX = "fax";

    public static final String CREATE_TIME = "create_time";

    public static final String CREATE_USER = "create_user";
}
