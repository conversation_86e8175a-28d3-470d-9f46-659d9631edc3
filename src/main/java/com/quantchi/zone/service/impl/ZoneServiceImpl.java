package com.quantchi.zone.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.quantchi.company.model.entity.Company;
import com.quantchi.company.service.impl.CompanyServiceImpl;
import com.quantchi.contract.mapper.LeaseContractZoneMapper;
import com.quantchi.contract.model.entity.LeaseContractZone;
import com.quantchi.contract.service.impl.LeaseContractServiceImpl;
import com.quantchi.zone.model.entity.Building;
import com.quantchi.zone.model.entity.Floor;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.Zone;
import com.quantchi.zone.mapper.ZoneMapper;
import com.quantchi.zone.model.enums.LeaseStatus;
import com.quantchi.zone.model.vo.*;
import com.quantchi.zone.service.IZoneService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.zone.model.bo.ZoneBO;
import com.quantchi.contract.model.entity.LeaseContract;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.*;

import com.quantchi.sys.utils.LoginHelper;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.BeanUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.quantchi.common.utils.DateHandlerUtil;
import org.springframework.transaction.annotation.Transactional;

import static com.quantchi.zone.model.entity.Zone.*;
import static com.quantchi.zone.model.vo.ZoneItemVO.*;

/**
 * <p>
 * 空间实体 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Service
@Slf4j
public class ZoneServiceImpl extends ServiceImpl<ZoneMapper, Zone> implements IZoneService {

    @Autowired
    private CompanyServiceImpl companyService;

    @Autowired
    private LeaseContractServiceImpl leaseContractService;

    @Autowired
    private ParkServiceImpl parkService;

    @Autowired
    private BuildingServiceImpl buildingService;

    @Autowired
    private FloorServiceImpl floorService;

    @Autowired
    private LeaseContractZoneMapper leaseContractZoneMapper;

    // 正则表达式，用于匹配一个或多个数字
    private static final Pattern NUMERIC_PATTERN = Pattern.compile("(\\d+)");

    private static int extractNumericValue(String floorNameStr) {
        if (StringUtils.isBlank(floorNameStr)) {
            // 空或空白的排在最后
            return Integer.MAX_VALUE;
        }

        String normalized = floorNameStr.trim().toLowerCase();
        int multiplier = 1;

        // 检查是否为负数/地下楼层
        if (normalized.startsWith("b") || normalized.startsWith("负") || normalized.startsWith("地下")) {
            multiplier = -1;
            // 尝试移除前缀，以便后续数字提取更干净
            if (normalized.startsWith("b")) {
                normalized = normalized.substring(1);
            } else if (normalized.startsWith("负")) {
                normalized = normalized.substring(1);
            } else if (normalized.startsWith("地下")) {
                normalized = normalized.substring(2);
            }
        }

        Matcher matcher = NUMERIC_PATTERN.matcher(normalized);
        if (matcher.find()) {
            try {
                // matcher.group(1) 获取第一个捕获组，即 (\\d+) 匹配到的内容
                return Integer.parseInt(matcher.group(1)) * multiplier;
            } catch (NumberFormatException e) {
                // 理论上如果 regex 匹配到 \\d+，parseInt 不会失败，但以防万一
                System.err.println("Warning: Could not parse extracted digits '" + matcher.group(1) + "' from: " + floorNameStr);
                return Integer.MAX_VALUE - 1; // 无法解析数字的排在倒数第二
            }
        } else {
            // 如果没有找到数字 (例如 "大堂", "顶层")
            System.err.println("Warning: No numeric part found in floor name: " + floorNameStr);
            return Integer.MAX_VALUE - 2; // 完全没有数字的排在倒数第三 (或根据需要调整)
        }
    }

    @Override
    public List<ZoneListVO> zoneList(String parkId, String buildingId) {

        List<ZoneListVO> resultList = new ArrayList<>();

        // 1. 查询所有floor
        LambdaQueryWrapper<Floor> floorQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(buildingId)) {
            floorQuery.eq(Floor::getBuildingId, buildingId);
        }
        List<Floor> allFloors = floorService.list(floorQuery);

        // 2. floorName排序后逐个组装ZoneListVO
        List<ZoneItemVO> voList = getZoneVOList(parkId, buildingId);
        Map<String, List<ZoneItemVO>> floorIdToZoneList = new HashMap<>();
        if (!voList.isEmpty()) {
            for (ZoneItemVO vo : voList) {
                String floorId = vo.getFloorId();
                floorIdToZoneList.computeIfAbsent(floorId, k -> new ArrayList<>()).add(vo);
            }
        }
        allFloors.stream()
                .filter(f -> StringUtils.isNotBlank(f.getFloorName()))
                .distinct()
                .sorted(Comparator.comparingInt(floor -> extractNumericValue(floor.getFloorName())))
                .forEach(floor -> {
                    ZoneListVO zoneListVO = new ZoneListVO();
                    zoneListVO.setFloorId(floor.getId());
                    zoneListVO.setFloorName(floor.getFloorName());
                    zoneListVO.setZoneItemList(floorIdToZoneList.getOrDefault(floor.getId(), new ArrayList<>()));
                    resultList.add(zoneListVO);
                });
        return resultList;
    }

    /**
     * 查询zone并转为ZoneItemVO列表，包含租赁、承租人等信息
     */
    private List<ZoneItemVO> getZoneVOList(String parkId, String buildingId) {

        List<ZoneItemVO> voList = new ArrayList<>();

        LambdaQueryWrapper<Zone> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Zone::getIsValid, IS_VALID);
        if (StringUtils.isNotBlank(parkId)) {
            queryWrapper.eq(Zone::getParkId, parkId);
        }
        if (StringUtils.isNotBlank(buildingId)) {
            queryWrapper.eq(Zone::getBuildId, buildingId);
        }
        List<Zone> zoneList = this.list(queryWrapper);

        if (zoneList == null || zoneList.isEmpty()) {
            return voList;
        }

        for (Zone zone : zoneList) {

            ZoneItemVO vo = new ZoneItemVO();
            BeanUtils.copyProperties(zone, vo);

            // 查询最新合同
            LeaseContract contract = null;
            List<LeaseContract> contractList = leaseContractService.list(new MPJLambdaWrapper<LeaseContract>()
                    .select(LeaseContract::getLesseeId, LeaseContract::getLeaseStartTime, LeaseContract::getLeaseEndTime)
                    .leftJoin(LeaseContractZone.class, LeaseContractZone::getContractId, LeaseContract::getId)
                    .eq(LeaseContractZone::getZoneId, zone.getId())
                    .orderByDesc(LeaseContract::getLeaseStartTime).last("limit 1"));

            if (CollectionUtils.isEmpty(contractList)) {
                voList.add(vo);
                continue;
            }

            contract = contractList.get(0);

            // lessee
            String lesseeId = contract.getLesseeId();
            vo.setLessee(getLessee(lesseeId));

            LocalDate expireTime = contract.getLeaseEndTime();
            LocalDate leaseTime = contract.getLeaseStartTime();
            vo.setLeaseTime(DateHandlerUtil.toYMD(leaseTime));
            vo.setExpireTime(DateHandlerUtil.toYMD(expireTime));
            if (expireTime != null) {
                long years = Period.between(LocalDate.now(), expireTime).get(ChronoUnit.YEARS);
                long months = Period.between(LocalDate.now(), expireTime).get(ChronoUnit.MONTHS);
                long days = Period.between(LocalDate.now(), expireTime).get(ChronoUnit.DAYS);
                if (years <= 0 && months <= 0) {
                    if (days <= 0) {
                        vo.setStatus(LEASE_STATUS_OVERDUE);
                    } else if (days < 90) {
                        vo.setStatus(LEASE_STATUS_EXPIRING);
                    } else {
                        vo.setStatus(LEASE_STATUS_LEASE);
                    }
                } else {
                    vo.setStatus(LEASE_STATUS_LEASE);
                }
            }

            voList.add(vo);

        }
        return voList;
    }

    private String getLessee(String lesseeId) {
        String lesseeName = null;
        if (StringUtils.isNotBlank(lesseeId)) {
            Company company = companyService.lambdaQuery()
                    .select(Company::getName)
                    .eq(Company::getId, lesseeId)
                    .one();
            if (company != null) {
                lesseeName = company.getName();
            }
        }
        return lesseeName;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editZone(ZoneBO zoneBO) {
        try {
            checkEditParam(zoneBO);

            String zoneId = zoneBO.getZoneId();
            String userName = LoginHelper.getUsername();
            LocalDateTime now = LocalDateTime.now();

            if (StringUtils.isBlank(zoneId)) {
                // 新增逻辑
                Zone zone = new Zone();
                BeanUtils.copyProperties(zoneBO, zone);
                zone.setId(UUID.randomUUID().toString().replace("-", ""));
                zone.setIsValid(IS_VALID);
                zone.setCreateUser(userName);
                zone.setUpdateUser(userName);
                zone.setCreateTime(now);
                zone.setUpdateTime(now);
                zone.setStatus(zoneBO.getStatus());
                this.save(zone);
            } else {
                // 编辑逻辑
                Zone zone = this.getById(zoneId);
                if (zone == null) {
                    throw new IllegalArgumentException("未找到对应空间");
                }
                BeanUtils.copyProperties(zoneBO, zone);
                zone.setUpdateUser(userName);
                zone.setUpdateTime(now);
                zone.setStatus(zoneBO.getStatus());
                this.updateById(zone);
            }

        } catch (Exception e) {
            log.error("error occurred add or edit zone! ", e);
            throw e;
        }
    }

    @SneakyThrows
    private void checkEditParam(ZoneBO zoneBO) {
        List<Zone> list = this.list(Wrappers.lambdaQuery(Zone.class)
                .eq(Zone::getParkId, zoneBO.getParkId())
                .eq(Zone::getBuildId, zoneBO.getBuildId())
                .eq(Zone::getFloorId, zoneBO.getFloorId())
                .eq(Zone::getZoneName, zoneBO.getZoneName())
                .eq(Zone::getIsValid, IS_VALID));

        if (CollectionUtils.isNotEmpty(list)) {
            if (list.size() == 1) {
                Zone existZone = list.get(0);
                // zoneId为空或与查到的zone不一致才算重复
                if (StringUtils.isBlank(zoneBO.getZoneId()) && !existZone.getId().equals(zoneBO.getZoneId())) {
                    throw new IllegalArgumentException("空间名称重复！");
                }
            } else {
                throw new IllegalArgumentException("空间名称重复！");
            }
        }

    }

    @Override
    public ZoneDetailVO zoneDetail(String zoneId) {
        try {
            if (StringUtils.isBlank(zoneId)) {
                throw new IllegalArgumentException("zoneId不能为空");
            }
            Zone zone = this.getById(zoneId);
            if (zone == null) {
                throw new IllegalArgumentException("未找到对应空间");
            }

            ZoneDetailVO vo = new ZoneDetailVO();
            BeanUtils.copyProperties(zone, vo);
            vo.setZoneId(zoneId);

            // 查询园区名称
            if (StringUtils.isNotBlank(zone.getParkId())) {
                Park park = parkService.getById(zone.getParkId());
                if (park != null) {
                    vo.setParkName(park.getParkName());
                }
            }
            // 查询楼栋名称
            if (StringUtils.isNotBlank(zone.getBuildId())) {
                Building building = buildingService.getById(zone.getBuildId());
                if (building != null) {
                    vo.setBuildingName(building.getBuildingName());
                }
            }
            // 查询楼层名称
            if (StringUtils.isNotBlank(zone.getFloorId())) {
                Floor floor = floorService.getById(zone.getFloorId());
                if (floor != null) {
                    vo.setFloor(floor.getFloorName());
                }
            }

            // 查询该空间所有合同，按租赁开始时间倒序
            List<LeaseContract> contractList = leaseContractService.list(new MPJLambdaWrapper<LeaseContract>()
                    .select(LeaseContract::getId, LeaseContract::getContractNo, LeaseContract::getLesseeId, LeaseContract::getLeaseStartTime, LeaseContract::getLeaseEndTime)
                    .leftJoin(LeaseContractZone.class, LeaseContractZone::getContractId, LeaseContract::getId)
                    .eq(LeaseContractZone::getZoneId, zone.getId())
                    .orderByDesc(LeaseContract::getLeaseStartTime));

            if (contractList != null && !contractList.isEmpty()) {
                LeaseContract latest = contractList.get(0);
                vo.setContractNo(latest.getContractNo());
                vo.setContractId(latest.getId());
                vo.setLessee(getLessee(latest.getLesseeId()));

                LocalDate expireTime = latest.getLeaseEndTime();
                if (expireTime != null) {
                    long years = Period.between(LocalDate.now(), expireTime).get(ChronoUnit.YEARS);
                    long months = Period.between(LocalDate.now(), expireTime).get(ChronoUnit.MONTHS);
                    long days = Period.between(LocalDate.now(), expireTime).get(ChronoUnit.DAYS);
                    if (years <= 0 && months <= 0) {
                        if (days <= 0) {
                            vo.setStatus(LEASE_STATUS_OVERDUE);
                        } else if (days < 30) {
                            vo.setStatus(LEASE_STATUS_EXPIRING);
                        } else {
                            vo.setStatus(LEASE_STATUS_LEASE);
                        }
                    } else {
                        vo.setStatus(LEASE_STATUS_LEASE);
                    }
                }

                List<LeaseItemVO> leaseItemVOS = contractList.stream().map(leaseContract -> {
                    LeaseItemVO leaseItemVO = new LeaseItemVO();
                    BeanUtils.copyProperties(leaseContract, leaseItemVO);
                    leaseItemVO.setLeaseTime(DateHandlerUtil.toYMD(leaseContract.getLeaseStartTime()) + " - " + DateHandlerUtil.toYMD(leaseContract.getLeaseEndTime()));
                    Company company = companyService.lambdaQuery().select(Company::getName)
                            .eq(Company::getId, leaseContract.getLesseeId())
                            .last("limit 1").one();
                    if (company != null) {
                        leaseItemVO.setLessee(company.getName());
                    }
                    return leaseItemVO;
                }).collect(Collectors.toList());

                vo.setHistoricLeaseInfos(leaseItemVOS);
            }

            return vo;
        } catch (Exception e) {
            log.error("error occurred get zone detail! ", e);
            throw e;
        }
    }

    /**
     * 逻辑删除空间（isValid=0）
     */
    @Override
    public void deleteZone(String zoneId) {
        if (StringUtils.isBlank(zoneId)) {
            throw new IllegalArgumentException("zoneId不能为空");
        }

        Zone zone = this.getById(zoneId);
        if (zone == null) {
            throw new IllegalArgumentException("未找到对应空间");
        }

        List<LeaseContractZone> leaseContractZones = leaseContractZoneMapper.selectList(Wrappers.lambdaQuery(LeaseContractZone.class).eq(LeaseContractZone::getZoneId, zoneId));

        if (leaseContractZones != null && !leaseContractZones.isEmpty()) {

            List<String> contractIds = leaseContractZones.stream().map(LeaseContractZone::getContractId).collect(Collectors.toList());
            List<LeaseContract> contractList = leaseContractService.list(Wrappers.lambdaQuery(LeaseContract.class)
                    .in(LeaseContract::getId, contractIds)
                    .orderByDesc(LeaseContract::getLeaseEndTime));

            LocalDate expireTime = contractList.get(0).getLeaseEndTime();
            if (Period.between(LocalDate.now(), expireTime).get(ChronoUnit.DAYS) > 0) {
                throw new IllegalArgumentException("该空间有关联的生效中合同！无法删除");
            }
        }

        zone.setIsValid(NOT_VALID);
        zone.setUpdateUser(LoginHelper.getUsername());
        zone.setUpdateTime(LocalDateTime.now());
        this.updateById(zone);
    }

    @Override
    public List<FloorVO> zoneSimpleList(String parkId, String buildId, String contractId) {

        List<FloorVO> res = new ArrayList<>();

        // 获取所有不在租约中的空间ID列表，或者符合指定合同ID的空间
        List<String> excludeZoneIds = new ArrayList<>();
        
        // 使用链式调用查询所有租约中的空间ID
        if (StringUtils.isBlank(contractId)) {
            // 如果contractId为空，排除所有有租约的空间
            excludeZoneIds = leaseContractZoneMapper.selectList(Wrappers.lambdaQuery(LeaseContractZone.class)
                            .isNotNull(LeaseContractZone::getZoneId)
                            .groupBy(LeaseContractZone::getZoneId))
                    .stream().map(LeaseContractZone::getZoneId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        } else {
            // 如果contractId不为空，排除除指定合同外的所有有租约的空间
            excludeZoneIds = leaseContractZoneMapper.selectList(Wrappers.lambdaQuery(LeaseContractZone.class)
                            .isNotNull(LeaseContractZone::getZoneId)
                            .ne(LeaseContractZone::getContractId, contractId)
                            .groupBy(LeaseContractZone::getZoneId))
                    .stream().map(LeaseContractZone::getZoneId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }
        
        final List<String> finalExcludeZoneIds = excludeZoneIds;
        List<Zone> list = this.lambdaQuery()
                .select(Zone::getId, Zone::getZoneName, Zone::getArea, Zone::getFloorId)
                .eq(Zone::getParkId, parkId)
                .eq(Zone::getBuildId, buildId)
                .eq(Zone::getIsValid, IS_VALID)
                .notIn(CollectionUtils.isNotEmpty(finalExcludeZoneIds), Zone::getId, finalExcludeZoneIds)
                .list();

        if (CollectionUtils.isEmpty(list)) {
            return res;
        }

        List<String> floorIds = list.stream().map(Zone::getFloorId).distinct().collect(Collectors.toList());

        List<Floor> allFloors = floorService.list(new LambdaQueryWrapper<Floor>().in(Floor::getId, floorIds));

        Map<String, List<Zone>> floorIdToZoneList = new HashMap<>();
        for (Zone zone : list) {
            String floorId = zone.getFloorId();
            floorIdToZoneList.computeIfAbsent(floorId, k -> new ArrayList<>()).add(zone);
        }

        allFloors.stream()
                .filter(f -> StringUtils.isNotBlank(f.getFloorName()))
                .distinct()
                .sorted(Comparator.comparing(Floor::getFloorName))
                .forEach(floor -> {
                    FloorVO vo = new FloorVO();
                    vo.setFloorId(floor.getId());
                    vo.setFloorName(floor.getFloorName());
                    vo.setZoneList(Optional.ofNullable(floorIdToZoneList.get(floor.getId())).map(zones ->
                            zones.stream().map(ZoneSimpleVO::convertFromZone).collect(Collectors.toList())
                    ).orElse(new ArrayList<>()));
                    res.add(vo);
                });

        return res;

    }

    @Override
    public List<ZoneSimpleVO> zoneSimpleListAll() {
        return this.lambdaQuery()
                .select(Zone::getId, Zone::getZoneName, Zone::getArea)
                .list()
                .stream()
                .map(zone -> {
                    ZoneSimpleVO vo = new ZoneSimpleVO();
                    BeanUtils.copyProperties(zone, vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }

}
