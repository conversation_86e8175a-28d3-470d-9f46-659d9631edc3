package com.quantchi.application.model.bo;

import com.quantchi.common.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 审批处理请求对象
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Data
@ApiModel(value = "审批处理请求")
public class AuditProcessBO {

    @ApiModelProperty(value = "申请ID", required = true)
    @NotBlank(message = "申请ID不能为空")
    private String applicationId;

    @ApiModelProperty(value = "审批状态：1-已同意；0-已驳回", required = true)
    @NotNull(message = "审批状态不能为空")
    private Integer processStatus;

    @ApiModelProperty(value = "审批意见")
    private String processAdvice;
    
//    @ApiModelProperty(value = "审批流程（可选，如不提供则使用默认值）")
//    private String process;

    @ApiModelProperty("审批材料")
    private List<FileInfo> auditFiles;
}
