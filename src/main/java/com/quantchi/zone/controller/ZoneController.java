package com.quantchi.zone.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.common.domain.InMemoryCache;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.zone.model.entity.Building;
import com.quantchi.zone.model.entity.Floor;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.Zone;
import com.quantchi.zone.model.vo.*;
import com.quantchi.zone.model.bo.ZoneBO;
import com.quantchi.zone.service.IFloorService;
import com.quantchi.zone.service.impl.BuildingServiceImpl;
import com.quantchi.zone.service.impl.FloorServiceImpl;
import com.quantchi.zone.service.impl.ZoneServiceImpl;
import com.quantchi.company.service.impl.CompanyBusinessServiceImpl;
import com.quantchi.contract.service.ILeaseContractService;
import com.quantchi.contract.model.entity.LeaseContract;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <p>
 * 空间实体 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@RestController
@RequestMapping("/zone")
@Slf4j
@Api(tags = "空间管理接口")
@RequiredArgsConstructor
public class ZoneController {

    private final BuildingServiceImpl buildingService;
    private final ZoneServiceImpl zoneService;
    private final FloorServiceImpl floorService;
    @Autowired
    private CompanyBusinessServiceImpl companyService;
    @Autowired
    private ILeaseContractService leaseContractService;

    @GetMapping("/parkList")
    @ApiOperation("园区列表")
    public ResultInfo<List<Park>> parkList() {
        return ResultConvert.success(InMemoryCache.getParkingList());
    }

    @GetMapping("/buildingList")
    @ApiOperation("楼栋列表")
    public ResultInfo<List<Building>> buildingList(@RequestParam final String parkId) {
        return ResultConvert.success(buildingService
                .list(Wrappers.lambdaQuery(Building.class).eq(Building::getParkId, parkId)));
    }
    
    @GetMapping("/floorList")
    @ApiOperation("楼层列表")
    public ResultInfo<List<Floor>> floorList(@RequestParam String buildingId) {
        return ResultConvert.success(floorService
                .list(Wrappers.lambdaQuery(Floor.class).eq(Floor::getBuildingId, buildingId)));
    }

    @GetMapping("/zoneList")
    @ApiOperation("空间列表")
    public ResultInfo<List<ZoneListVO>> zoneList(@RequestParam final String parkId,
                                                 @RequestParam final String buildingId) {
        return ResultConvert.success(zoneService.zoneList(parkId, buildingId));
    }

    @GetMapping("/zoneSimpleList")
    @ApiOperation("空间简单列表（仅id、name、area）")
    public ResultInfo<List<FloorVO>> zoneSimpleList(@RequestParam String parkId, @RequestParam String buildId,
                                                    @RequestParam(required = false) String contractId) {
        return ResultConvert.success(zoneService.zoneSimpleList(parkId, buildId, contractId));
    }

    @GetMapping("/zoneSimpleListAll")
    @ApiOperation("所有空间简单列表（仅id、name、area）")
    public ResultInfo<List<ZoneSimpleVO>> zoneSimpleListAll() {
        return ResultConvert.success(zoneService.zoneSimpleListAll());
    }

    @PostMapping("/editZone")
    @ApiOperation("新增或编辑空间")
    public ResultInfo<String> editZone(@RequestBody @Valid ZoneBO zoneBO) {
        try {
            zoneService.editZone(zoneBO);
            return ResultConvert.success(zoneBO.getZoneId() == null ? "创建空间成功" : "编辑空间成功");
        } catch (Exception e) {
            return ResultConvert.error(e.getMessage());
        }
    }

    @PostMapping("/deleteZone")
    @ApiOperation("删除空间")
    public ResultInfo<String> deleteZone(@RequestParam String zoneId) {
        try {
            zoneService.deleteZone(zoneId);
            return ResultConvert.success("空间删除成功");
        } catch (Exception e) {
            return ResultConvert.error(e.getMessage());
        }
    }

    @GetMapping("/zoneDetail")
    @ApiOperation("空间详情")
    public ResultInfo<ZoneDetailVO> zoneDetail(@RequestParam String zoneId) {
        try {
            ZoneDetailVO detail = zoneService.zoneDetail(zoneId);
            return ResultConvert.success(detail);
        } catch (Exception e) {
            return ResultConvert.error(e.getMessage());
        }
    }
}
