package com.quantchi.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/24 15:14
 */
@ApiModel("es分页查询结果")
@Data
public class EsPageResult {

    @ApiModelProperty("总数")
    private Long total = 0L;

    @ApiModelProperty("列表")
    private List<Map<String, Object>> list = new ArrayList<>();

    @ApiModelProperty("分页大小")
    private Integer pageSize;

    @ApiModelProperty("最大条数限制")
    private Integer maxSize;
}
