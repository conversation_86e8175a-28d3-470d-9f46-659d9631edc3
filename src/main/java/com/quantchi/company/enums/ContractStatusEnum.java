package com.quantchi.company.enums;

public enum ContractStatusEnum {
    INVALID(0, "已失效"),
    ACTIVE(1, "生效中"),
    INACTIVE(2, "未生效"),
    ;

    private final int code;
    private final String desc;

    ContractStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
