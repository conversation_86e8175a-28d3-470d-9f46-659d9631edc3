package com.quantchi.common.domain;

import com.quantchi.common.constant.SearchTypeConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * GIKS项目导航栏设置
 */
@Data
public class NavigationSettings {

    private Map<String, List<CustomIndexNavSetting>> indexNavSettingMap;

    public List<String> getRangeFields(final CustomIndexNavSetting setting,
                                       final List<String> inputFields, final Integer searchType) {
        if (setting == null) {
            return inputFields;
        }
        final List<String> selectedFields = new ArrayList<>();
        final Map<String, String> navMaps = new HashMap<>();

        // 初始化配置的范围值
        if (CollectionUtils.isNotEmpty(setting.getRange())) {
            final List<String> scope = setting.getScope();
            final List<String> range = setting.getRange();
            for (int i = 0; i < scope.size(); i++) {
                navMaps.put(scope.get(i), range.get(i));
            }
        }
        // 查找选择到的值对应的范围
        for (final String input : inputFields) {
            final String rangeInput = navMaps.get(input);
            if (null != rangeInput) {
                // 已存在定义好的值，比如"3月内"对应"0,3"
                if (searchType.equals(SearchTypeConstants.ONE_TO_MANY)) {
                    final String[] split = rangeInput.split(",");
                    selectedFields.addAll(Arrays.asList(split));
                } else {
                    selectedFields.add(rangeInput);
                }
            } else {
                // 不存在已定义好的值，那就直接添加值
                selectedFields.add(input);
            }
        }

        return selectedFields;
    }


    public Object[] getAliasFields(final List<CustomIndexNavSetting> navSettings, final List<String> inputFields) {
        final List<String> selectedFields = new ArrayList<>();
        final Map<String, String> navMaps = new HashMap<>();

        //初始化别名的配置
        for (final IndexNavSetting setting : navSettings) {
            if (CollectionUtils.isNotEmpty(setting.getAlias())) {
                final List<String> scopeList = setting.getScope();
                final List<String> aliasList = setting.getAlias();
                for (int i = 0; i < scopeList.size(); i++) {
                    navMaps.put(scopeList.get(i), aliasList.get(i));
                }
            }
        }
        //查找选择到的值对应的范围
        for (final String input : inputFields) {
            if (null != navMaps.get(input)) {
                selectedFields.add(navMaps.get(input));
            }
        }
        return selectedFields.toArray(new String[0]);
    }

}
