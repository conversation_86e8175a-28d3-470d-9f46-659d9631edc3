<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.contract.mapper.LeaseContractMapper">

    <select id="pageLeaseContractList" resultType="com.quantchi.contract.model.vo.LeaseContractListVO">
        SELECT * FROM (
            (SELECT
                c.id,
                c.contract_no,
                co.name AS lessee,
                c.lease_start_time,
                c.lease_end_time,
                c.rental_collect,
                c.create_time,
                c.create_user
            FROM lease_contract c
            LEFT JOIN company co ON c.lessee_id = co.id
            LEFT JOIN zone z ON c.zone_id = z.id
        <where>
                <if test="query.keyword != null and query.keyword != ''">
                    c.contract_no LIKE CONCAT('%', #{query.keyword}, '%')
                </if>
                <if test="query.parkId != null and query.parkId != ''">
                    AND z.park_id = #{query.parkId}
                </if>
                <if test="query.leaseStartTimeBegin != null">
                    AND c.lease_start_time &gt;= #{query.leaseStartTimeBegin}
                </if>
                <if test="query.leaseStartTimeEnd != null">
                    AND c.lease_start_time &lt;= #{query.leaseStartTimeEnd}
                </if>
                <if test="query.createTimeBegin != null">
                    AND c.create_time &gt;= #{query.createTimeBegin}
                </if>
                <if test="query.createTimeEnd != null">
                    AND c.create_time &lt;= #{query.createTimeEnd}
                </if>
            </where>
            )
            UNION
            (SELECT
                c.id,
                c.contract_no,
                co.name AS lessee,
                c.lease_start_time,
                c.lease_end_time,
                c.rental_collect,
                c.create_time,
                c.create_user
            FROM lease_contract c
            LEFT JOIN company co ON c.lessee_id = co.id
            LEFT JOIN zone z ON c.zone_id = z.id
            <where>
                <if test="query.keyword != null and query.keyword != ''">
                    co.name LIKE CONCAT('%', #{query.keyword}, '%')
                </if>
                <if test="query.parkId != null and query.parkId != ''">
                    AND z.park_id = #{query.parkId}
                </if>
                <if test="query.leaseStartTimeBegin != null">
                    AND c.lease_start_time &gt;= #{query.leaseStartTimeBegin}
                </if>
                <if test="query.leaseStartTimeEnd != null">
                    AND c.lease_start_time &lt;= #{query.leaseStartTimeEnd}
                </if>
                <if test="query.createTimeBegin != null">
                    AND c.create_time &gt;= #{query.createTimeBegin}
                </if>
                <if test="query.createTimeEnd != null">
                    AND c.create_time &lt;= #{query.createTimeEnd}
                </if>
            </where>
            )
        ) t
        <if test="query.keyword != null and query.keyword != ''">
            ORDER BY t.create_time DESC
        </if>
        LIMIT #{offset}, #{size}
    </select>

    <select id="countLeaseContractList" resultType="long">
        SELECT COUNT(*) FROM (
            (SELECT c.id
            FROM lease_contract c
            LEFT JOIN company co ON c.lessee_id = co.id
            LEFT JOIN zone z ON c.zone_id = z.id
            <where>
                <if test="query.keyword != null and query.keyword != ''">
                    c.contract_no LIKE CONCAT('%', #{query.keyword}, '%')
                </if>
                <if test="query.parkId != null and query.parkId != ''">
                    AND z.park_id = #{query.parkId}
                </if>
                <if test="query.leaseStartTimeBegin != null">
                    AND c.lease_start_time &gt;= #{query.leaseStartTimeBegin}
                </if>
                <if test="query.leaseStartTimeEnd != null">
                    AND c.lease_start_time &lt;= #{query.leaseStartTimeEnd}
                </if>
                <if test="query.createTimeBegin != null">
                    AND c.create_time &gt;= #{query.createTimeBegin}
                </if>
                <if test="query.createTimeEnd != null">
                    AND c.create_time &lt;= #{query.createTimeEnd}
                </if>
            </where>
            )
            UNION
            (SELECT c.id
            FROM lease_contract c
            LEFT JOIN company co ON c.lessee_id = co.id
            LEFT JOIN zone z ON c.zone_id = z.id
            <where>
                <if test="query.keyword != null and query.keyword != ''">
                    co.name LIKE CONCAT('%', #{query.keyword}, '%')
                </if>
                <if test="query.parkId != null and query.parkId != ''">
                    AND z.park_id = #{query.parkId}
                </if>
                <if test="query.leaseStartTimeBegin != null">
                    AND c.lease_start_time &gt;= #{query.leaseStartTimeBegin}
                </if>
                <if test="query.leaseStartTimeEnd != null">
                    AND c.lease_start_time &lt;= #{query.leaseStartTimeEnd}
                </if>
                <if test="query.createTimeBegin != null">
                    AND c.create_time &gt;= #{query.createTimeBegin}
                </if>
                <if test="query.createTimeEnd != null">
                    AND c.create_time &lt;= #{query.createTimeEnd}
                </if>
            </where>
            )
        ) t
    </select>

    <!-- 统计未来30天内合同到期的企业数量 -->
    <select id="countExpiringCompanies" resultType="long">
        SELECT COUNT(DISTINCT lessee_id)
        FROM lease_contract
        WHERE lease_end_time BETWEEN NOW() AND #{endDate}
    </select>
    
    <!-- 统计指定园区未来30天内合同到期的企业数量 -->
    <select id="countExpiringCompaniesByPark" resultType="long">
        SELECT COUNT(DISTINCT lc.lessee_id)
        FROM lease_contract lc
        LEFT JOIN zone z ON lc.zone_id = z.id
        LEFT JOIN park p ON z.park_id = p.id
        WHERE lc.lease_end_time BETWEEN NOW() AND #{endDate}
        AND p.id = #{parkId}
    </select>
    
    <!-- 根据lesseeId查询合同id和contractNo -->
    <select id="selectIdAndNoByLesseeId" resultType="com.quantchi.contract.model.vo.LeaseContractSimpleVO">
        SELECT id, contract_no AS contractNo
        FROM lease_contract 
        WHERE lessee_id = #{lesseeId}
    </select>

</mapper>
