package com.quantchi.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13 15:05
 */
@NoArgsConstructor
@Data
public class AliyunEchData {

    @JsonProperty("result")
    private ResultDTO result;
    @JsonProperty("success")
    private Boolean success;
    @JsonProperty("tracerId")
    private String tracerId;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JsonProperty("edits")
        private List<EditsDTO> edits;
        @JsonProperty("source")
        private String source;
        @JsonProperty("target")
        private String target;

        @NoArgsConstructor
        @Data
        public static class EditsDTO {
            @JsonProperty("tgt")
            private String tgt;
            @JsonProperty("pos")
            private Integer pos;
            @JsonProperty("src")
            private String src;
            @JsonProperty("confidence")
            private Double confidence;
            @JsonProperty("type")
            private String type;
        }
    }
}
