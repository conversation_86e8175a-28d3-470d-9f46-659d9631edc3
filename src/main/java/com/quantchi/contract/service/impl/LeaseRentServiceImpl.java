package com.quantchi.contract.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.contract.mapper.LeaseRentMapper;
import com.quantchi.contract.model.bo.LeaseRentListQuery;
import com.quantchi.contract.model.entity.LeaseRent;
import com.quantchi.contract.model.vo.*;
import com.quantchi.contract.service.ILeaseRentService;
import com.quantchi.contract.service.ILeaseContractService;
import com.quantchi.zone.model.entity.Building;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.Zone;
import com.quantchi.zone.service.IZoneService;
import com.quantchi.zone.service.IParkService;
import com.quantchi.zone.service.IBuildingService;
import com.quantchi.company.service.ICompanyService;
import com.quantchi.contract.model.bo.EditLeaseRentBO;
import com.quantchi.contract.model.enums.PayStatusEnum;
import com.quantchi.contract.utils.NoFillStyleStrategy;
import com.quantchi.contract.utils.PostProcessStyleStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.excel.EasyExcel;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.poi.ss.usermodel.*;

@Service
@Slf4j
public class LeaseRentServiceImpl extends ServiceImpl<LeaseRentMapper, LeaseRent> implements ILeaseRentService {
    @Autowired
    private ILeaseContractService leaseContractService;
    @Autowired
    private IZoneService zoneService;
    @Autowired
    private IParkService parkService;
    @Autowired
    private IBuildingService buildingService;
    @Autowired
    private ICompanyService companyService;

    @Autowired
    private LeaseRentMapper leaseRentMapper;

    @Override
    public LeaseRentDetailVO listByContractId(String contractId) {
        // 一次查扩展信息
        LeaseRentDetailVO detailVO = leaseRentMapper.selectContractExtInfoById(contractId);

        if (detailVO == null) {
            return new LeaseRentDetailVO();
        }

        if (StringUtils.isNotBlank(detailVO.getZoneName())) {
            if (detailVO.getZoneName().contains(",")) {
                List<String> zoneIds = Arrays.stream(detailVO.getZoneName().split(",")).collect(Collectors.toList());
                List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).in(Zone::getId, zoneIds));
                getLocation(zones, detailVO);
            } else {
                List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).eq(Zone::getId, detailVO.getZoneName()));
                getLocation(zones, detailVO);
            }

        }

        detailVO.setContractId(contractId);
        // 查租金明细
        List<LeaseRent> rents = this.lambdaQuery().eq(LeaseRent::getContractId, contractId).list();
        List<LeaseRentVO> rentVOList = rents.stream().map(e -> {
            LeaseRentVO vo = new LeaseRentVO();
            BeanUtils.copyProperties(e, vo);
            vo.setReceivableAmount(e.getReceivableAmountEdit() != null ? e.getReceivableAmountEdit() : e.getReceivableAmount());
            return vo;
        }).sorted(Comparator.comparing(LeaseRentVO::getYear)
                .thenComparing(LeaseRentVO::getQuarter))
                .collect(Collectors.toList());
        // 按年份和季度升序排序
        detailVO.setRentList(rentVOList);
        return detailVO;
    }

    private void getLocation(List<Zone> zones, LeaseContractBaseVO detailVO) {
        Map<String, List<Zone>> zoneMap = zones.stream().collect(Collectors.groupingBy(Zone::getBuildId));
        int parkSize = (int) zones.stream().map(Zone::getParkId).distinct().count();
        StringBuilder parks = new StringBuilder();
        StringBuilder buildings = new StringBuilder();
        StringBuilder locationInfo = new StringBuilder();
        zoneMap.forEach((buildId, zoneList) -> {
            Zone temp = zoneList.get(0);
            // parkId -> parkName
            if (StringUtils.isNotBlank(temp.getParkId())) {
                Park park = parkService.getById(temp.getParkId());
                if (park != null) {
                    parks.append(park.getParkName()).append("、");
                    if (parkSize > 1) {
                        buildings.append(park.getParkName()).append("-");
                        locationInfo.append(park.getParkName()).append("-");
                    }
                }
            }
            if (StringUtils.isNotBlank(temp.getBuildId())) {
                Building building = buildingService.getById(temp.getBuildId());
                if (building != null) {
                    buildings.append(building.getBuildingName()).append("、");
                    if (zoneMap.size() > 1) {
                        locationInfo.append(building.getBuildingName()).append("-");
                    }
                }
            }
            zoneList.forEach(zone -> {
                locationInfo.append(zone.getZoneName()).append("、");
            });
            locationInfo.deleteCharAt(locationInfo.lastIndexOf("、"));
            locationInfo.append("；");
        });
        parks.deleteCharAt(parks.lastIndexOf("、"));
        buildings.deleteCharAt(buildings.lastIndexOf("、"));
        locationInfo.deleteCharAt(locationInfo.lastIndexOf("；"));
        detailVO.setParkName(parks.toString());
        detailVO.setBuildingName(buildings.toString());
        detailVO.setZoneName(locationInfo.toString());
    }

    @Override
    public IPage<LeaseRentListVO> pageRentByCondition(LeaseRentListQuery query) {
        // 设置只查当前年当前季度及之前的租金
        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();
        int currentQuarter = (currentMonth - 1) / 3 + 1;

        query.setYearLimit(currentYear);
        query.setQuarterLimit(currentQuarter);

        // 查询所有满足条件的数据
        List<LeaseRentListVO> allList = leaseRentMapper.selectByCondition(query);

        // 按合同聚合历史租金，计算缴纳状态
        Map<String, List<LeaseRentListVO>> contractMap = allList.stream()
                .collect(Collectors.groupingBy(LeaseRentListVO::getContractId));

        List<LeaseRentListVO> contractFirstList = new ArrayList<>();

        for (Map.Entry<String, List<LeaseRentListVO>> entry : contractMap.entrySet()) {
            List<LeaseRentListVO> voList = entry.getValue();

            // 用于聚合缴纳状态
            BigDecimal totalReceivable = voList.stream()
                    .map(LeaseRentListVO::getReceivableAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalReceived = voList.stream()
                    .map(LeaseRentListVO::getReceivedAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            PayStatusEnum payStatus;
            if (totalReceivable.compareTo(BigDecimal.ZERO) == 0) {
                payStatus = PayStatusEnum.UNPAID;
            } else if (totalReceived.compareTo(totalReceivable) >= 0) {
                payStatus = PayStatusEnum.PAID;
            } else if (totalReceived.compareTo(BigDecimal.ZERO) > 0) {
                payStatus = PayStatusEnum.PARTIAL;
            } else {
                payStatus = PayStatusEnum.UNPAID;
            }

            LeaseRentListVO firstVO = voList.get(0);
            firstVO.setPayStatus(payStatus.getCode());

            if (StringUtils.isNotBlank(firstVO.getZoneName())) {
                if (firstVO.getZoneName().contains(",")) {
                    List<String> zoneIds = Arrays.stream(firstVO.getZoneName().split(",")).collect(Collectors.toList());
                    List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).in(Zone::getId, zoneIds));
                    getLocation(zones, firstVO);
                } else {
                    List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).eq(Zone::getId, firstVO.getZoneName()));
                    getLocation(zones, firstVO);
                }
            }

            contractFirstList.add(firstVO);
        }

        // payStatus 查询过滤
        List<LeaseRentListVO> filteredList = contractFirstList;
        if (query.getPayStatus() != null) {
            int filterStatus = query.getPayStatus();
            filteredList = contractFirstList.stream()
                .filter(vo -> vo.getPayStatus() != null && vo.getPayStatus().intValue() == filterStatus)
                .collect(Collectors.toList());
        }

        // 按合同创建时间倒序排列
        filteredList.sort(Comparator.comparing(LeaseRentListVO::getContractCreateTime, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(LeaseRentListVO::getContractId));

        // 手动分页
        int pageNum = query.getPageNum() == null ? 1 : query.getPageNum();
        int pageSize = query.getPageSize() == null ? 10 : query.getPageSize();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, filteredList.size());
        List<LeaseRentListVO> pageRecords = start >= filteredList.size() ? Collections.emptyList() : filteredList.subList(start, end);

        Page<LeaseRentListVO> page = new Page<>(pageNum, pageSize);
        page.setRecords(pageRecords);
        page.setTotal(filteredList.size());
        return page;
    }

    @Override
    public List<LeaseRentListVO> listRentByCondition(LeaseRentListQuery query) {

        // 使用专门的导出查询，返回每个租金记录
        List<LeaseRentListVO> allList = leaseRentMapper.selectForExport(query);

        // 处理园区、楼栋、房号的显示
        for (LeaseRentListVO item : allList) {
            // 查询合同关联的空间信息
            List<Zone> zones = getZonesByContractId(item.getContractId());
            if (!zones.isEmpty()) {
                getLocation(zones, item);
            }

            // 计算缴纳状态
            BigDecimal receivableAmount = item.getReceivableAmount() != null ? item.getReceivableAmount() : BigDecimal.ZERO;
            BigDecimal receivedAmount = item.getReceivedAmount() != null ? item.getReceivedAmount() : BigDecimal.ZERO;

            int payStatus;
            if (receivedAmount.compareTo(BigDecimal.ZERO) == 0) {
                payStatus = 0; // 未缴纳
            } else if (receivedAmount.compareTo(receivableAmount) >= 0) {
                payStatus = 1; // 已缴纳
            } else {
                payStatus = 2; // 部分缴纳
            }
            item.setPayStatus(payStatus);
        }

        // 根据缴纳状态过滤
        List<LeaseRentListVO> filteredList = allList;
        if (query.getPayStatus() != null) {
            filteredList = allList.stream()
                    .filter(rent -> rent.getPayStatus().equals(query.getPayStatus()))
                    .collect(Collectors.toList());
        }

        // 按合同创建时间倒序，然后按年份、季度排序
        filteredList.sort(Comparator.comparing(LeaseRentListVO::getContractCreateTime, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(LeaseRentListVO::getYear)
                .thenComparing(LeaseRentListVO::getQuarter));

        return filteredList;
    }

    /**
     * 根据合同ID查询关联的空间信息
     */
    private List<Zone> getZonesByContractId(String contractId) {
        // 通过合同查询关联的空间
        return zoneService.list(Wrappers.lambdaQuery(Zone.class)
                .inSql(Zone::getId, "SELECT zone_id FROM lease_contract_zone WHERE contract_id = '" + contractId + "'"));
    }

    @Override
    public void exportRentByCondition(LeaseRentListQuery query, HttpServletResponse response) throws Exception {

        try {
            // 查询所有符合条件的数据
            List<LeaseRentListVO> dataList = listRentByCondition(query);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 生成文件名
            String fileName = "租金明细_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);

            // 动态生成Excel
            generateDynamicExcel(dataList, response.getOutputStream());

            log.info("租金明细Excel导出完成，共导出{}条数据", dataList.size());

        } catch (IOException e) {
            log.error("导出租金明细Excel失败", e);
            throw new Exception("导出Excel失败：" + e.getMessage());
        }
    }

    @Override
    public BigDecimal getOriginalAmountByContractId(String contractId) {
        return this.lambdaQuery().eq(LeaseRent::getContractId, contractId).list().stream().map(LeaseRent::getReceivableAmount).distinct().collect(Collectors.toList()).get(0);
    }

    /**
     * 动态生成Excel
     */
    private void generateDynamicExcel(List<LeaseRentListVO> dataList, OutputStream outputStream) throws IOException {
        if (dataList.isEmpty()) {
            // 如果没有数据，创建空的Excel
            EasyExcel.write(outputStream)
                    .sheet("租金明细")
                    .doWrite(Collections.emptyList());
            return;
        }

        // 1. 获取所有年份季度组合，并排序
        Set<String> yearQuarterSet = dataList.stream()
                .filter(item -> item.getYear() != null && item.getQuarter() != null)
                .map(item -> item.getYear() + "Q" + item.getQuarter())
                .collect(Collectors.toSet());

        List<String> sortedYearQuarters = yearQuarterSet.stream()
                .sorted((a, b) -> {
                    // 按年份和季度排序
                    String[] aParts = a.split("Q");
                    String[] bParts = b.split("Q");
                    int yearCompare = Integer.compare(Integer.parseInt(aParts[0]), Integer.parseInt(bParts[0]));
                    if (yearCompare != 0) return yearCompare;
                    return Integer.compare(Integer.parseInt(aParts[1]), Integer.parseInt(bParts[1]));
                })
                .collect(Collectors.toList());

        // 2. 构建表头
        List<List<String>> headers = buildDynamicHeaders(sortedYearQuarters);

        // 3. 按合同分组并构建数据行
        Map<String, List<LeaseRentListVO>> contractMap = dataList.stream()
                .collect(Collectors.groupingBy(LeaseRentListVO::getContractId));

        List<List<Object>> dataRows = new ArrayList<>();
        for (Map.Entry<String, List<LeaseRentListVO>> entry : contractMap.entrySet()) {
            List<LeaseRentListVO> contractRents = entry.getValue();
            if (contractRents.isEmpty()) continue;

            List<Object> row = buildDataRow(contractRents, sortedYearQuarters);
            dataRows.add(row);
        }

        // 4. 先生成到临时字节数组
        ByteArrayOutputStream tempOutput = new ByteArrayOutputStream();
        EasyExcel.write(tempOutput)
                .head(headers)
                .sheet("租金明细")
                .doWrite(dataRows);

        // 5. 用POI重新处理，移除灰色背景并设置列宽
        processExcelStyle(tempOutput.toByteArray(), outputStream, sortedYearQuarters.size());
    }

    /**
     * 构建动态表头
     */
    private List<List<String>> buildDynamicHeaders(List<String> sortedYearQuarters) {
        List<List<String>> headers = new ArrayList<>();

        // 固定列
        headers.add(Arrays.asList("", "合同编号"));
        headers.add(Arrays.asList("", "承租方"));
        headers.add(Arrays.asList("", "房号/楼栋/园区"));
        headers.add(Arrays.asList("", "租金标准（元/㎡·月）"));

        // 动态年份季度列，每个季度包含5个字段
        for (String yearQuarter : sortedYearQuarters) {
            headers.add(Arrays.asList(yearQuarter, "折扣系数"));
            headers.add(Arrays.asList(yearQuarter, "减免金额（元）"));
            headers.add(Arrays.asList(yearQuarter, "应收金额（元）"));
            headers.add(Arrays.asList(yearQuarter, "已收金额（元）"));
            headers.add(Arrays.asList(yearQuarter, "未收金额（元）"));
        }

        return headers;
    }

    /**
     * 构建数据行
     */
    private List<Object> buildDataRow(List<LeaseRentListVO> contractRents, List<String> sortedYearQuarters) {
        if (contractRents.isEmpty()) return Collections.emptyList();

        // 取第一条记录作为基础信息
        LeaseRentListVO firstRent = contractRents.get(0);

        List<Object> row = new ArrayList<>();

        // 固定列数据
        row.add(firstRent.getContractNo());
        row.add(firstRent.getLessee());

        // 组合房号/楼栋/园区信息
        String zoneInfo = String.format("%s/%s/%s",
                firstRent.getZoneName() != null ? firstRent.getZoneName() : "",
                firstRent.getBuildingName() != null ? firstRent.getBuildingName() : "",
                firstRent.getParkName() != null ? firstRent.getParkName() : "");
        row.add(zoneInfo);

        row.add(firstRent.getRentStandard());

        // 创建年份季度到租金数据的映射
        Map<String, LeaseRentListVO> yearQuarterMap = contractRents.stream()
                .filter(rent -> rent.getYear() != null && rent.getQuarter() != null)
                .collect(Collectors.toMap(
                        rent -> rent.getYear() + "Q" + rent.getQuarter(),
                        rent -> rent,
                        (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

        // 动态年份季度列数据，每个季度包含5个字段
        for (String yearQuarter : sortedYearQuarters) {
            LeaseRentListVO rent = yearQuarterMap.get(yearQuarter);
            if (rent != null) {
                row.add(rent.getDiscount()); // 折扣系数
                row.add(rent.getReduction()); // 减免金额
                row.add(rent.getReceivableAmount()); // 应收金额
                row.add(rent.getReceivedAmount()); // 已收金额
                row.add(rent.getUnreceivedAmount()); // 未收金额
            } else {
                row.add(null); // 折扣系数
                row.add(null); // 减免金额
                row.add(null); // 应收金额
                row.add(null); // 已收金额
                row.add(null); // 未收金额
            }
        }

        return row;
    }

    /**
     * 获取缴纳状态描述
     */
    private String getPayStatusDesc(Integer payStatus) {
        if (payStatus == null) {
            return "未知";
        }
        switch (payStatus) {
            case 0:
                return "未缴纳";
            case 1:
                return "已缴纳";
            case 2:
                return "部分缴纳";
            default:
                return "未知";
        }
    }

    @Override
    public boolean editLeaseRent(EditLeaseRentBO bo) {
        try {
            LeaseRent rent = this.lambdaQuery()
                    .eq(LeaseRent::getContractId, bo.getContractId())
                    .eq(LeaseRent::getYear, bo.getYear())
                    .eq(LeaseRent::getQuarter, bo.getQuarter())
                    .last("limit 1").one();
            if (rent == null) {
                throw new IllegalArgumentException("租金信息不存在，请重新选择");
            }
            rent.setDiscount(bo.getDiscount() != null ? bo.getDiscount() : BigDecimal.ONE);
            rent.setReduction(bo.getReduction() != null ? bo.getReduction() : BigDecimal.ZERO);
            rent.setReceivableAmountEdit(bo.getReceivableAmount());
            rent.setReceivedAmount(bo.getReceivedAmount());
            if (bo.getReceivableAmount() != null && bo.getReceivedAmount() != null) {
                rent.setUnreceivedAmount(bo.getReceivableAmount().subtract(bo.getReceivedAmount()));
            }
            return this.updateById(rent);
        } catch (Exception e) {
            log.error("edit leaseRent process error! ", e);
            throw e;
        }
    }

    @Override
    public boolean editLeaseRentByCompany(EditLeaseRentBO bo) {
        try {
            LeaseRent rent = this.lambdaQuery()
                .eq(LeaseRent::getCompanyId, bo.getCompanyId())
                .eq(LeaseRent::getYear, bo.getYear())
                .eq(LeaseRent::getQuarter, bo.getQuarter())
                .last("limit 1").one();
            if (rent == null) {
                throw new IllegalArgumentException("租金信息不存在，请重新选择");
            }

            rent.setDiscount(bo.getDiscount() != null ? bo.getDiscount() : BigDecimal.ONE);
            rent.setReduction(bo.getReduction() != null ? bo.getReduction() : BigDecimal.ZERO);
            rent.setReceivableAmountEdit(bo.getReceivableAmount());
            rent.setReceivedAmount(bo.getReceivedAmount());
            if (bo.getReceivableAmount() != null && bo.getReceivedAmount() != null) {
                rent.setUnreceivedAmount(bo.getReceivableAmount().subtract(bo.getReceivedAmount()));
            }
            return this.updateById(rent);
        } catch (Exception e) {
            log.error("error occurred when edit leaseRent! ", e);
            throw e;
        }
    }

    @Override
    public List<LeaseRentYearVO> listByContractIdGroupByYear(String contractId) {
        List<LeaseRent> rents = this.lambdaQuery().eq(LeaseRent::getContractId, contractId).list();
        Map<Integer, List<LeaseRentVO>> yearMap = rents.stream()
                .map(e -> {
                    LeaseRentVO vo = new LeaseRentVO();
                    BeanUtils.copyProperties(e, vo);
                    return vo;
                })
                .collect(Collectors.groupingBy(LeaseRentVO::getYear));
        List<LeaseRentYearVO> result = new ArrayList<>();
        for (Map.Entry<Integer, List<LeaseRentVO>> entry : yearMap.entrySet()) {
            LeaseRentYearVO yearVO = new LeaseRentYearVO();
            yearVO.setYear(entry.getKey());
            yearVO.setRents(entry.getValue());
            result.add(yearVO);
        }
        // 按年份升序
        result.sort(Comparator.comparing(LeaseRentYearVO::getYear));
        return result;
    }

    @Override
    public IPage<LeaseRentContractVO> pageRentByConditionGroupByContract(LeaseRentListQuery query) {
//        // 先查出所有符合条件的租金明细
//        IPage<LeaseRentListVO> flatPage = leaseRentMapper.selectPageByCondition(new Page<>(query.getPageNum(), query.getPageSize()), query);
//        List<LeaseRentListVO> allRents = flatPage.getRecords();
//        // 按合同id分组
//        Map<String, List<LeaseRentListVO>> contractMap = allRents.stream().collect(Collectors.groupingBy(LeaseRentListVO::getContractId));
//        List<LeaseRentContractVO> contractVOList = new ArrayList<>();
//        for (Map.Entry<String, List<LeaseRentListVO>> entry : contractMap.entrySet()) {
//            LeaseRentContractVO contractVO = new LeaseRentContractVO();
//            contractVO.setContractId(entry.getKey());
//            contractVO.setRents(entry.getValue());
//            contractVOList.add(contractVO);
//        }
//        // 构造分页对象
//        Page<LeaseRentContractVO> resultPage = new Page<>(flatPage.getCurrent(), flatPage.getSize(), flatPage.getTotal());
//        resultPage.setRecords(contractVOList);
//        return resultPage;
        return null;
    }

    /**
     * 处理Excel样式，移除灰色背景并设置列宽
     */
    private void processExcelStyle(byte[] excelData, OutputStream outputStream, int quarterCount) throws IOException {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(excelData);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);

            // 设置列宽
            setColumnWidths(sheet, quarterCount);

            // 创建透明样式
            CellStyle transparentStyle = createTransparentStyle(workbook);

            // 处理表头行（前两行）
            for (int rowIndex = 0; rowIndex < Math.min(2, sheet.getLastRowNum() + 1); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    for (int colIndex = 0; colIndex < row.getLastCellNum(); colIndex++) {
                        Cell cell = row.getCell(colIndex);
                        if (cell != null) {
                            // 保留原有的值，只改变样式
                            String cellValue = getCellValueAsString(cell);
                            cell.setCellStyle(transparentStyle);
                            cell.setCellValue(cellValue);
                        }
                    }
                }
            }

            // 写入到输出流
            workbook.write(outputStream);
        }
    }

    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet, int quarterCount) {
        sheet.setColumnWidth(0, 20 * 256); // 合同编号
        sheet.setColumnWidth(1, 25 * 256); // 承租方
        sheet.setColumnWidth(2, 30 * 256); // 房号/楼栋/园区
        sheet.setColumnWidth(3, 20 * 256); // 租金标准

        // 动态季度列
        int startCol = 4;
        for (int i = 0; i < quarterCount; i++) {
            int baseCol = startCol + i * 5;
            sheet.setColumnWidth(baseCol, 12 * 256);     // 折扣系数
            sheet.setColumnWidth(baseCol + 1, 16 * 256); // 减免金额
            sheet.setColumnWidth(baseCol + 2, 16 * 256); // 应收金额
            sheet.setColumnWidth(baseCol + 3, 16 * 256); // 已收金额
            sheet.setColumnWidth(baseCol + 4, 16 * 256); // 未收金额
        }
    }

    /**
     * 创建透明样式
     */
    private CellStyle createTransparentStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillPattern(FillPatternType.NO_FILL);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(false);
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 简化处理，直接转换为字符串
                double numericValue = cell.getNumericCellValue();
                if (numericValue == (long) numericValue) {
                    return String.valueOf((long) numericValue);
                } else {
                    return String.valueOf(numericValue);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}