package com.quantchi.contract.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.common.service.impl.FileInfoServiceImpl;
import com.quantchi.company.model.entity.Company;
import com.quantchi.company.service.ICompanyService;
import com.quantchi.contract.model.entity.LeaseContract;
import com.quantchi.contract.mapper.LeaseContractMapper;
import com.quantchi.contract.model.entity.LeaseContractZone;
import com.quantchi.contract.model.entity.LeaseRent;
import com.quantchi.contract.model.vo.*;
import com.quantchi.contract.service.ILeaseContractService;
import com.quantchi.contract.model.bo.LeaseContractBO;
import com.quantchi.contract.service.ILeaseContractZoneService;
import com.quantchi.contract.service.ILeaseRentService;
import com.quantchi.sys.utils.LoginHelper;
import com.quantchi.zone.service.IParkService;
import com.quantchi.zone.service.IBuildingService;
import com.quantchi.zone.service.IZoneService;
import com.quantchi.zone.model.entity.Zone;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.Building;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.quantchi.common.domain.InMemoryCache;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.quantchi.common.utils.WordUtils;
import com.quantchi.common.utils.DocToPdfConverter;
import com.quantchi.common.utils.MoneyUtils;

/**
 * <p>
 * 租赁合同 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Service
@Slf4j
public class LeaseContractServiceImpl extends ServiceImpl<LeaseContractMapper, LeaseContract> implements ILeaseContractService {

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private IParkService parkService;

    @Autowired
    private IBuildingService buildingService;

    @Autowired
    private IZoneService zoneService;

    @Autowired
    private LeaseContractMapper leaseContractMapper;

    @Autowired
    private FileInfoServiceImpl fileInfoService;

    @Autowired
    private ILeaseRentService leaseRentService;

    @Autowired
    private ILeaseContractZoneService leaseContractZoneService;

    @Value("${file.upload.contractScanCopy}")
    private String contractScanCopyDir;
    @Value("${file.upload.investAgreement}")
    private String investAgreementDir;
    @Value("${self.address}")
    private String downloadAddress;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateLeaseContract(LeaseContractBO bo) {
        try {

            if (StringUtils.isNotBlank(bo.getContractNo())) {
                List<LeaseContract> list = this.list(Wrappers.lambdaQuery(LeaseContract.class)
                        .eq(LeaseContract::getContractNo, bo.getContractNo()));
                if (CollectionUtils.isNotEmpty(list)) {
                    if (list.size() == 1) {
                        LeaseContract exist = list.get(0);
                        if (StringUtils.isBlank(bo.getId()) && !exist.getId().equals(bo.getId())) {
                            throw new IllegalArgumentException("合同编号已存在");
                        }
                    } else {
                        throw new IllegalArgumentException("合同编号已存在");
                    }
                }
            }

            if (StringUtils.isBlank(bo.getLesseeId()) && StringUtils.isNotBlank(bo.getLessee())) {
                Company company = new Company();
                BeanUtils.copyProperties(bo, company);
                company.setName(bo.getLessee());
                bo.setLesseeId(companyService.addCompanyInfo(company));
                if (StringUtils.isBlank(bo.getLesseeId())) {
                    throw new IllegalArgumentException("企业创建失败");
                }
            }

            if (StringUtils.isBlank(bo.getId())) {
                bo.setId(UUID.randomUUID().toString().replace("-", ""));
            }

            LeaseContract contract = new LeaseContract();
            BeanUtils.copyProperties(bo, contract);

            LeaseContract existedContract = this.lambdaQuery().eq(LeaseContract::getId, bo.getId()).last("limit 1").one();
            if (existedContract != null) {
                contract.setUpdateUser(LoginHelper.getUsername());
                contract.setUpdateTime(LocalDateTime.now());
                this.updateById(contract);

            } else {
                contract.setCreateUser(LoginHelper.getUsername());
                contract.setUpdateUser(LoginHelper.getUsername());
                contract.setCreateTime(LocalDate.now());
                contract.setUpdateTime(LocalDateTime.now());
                this.save(contract);
            }

            //处理合同相关的空间
            if (existedContract != null) {
                List<String> existedZoneIds = leaseContractZoneService.list(Wrappers.lambdaQuery(LeaseContractZone.class)
                        .eq(LeaseContractZone::getContractId, bo.getId()))
                        .stream().map(LeaseContractZone::getZoneId).collect(Collectors.toList());
                zoneService.lambdaUpdate()
                        .set(Zone::getStatus, Zone.NOT_RENT)
                        .in(Zone::getId, existedZoneIds);
                leaseContractZoneService.remove(Wrappers.lambdaQuery(LeaseContractZone.class).eq(LeaseContractZone::getContractId, bo.getId()));
            }
            if (CollectionUtils.isNotEmpty(bo.getZoneIds())) {
                zoneService.lambdaUpdate()
                        .set(Zone::getStatus, Zone.RENT)
                        .in(Zone::getId, bo.getZoneIds());
                List<LeaseContractZone> leaseContractZoneList = bo.getZoneIds().stream().map(zoneId -> {
                    LeaseContractZone leaseContractZone = new LeaseContractZone();
                    leaseContractZone.setId(UUID.randomUUID().toString().replace("-", ""));
                    leaseContractZone.setContractId(bo.getId());
                    leaseContractZone.setZoneId(zoneId);
                    return leaseContractZone;
                }).collect(Collectors.toList());
                leaseContractZoneService.saveBatch(leaseContractZoneList);
            }

            // 根据租赁起始日期写入lease_rent表，逐年逐季度生成，精确判断季度区间
            if (existedContract != null) {
                leaseRentService.remove(Wrappers.lambdaQuery(LeaseRent.class).eq(LeaseRent::getContractId, bo.getId()));
            }
            if (contract.getLeaseStartTime() != null && contract.getLeaseEndTime() != null) {
                int startYear = contract.getLeaseStartTime().getYear();
                int endYear = contract.getLeaseEndTime().getYear();
                int startQuarter = (contract.getLeaseStartTime().getMonthValue() - 1) / 3 + 1;
                int endQuarter = (contract.getLeaseEndTime().getMonthValue() - 1) / 3 + 1;
                log.info("lease startYear: {}, startQuarter: {}, endYear: {}, endQuarter: {}", startYear, startQuarter, endYear,endQuarter);
                List<LeaseRent> leaseRents = new ArrayList<>();
                for (int year = startYear; year <= endYear; year++) {
                    int qStart = (year == startYear) ? startQuarter : 1;
                    int qEnd = (year == endYear) ? endQuarter : 4;
                    for (int quarter = qStart; quarter <= qEnd; quarter++) {
                        LeaseRent leaseRent = new LeaseRent();
                        leaseRent.setId(UUID.randomUUID().toString().replace("-", ""));
                        leaseRent.setContractId(contract.getId());
                        leaseRent.setCompanyId(bo.getLesseeId());
                        leaseRent.setYear(year);
                        leaseRent.setQuarter(quarter);
                        leaseRent.setReceivableAmount(BigDecimal.valueOf(contract.getRentalCollect()/4));
                        leaseRents.add(leaseRent);
                    }
                }
                leaseRentService.saveBatch(leaseRents);
            }

            return contract.getId();

        } catch (Exception e) {
            log.error("error occurred add or edit lease contract! ", e);
            throw e;
        }
    }

    @Override
    public LeaseContractVO getLeaseContractDetail(String id) {
        LeaseContractVO vo = new LeaseContractVO();
        LeaseContract contract = this.getById(id);
        if (contract == null) {
            return vo;
        }
        BeanUtils.copyProperties(contract, vo);
        // lesseeId -> lessee
        if (StringUtils.isNotBlank(contract.getLesseeId())) {
            Company company = companyService.getById(contract.getLesseeId());
            if (company != null) {
                vo.setLessee(company.getName());
            }
        }
        // zoneId -> zoneName, parkId, buildId
        List<LeaseContractZone> leaseContractZones = leaseContractZoneService.list(Wrappers.lambdaQuery(LeaseContractZone.class)
                .eq(LeaseContractZone::getContractId, id));
        StringBuilder locationInfo = new StringBuilder();
        if (CollectionUtils.isNotEmpty(leaseContractZones)) {
            List<ContractZoneItemVO> zoneItems = new ArrayList<>();
            List<String> zoneIds = leaseContractZones.stream().map(LeaseContractZone::getZoneId).collect(Collectors.toList());
            List<Zone> zones = zoneService.list(Wrappers.lambdaQuery(Zone.class).in(Zone::getId, zoneIds));
            Map<String, List<Zone>> zoneMap = zones.stream().collect(Collectors.groupingBy(Zone::getBuildId));
            zoneMap.forEach((buildId, zoneList) -> {
                ContractZoneItemVO item = new ContractZoneItemVO();
                Zone temp = zoneList.get(0);
                BeanUtils.copyProperties(temp, item);
                // parkId -> parkName
                if (StringUtils.isNotBlank(temp.getParkId())) {
                    Park park = parkService.getById(temp.getParkId());
                    if (park != null) {
                        locationInfo.append(park.getParkName()).append("-");
                    }
                }
                if (StringUtils.isNotBlank(temp.getBuildId())) {
                    item.setBuildId(temp.getBuildId());
                    Building building = buildingService.getById(temp.getBuildId());
                    if (building != null) {
                        locationInfo.append(building.getBuildingName()).append("-");
                    }
                }
                List<String> zoneItemVOS = zoneList.stream().map(zone -> {
                    locationInfo.append(zone.getZoneName()).append("、");
                    return zone.getId();
                }).collect(Collectors.toList());
                locationInfo.deleteCharAt(locationInfo.lastIndexOf("、"));
                locationInfo.append("；");
                item.setZoneIds(zoneItemVOS);
                zoneItems.add(item);
            });
            vo.setZoneList(zoneItems);
            double sumArea = zones.stream().mapToDouble(zone -> Double.parseDouble(zone.getArea())).sum();
            vo.setArea(String.valueOf(sumArea));
            locationInfo.deleteCharAt(locationInfo.lastIndexOf("；"));
            vo.setLocationInfo(locationInfo.toString());

        }

        // leaseFunctionId -> leaseFunction from InMemoryCache Map
        if (contract.getLeaseFunctionId() != null) {
            String leaseFunctionName = InMemoryCache.getLeaseFunctionMap().get(contract.getLeaseFunctionId());
            vo.setLeaseFunction(leaseFunctionName);
        }
        // decorateStandardId -> decorateStandard from InMemoryCache Map
        if (contract.getDecorationRestrictionId()!= null) {
            String decorateStandardName = InMemoryCache.getDecorateTimeLimitMap().get(contract.getDecorationRestrictionId());
            vo.setDecorationRestriction(decorateStandardName);
        }
        // utilitiesDepositStandardId -> utilitiesDepositStandard from InMemoryCache Map
        if (contract.getUtilitiesDepositStandardId()!= null) {
            String utilitiesDepositStandardName = InMemoryCache.getUtilitiesDepositStandardMap().get(contract.getUtilitiesDepositStandardId());
            vo.setUtilitiesDepositStandard(utilitiesDepositStandardName);
        }
        // firstRentPayTimeId -> firstRentPayTime from InMemoryCache Map
        if (contract.getFirstRentPayTimeId() != null) {
            String firstRentPayTime = InMemoryCache.getFirstRentPayTimeMap().get(contract.getFirstRentPayTimeId());
            vo.setFirstRentPayTime(firstRentPayTime);
        }
        // zoneFunctionId -> zoneFunction from InMemoryCache Map
        if (contract.getZoneFunctionId() != null) {
            String zoneFunction = InMemoryCache.getZoneFunctionMap().get(contract.getZoneFunctionId());
            vo.setZoneFunction(zoneFunction);
        }
        // powerConsumptionStandardId -> powerConsumptionStandard from InMemoryCache Map
        if (contract.getPowerConsumptionStandardId() != null) {
            String powerConsumptionStandard = InMemoryCache.getPowerConsumptionStandardMap().get(contract.getPowerConsumptionStandardId());
            vo.setPowerConsumptionStandard(powerConsumptionStandard);
        }

        vo.setContractScanCopyPath(StringUtils.isNotBlank(contract.getContractScanCopyPath())
                ? downloadAddress + contract.getContractScanCopyPath() : null);
        vo.setInvestAgreementPath(StringUtils.isNotBlank(contract.getInvestAgreementPath())
                ? downloadAddress + contract.getInvestAgreementPath() : null);

        return vo;
    }

    @Override
    public IPage<LeaseContractListVO> pageLeaseContractList(LeaseContractPageQuery query) {
        int pageNum = query.getPageNum() != null ? query.getPageNum() : 1;
        int pageSize = query.getPageSize() != null ? query.getPageSize() : 10;
        int offset = (pageNum - 1) * pageSize;
        List<LeaseContractListVO> records = leaseContractMapper.pageLeaseContractList(offset, pageSize, query);
        long total = leaseContractMapper.countLeaseContractList(query);
        Page<LeaseContractListVO> page = new Page<>(pageNum, pageSize);
        page.setRecords(records);
        page.setTotal(total);
        return page;
    }

    @Override
    public boolean deleteLeaseContractById(String id) {
        leaseRentService.remove(Wrappers.lambdaQuery(LeaseRent.class).eq(LeaseRent::getContractId, id));
        return this.removeById(id);
    }

    @Override
    public String uploadContractScanCopy(MultipartFile file, String contractId) {
        String[] result = fileInfoService.saveFile(file, contractScanCopyDir);
        String fullPath = result[0];
        String originalFilename = result[1];
        LeaseContract contract = this.getById(contractId);
        if (contract == null) {
            throw new IllegalArgumentException("合同不存在");
        }
        contract.setContractScanCopyPath(fullPath);
        contract.setContractScanCopyName(originalFilename);
        this.updateById(contract);
        return fullPath;
    }

    @Override
    public String uploadInvestAgreement(MultipartFile file, String contractId) {
        String[] result = fileInfoService.saveFile(file, investAgreementDir);
        String fullPath = result[0];
        String originalFilename = result[1];
        LeaseContract contract = this.getById(contractId);
        if (contract == null) {
            throw new IllegalArgumentException("合同不存在");
        }
        contract.setInvestAgreementPath(fullPath);
        contract.setInvestAgreementName(originalFilename);
        this.updateById(contract);
        return fullPath;
    }

    @Override
    public void download(String fileId, boolean inline, HttpServletResponse response) {
        fileInfoService.download(fileId, inline, response);
    }

    @Override
    public List<LeaseContractSimpleVO> getContractIdAndNoByLesseeId(String lesseeId) {
        List<LeaseContract> contracts = this.lambdaQuery()
                .eq(LeaseContract::getLesseeId, lesseeId)
                .select(LeaseContract::getId, LeaseContract::getContractNo)
                .list();
        return contracts.stream().map(contract -> {
            LeaseContractSimpleVO vo = new LeaseContractSimpleVO();
            BeanUtils.copyProperties(contract, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void downloadLeaseContract(String contractId, HttpServletResponse response) throws Exception {
        if (StringUtils.isBlank(contractId)) {
            throw new IllegalArgumentException("合同ID不能为空");
        }

        log.info("开始下载租赁合同, contractId: {}", contractId);

        // 查询合同详情
        LeaseContractVO contractVO = this.getLeaseContractDetail(contractId);
        if (contractVO == null) {
            throw new IllegalArgumentException("合同不存在");
        }

        // 获取公司信息
        Company company = companyService.getById(contractVO.getLesseeId());
        if (company == null) {
            throw new IllegalArgumentException("承租方信息不存在");
        }

        // 获取模板文件路径
        String templatePath = "templates/租赁协议模板.docx";
        // 设置文件名
        String fileName = company.getName() + "-租赁合同.docx";
        
        log.info("使用模板: {}, 生成文件: {}", templatePath, fileName);
        
        // 准备替换参数
        Map<String, String> params = new HashMap<>();
        
        // 基本合同信息
        params.put("contractNo", contractVO.getContractNo());
        
        // 承租方信息
        params.put("companyName", company.getName());
        params.put("address", contractVO.getAddress());
        params.put("legalPerson", contractVO.getLegalPerson());
        params.put("creditCode", contractVO.getCreditCode());
        params.put("contactAddress", contractVO.getContactAddress());
        params.put("postalCode", contractVO.getPostalCode());
        params.put("tel", contractVO.getTel());
        params.put("fax", contractVO.getFax());
        params.put("email", contractVO.getEmail());

        // 获取园区及区域信息
        params.put("location", contractVO.getLocationInfo());

        //面积
        params.put("area", contractVO.getArea());
        
        // 房屋用途
        if (contractVO.getZoneFunctionId() != null) {
            String zoneFunction = InMemoryCache.getZoneFunctionMap().get(contractVO.getZoneFunctionId());
            params.put("zoneFunction", zoneFunction);
        }
        
        // 设计标准用电量
        if (contractVO.getPowerConsumptionStandardId() != null) {
            String powerConsumptionStandard = InMemoryCache.getPowerConsumptionStandardMap().get(contractVO.getPowerConsumptionStandardId());
            params.put("powerConsumptionStandard", powerConsumptionStandard);
        }
        
        // 租赁期限信息
        params.put("leaseTerm", String.valueOf(contractVO.getLeaseTerm()));
        
        // 租赁功能
        if (contractVO.getLeaseFunctionId() != null) {
            String leaseFunction = InMemoryCache.getLeaseFunctionMap().get(contractVO.getLeaseFunctionId());
            params.put("leaseFunction", leaseFunction);
        }
        
        // 租赁时间
        if (contractVO.getLeaseStartTime() != null) {
            params.put("leaseStartYear", String.valueOf(contractVO.getLeaseStartTime().getYear()));
            params.put("leaseStartMonth", String.valueOf(contractVO.getLeaseStartTime().getMonthValue()));
            params.put("leaseStartDay", String.valueOf(contractVO.getLeaseStartTime().getDayOfMonth()));
        }
        
        if (contractVO.getLeaseEndTime() != null) {
            params.put("leaseEndYear", String.valueOf(contractVO.getLeaseEndTime().getYear()));
            params.put("leaseEndMonth", String.valueOf(contractVO.getLeaseEndTime().getMonthValue()));
            params.put("leaseEndDay", String.valueOf(contractVO.getLeaseEndTime().getDayOfMonth()));
        }
        
        if (contractVO.getLeaseDeliveryTime() != null) {
            params.put("deliveryYear", String.valueOf(contractVO.getLeaseDeliveryTime().getYear()));
            params.put("deliveryMonth", String.valueOf(contractVO.getLeaseDeliveryTime().getMonthValue()));
            params.put("deliveryDay", String.valueOf(contractVO.getLeaseDeliveryTime().getDayOfMonth()));
        }
        
        // 租金信息
        params.put("rentalStandard", String.valueOf(contractVO.getRentalStandard()));
        
        if (contractVO.getRentalCollect() != null) {
            params.put("rentalCollect", String.valueOf(contractVO.getRentalCollect()));
            params.put("rentalCollectChinese", MoneyUtils.toChineseSimple(contractVO.getRentalCollect()));
        }
        
        // 首期租金支付时间
        if (contractVO.getFirstRentPayTimeId() != null) {
            String firstRentPayTime = InMemoryCache.getFirstRentPayTimeMap().get(contractVO.getFirstRentPayTimeId());
            params.put("firstRentPayTime", firstRentPayTime);
        }
        
        // 装修期限信息
        params.put("decorationRestriction", contractVO.getDecorationRestriction());
        if (contractVO.getDecorateStartTime() != null) {
            params.put("decorateStartYear", String.valueOf(contractVO.getDecorateStartTime().getYear()));
            params.put("decorateStartMonth", String.valueOf(contractVO.getDecorateStartTime().getMonthValue()));
            params.put("decorateStartDay", String.valueOf(contractVO.getDecorateStartTime().getDayOfMonth()));
        }
        
        if (contractVO.getDecorateEndTime() != null) {
            params.put("decorateEndYear", String.valueOf(contractVO.getDecorateEndTime().getYear()));
            params.put("decorateEndMonth", String.valueOf(contractVO.getDecorateEndTime().getMonthValue()));
            params.put("decorateEndDay", String.valueOf(contractVO.getDecorateEndTime().getDayOfMonth()));
        }
        
        // 押金信息
        params.put("utilitiesDepositStandard", contractVO.getUtilitiesDepositStandard());
        params.put("utilitiesDeposit", contractVO.getUtilitiesDeposit());
        
        params.put("propertyManageFeeStandard", contractVO.getPropertyManageFeeStandard());
        params.put("propertyManageFeeDeposit", contractVO.getPropertyManageFeeDeposit());
        
        if (StringUtils.isNotBlank(contractVO.getLeaseDeposit())) {
            params.put("leaseDeposit", contractVO.getLeaseDeposit());
            try {
                params.put("leaseDepositChinese", MoneyUtils.toChineseSimple(Integer.parseInt(contractVO.getLeaseDeposit())));
            } catch (NumberFormatException e) {
                log.warn("无法转换租赁保证金为数字: {}", contractVO.getLeaseDeposit());
            }
        }
        
        // 续签相关押金
        params.put("renewUtilitiesDeposit", contractVO.getRenewUtilitiesDeposit());
        params.put("renewPropertyManageDeposit", contractVO.getRenewPropertyManageDeposit());
        
        if (StringUtils.isNotBlank(contractVO.getRenewLeaseDeposit())) {
            params.put("renewLeaseDeposit", contractVO.getRenewLeaseDeposit());
            try {
                params.put("renewLeaseDepositChinese", MoneyUtils.toChineseSimple(Integer.parseInt(contractVO.getRenewLeaseDeposit())));
            } catch (NumberFormatException e) {
                log.warn("无法转换续签租赁保证金为数字: {}", contractVO.getRenewLeaseDeposit());
            }
        }
        
        // 合同附加备注
        if (StringUtils.isNotBlank(contractVO.getRemark())) {
            params.put("remark", contractVO.getRemark());
        }
        
        // 其他信息
        if (contractVO.getProcedureHandleTime() != null) {
            params.put("procedureYear", String.valueOf(contractVO.getProcedureHandleTime().getYear()));
            params.put("procedureMonth", String.valueOf(contractVO.getProcedureHandleTime().getMonthValue()));
            params.put("procedureDay", String.valueOf(contractVO.getProcedureHandleTime().getDayOfMonth()));
        }
        
        if (contractVO.getProcedureHandleEndTime() != null) {
            params.put("procedureEndYear", String.valueOf(contractVO.getProcedureHandleEndTime().getYear()));
            params.put("procedureEndMonth", String.valueOf(contractVO.getProcedureHandleEndTime().getMonthValue()));
            params.put("procedureEndDay", String.valueOf(contractVO.getProcedureHandleEndTime().getDayOfMonth()));
        }
        
        // 输出参数用于调试
        WordUtils.logParams(params);
        
        // 获取资源文件
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath)) {
            if (is == null) {
                log.error("模板文件不存在: {}", templatePath);
                throw new java.io.FileNotFoundException("模板文件不存在: " + templatePath);
            }
            
            log.info("成功读取模板文件: {}", templatePath);
            
            // 首先生成Word文档到内存中
            ByteArrayOutputStream wordOutput = new ByteArrayOutputStream();
            WordUtils.replaceTextInDocument(is, wordOutput, params);
            log.info("租赁合同Word文件生成完成");
            
            // 将Word转换为PDF
            ByteArrayInputStream wordInputStream = new ByteArrayInputStream(wordOutput.toByteArray());
            ByteArrayOutputStream pdfOutput = new ByteArrayOutputStream();

            log.info("开始将Word转换为PDF");
            try {
                // 使用自定义的DocToPdfConverter工具类进行转换
                DocToPdfConverter.convert(wordInputStream, pdfOutput);
                log.info("Word转PDF转换完成");
            } catch (Exception e) {
                log.error("Word转PDF失败", e);
                throw new RuntimeException("Word转PDF失败: " + e.getMessage(), e);
            }

            // 设置PDF响应头
            String pdfFileName = company.getName() + "-租赁合同.pdf";
            response.setContentType("application/pdf");
            response.setCharacterEncoding("UTF-8");
            String encodedFilename = URLEncoder.encode(pdfFileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFilename);
            
            // 输出PDF到响应
            byte[] pdfBytes = pdfOutput.toByteArray();
//            byte[] pdfBytes = wordOutput.toByteArray();
            response.getOutputStream().write(pdfBytes);
            log.info("PDF文件下载完成，大小: {} 字节", pdfBytes.length);
        }
    }
}
