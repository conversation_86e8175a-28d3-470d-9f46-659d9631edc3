package com.quantchi.common.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2022/2/25 14:13
 */
@Slf4j
@Configuration
public class EsClientConfig {

    @Value("${es.cluster.hosts}")
    private String hosts;

    @Value("${es.username}")
    private String userName;

    @Value("${es.password}")
    private String password;

    // 设置连接超时时间
    private final int connectTimeOut = 2000;
    private final int socketTimeOut = 100000;
    private final int connectionRequestTimeOut = 10000;
    // 一次最多接收请求
    private final int maxConnectNum = 100;
    // 某一个服务每次能并行接收的请求数量
    private final int maxConnectPerRoute = 100;


    /**
     * 初始化连接
     *
     * @return
     */
    @Bean(name = "restHighLevelClient")
    public ElasticsearchClient getClientBuilder() {
        log.info("Initializing Elasticsearch client with hosts: {}", hosts);
        
        // 解析配置的主机地址
        String[] hostArray = hosts.split(",");
        HttpHost[] httpHosts = new HttpHost[hostArray.length];
        for (int i = 0; i < hostArray.length; i++) {
            httpHosts[i] = HttpHost.create(hostArray[i]);
        }
        
        // 创建 RestClientBuilder
        RestClientBuilder builder = RestClient.builder(httpHosts);
        
        // 配置身份验证
        if (userName != null && !userName.isEmpty() && password != null && !password.isEmpty()) {
            log.info("Using basic authentication for Elasticsearch");
            builder.setDefaultHeaders(new Header[]{
                    new BasicHeader("Authorization", "Basic " + 
                            java.util.Base64.getEncoder().encodeToString(
                                    (userName + ":" + password).getBytes()))
            });
        }
        
        // 配置连接参数
        builder.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                .setConnectTimeout(connectTimeOut)
                .setSocketTimeout(socketTimeOut)
                .setConnectionRequestTimeout(connectionRequestTimeOut));
        
        // 配置连接池
        builder.setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder
                .setMaxConnTotal(maxConnectNum)
                .setMaxConnPerRoute(maxConnectPerRoute));
        
        // 创建低级客户端
        RestClient restClient = builder.build();

        // Create the transport with a Jackson mapper
        final ElasticsearchTransport transport = new RestClientTransport(
                restClient, new JacksonJsonpMapper());
        return new ElasticsearchClient(transport);
    }

}
