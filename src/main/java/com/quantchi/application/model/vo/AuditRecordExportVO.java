package com.quantchi.application.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 审批记录Excel导出VO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@ApiModel(value = "审批记录Excel导出VO")
@ContentRowHeight(20)
@HeadRowHeight(25)
public class AuditRecordExportVO {

    @ExcelProperty(value = "申请单位", index = 0)
    @ColumnWidth(30)
    @ApiModelProperty("申请单位")
    private String applicantName;

    @ExcelProperty(value = "申请事项", index = 1)
    @ColumnWidth(15)
    @ApiModelProperty("申请事项")
    private String applyTypeName;

    @ExcelProperty(value = "发起人", index = 2)
    @ColumnWidth(15)
    @ApiModelProperty("发起人")
    private String applicant;

    @ExcelProperty(value = "联系方式", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("联系方式")
    private String contactInfo;

    @ExcelProperty(value = "发起时间", index = 4)
    @ColumnWidth(15)
    @ApiModelProperty("发起时间")
    private String applyTime;

    @ExcelProperty(value = "当前进度", index = 5)
    @ColumnWidth(15)
    @ApiModelProperty("当前进度")
    private String statusName;
}
