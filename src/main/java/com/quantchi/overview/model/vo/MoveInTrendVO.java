package com.quantchi.overview.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业入驻增长趋势数据对象
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@ApiModel(value = "MoveInTrendVO", description = "企业入驻增长趋势数据对象")
public class MoveInTrendVO {

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("企业数量")
    private Integer companyCount;
}
