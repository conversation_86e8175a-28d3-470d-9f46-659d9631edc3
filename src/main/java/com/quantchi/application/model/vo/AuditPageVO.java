package com.quantchi.application.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 审批管理分页VO
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@ApiModel(value = "审批管理分页VO")
public class AuditPageVO {

    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private Long total;

    /**
     * 每页记录数
     */
    @ApiModelProperty("每页记录数")
    private Integer pageSize;

    /**
     * 当前页码
     */
    @ApiModelProperty("当前页码")
    private Integer pageNum;

    /**
     * 总页数
     */
    @ApiModelProperty("总页数")
    private Long pages;

    /**
     * 记录列表
     */
    @ApiModelProperty("记录列表")
    private List<AuditRecordVO> records;
}
