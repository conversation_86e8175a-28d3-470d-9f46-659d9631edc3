package com.quantchi.common.enums;

import com.quantchi.common.exception.BusinessException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/5/22
 * es 索引
 */
@Getter
public enum EsIndexEnum {

    COMPANY("company", "企业", "nanjing_complex_company", "_doc", "text/keyword:name", "establish_date", null, "score:desc", "name.suggest"),
    NEWS("news", "资讯", "nanjing_complex_news", "_doc", "text/keyword:name", "publish_date:desc", "id,title,images_url,url,channel,publish_date", "score:desc", "name.suggest"),
    ;

    private final String index;

    private final String indexName;

    private final String esIndex;

    private final String esType;

    /**
     * 标题对应字段及格式
     */
    private final String titleColumn;

    private final String sort;

    private final String includeFields;

    /**
     * 挂接库搜索时的搜索顺序
     */
    private final String relateSort;

    private final String titleSuggestField;

    EsIndexEnum(final String index, final String indexName, final String esIndex, final String esType, final String titleColumn, final String sort, final String includeFields, final String relateSort, final String titleSuggestField) {
        this.index = index;
        this.indexName = indexName;
        this.esIndex = esIndex;
        this.esType = esType;
        this.titleColumn = titleColumn;
        this.sort = sort;
        this.includeFields = includeFields;
        this.relateSort = relateSort;
        this.titleSuggestField = titleSuggestField;
    }

    public static EsIndexEnum getIndexEnumByEsIndex(final String esIndex) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getEsIndex().equals(esIndex)) {
                return value;
            }
        }
        return null;
    }

    public static EsIndexEnum getIndexEnumByType(final String index) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(index)) {
                return value;
            }
        }
        return null;
    }

    public static String getTitleSuggestFieldByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(type)) {
                return value.getTitleSuggestField();
            }
        }
        throw new BusinessException("这个类型" + type + "找不到对应的实际索引");
    }

    public static String getEsIndexByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(type)) {
                return value.getEsIndex();
            }
        }
        return type;
    }

    public static String getTypeByEsIndex(final String esIndex) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getEsIndex().equals(esIndex)) {
                return value.getIndex();
            }
        }
        throw new BusinessException("这个类型" + esIndex + "找不到对应的实际索引");
    }

    public static String getIndexNameByType(final String type) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(type)) {
                return value.getIndexName();
            }
        }
        return null;
    }

    public static String getIndexNameByIndex(final String index) {
        for (final EsIndexEnum value : EsIndexEnum.values()) {
            if (value.getIndex().equals(index)) {
                return value.getIndexName();
            }
        }
        return null;
    }

}
