package com.quantchi.application.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 企业联系人列表分页VO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@ApiModel(value = "企业联系人列表分页VO")
public class ContactListPageVO {

    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private Long total;

    /**
     * 每页记录数
     */
    @ApiModelProperty("每页记录数")
    private Integer pageSize;

    /**
     * 当前页码
     */
    @ApiModelProperty("当前页码")
    private Integer pageNum;

    /**
     * 总页数
     */
    @ApiModelProperty("总页数")
    private Long pages;

    /**
     * 联系人列表
     */
    @ApiModelProperty("联系人列表")
    private List<CompanyContactVO> records;
}
