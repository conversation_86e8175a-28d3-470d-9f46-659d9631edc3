package com.quantchi.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.quantchi.common.domain.bo.ActualQueryTimeBO;
import com.quantchi.common.domain.vo.StartEndVO;
import com.quantchi.common.exception.BusinessException;
import io.swagger.annotations.ApiModel;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static cn.hutool.core.date.DatePattern.NORM_MONTH_PATTERN;

/**
 * <AUTHOR>
 * @date 2023/2/10 10:07
 */
@ApiModel("日期业务工具类")
public class DateHandlerUtil {

    public static boolean isInTimeRange(final String time, final String startTime, final String endTime) {
        final boolean startFlag;
        if (CharSequenceUtil.isBlank(startTime)) {
            startFlag = true;
        } else {
            startFlag = time.compareTo(startTime) >= 0;
        }
        final boolean endFlag;
        if (CharSequenceUtil.isBlank(endTime)) {
            endFlag = true;
        } else {
            endFlag = time.compareTo(endTime) <= 0;
        }
        return startFlag && endFlag;
    }

    /**
     * 根据时间维度对日期进行格式化
     */
    public static StartEndVO getDateFromTimeDimension(final String timeDimension, final String timeField, final Boolean calculateNew) {
        final StartEndVO vo = new StartEndVO();
        final String endDate;
        String startDate = null;
        switch (timeDimension) {
            case "年":
                endDate = timeField + "-12-31";
                if (calculateNew) {
                    startDate = timeField + "-01-01";
                }
                break;
            case "季":
                // 2021-Q1 -> 2021-03-31
                endDate = timeField.replace("Q1", "03-31")
                        .replace("Q2", "06-30")
                        .replace("Q3", "09-30")
                        .replace("Q4", "12-31");
                if (calculateNew) {
                    startDate = timeField.replace("Q1", "01-01")
                            .replace("Q2", "04-01")
                            .replace("Q3", "07-01")
                            .replace("Q4", "10-01");
                }
                break;
            case "月":
                endDate = timeField + "-31";
                if (calculateNew) {
                    startDate = timeField + "-01";
                }
                break;
            default:
                throw new BusinessException("时间维度不正确");
        }
        vo.setStartDate(startDate);
        vo.setEndDate(endDate);
        return vo;
    }

    /**
     * 输入起始时间、结束时间以及对应的日期类型，返回yyyy-MM-dd HH:mm:ss格式的起始和结束时间
     *
     * @param startTime
     * @param endTime
     * @param filterType
     * @return
     */
    public static ActualQueryTimeBO getActualQueryTime(final String startTime, final String endTime, final String filterType) {
        final ActualQueryTimeBO res = new ActualQueryTimeBO();
        final Date startDate;
        final Date endDate;
        final DateTime startDateTime;
        final DateTime endDateTime;
        switch (filterType) {
            case "day":
                startDate = DateUtil.parse(startTime, NORM_DATE_PATTERN);
                endDate = DateUtil.parse(endTime, NORM_DATE_PATTERN);
                startDateTime = DateUtil.beginOfDay(startDate);
                endDateTime = DateUtil.endOfDay(endDate);
                res.setStartTime(DateUtil.format(startDateTime, DatePattern.NORM_DATETIME_PATTERN));
                res.setEndTime(DateUtil.format(endDateTime, DatePattern.NORM_DATETIME_FORMAT));
                res.setIntervalDateList(getDays(startTime, endTime));
                res.setOriginDatePattern(NORM_DATE_PATTERN);
                return res;
            case "month":
                startDate = DateUtil.parse(startTime, NORM_MONTH_PATTERN);
                endDate = DateUtil.parse(endTime, NORM_MONTH_PATTERN);
                startDateTime = DateUtil.beginOfMonth(startDate);
                endDateTime = DateUtil.endOfMonth(endDate);
                res.setStartTime(DateUtil.format(startDateTime, DatePattern.NORM_DATETIME_PATTERN));
                res.setEndTime(DateUtil.format(endDateTime, DatePattern.NORM_DATETIME_FORMAT));
                res.setIntervalDateList(getMonths(startTime, endTime));
                res.setOriginDatePattern(NORM_MONTH_PATTERN);
                return res;
            case "year":
                startDate = DateUtil.parse(startTime, "yyyy");
                endDate = DateUtil.parse(endTime, "yyyy");
                startDateTime = DateUtil.beginOfYear(startDate);
                endDateTime = DateUtil.endOfYear(endDate);
                res.setStartTime(DateUtil.format(startDateTime, DatePattern.NORM_DATETIME_PATTERN));
                res.setEndTime(DateUtil.format(endDateTime, DatePattern.NORM_DATETIME_FORMAT));
                res.setOriginDatePattern("yyyy");
                res.setIntervalDateList(getYears(startTime, endTime));
                return res;
            default:
                res.setStartTime(startTime);
                res.setEndTime(endTime);
                return res;
        }
    }

    /**
     * 获取时间段内的所有日期
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getDays(final String startTime, final String endTime) {
        // 返回的日期集合
        final List<String> days = new ArrayList<String>();
        final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            final Date start = dateFormat.parse(startTime);
            final Date end = dateFormat.parse(endTime);
            final Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            final Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
        } catch (final Exception e) {
            e.printStackTrace();
        }
        return days;
    }

    /**
     * 获取时间段内的所有月份
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getMonths(final String startTime, final String endTime) {
        // 返回的日期集合
        final List<String> months = new ArrayList<String>();
        final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        try {
            final Date start = dateFormat.parse(startTime);
            final Date end = dateFormat.parse(endTime);

            final Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            final Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.MONTH, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                months.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.MONTH, 1);
            }
        } catch (final Exception e) {
            e.printStackTrace();
        }
        return months;
    }

    public static final Integer MONTH_OFFSET = 6;

    /**
     * 获取最近十年的年份
     *
     * @return
     */
    public static List<String> getLatest10Year() {
         int currentYear = DateUtil.year(new Date());
        final int currentMonth = DateUtil.month(new Date());
        if (currentMonth <= MONTH_OFFSET) {
            currentYear = currentYear - 1;
        }
        // 6月之后再放开显示当年的数据
        return DateHandlerUtil.getYears(String.valueOf(currentYear - 9), String.valueOf(currentYear));
    }

    public static List<String> getLatestTimeField(final String timeDimension, final Integer yearOffset) {
        int currentYear = DateUtil.year(new Date());
        final int currentMonth = DateUtil.month(new Date());
        if (currentMonth <= MONTH_OFFSET) {
            currentYear = currentYear - 1;
        }
        switch (timeDimension) {
            case "年":
                return DateHandlerUtil.getYears(String.valueOf(currentYear - yearOffset), String.valueOf(currentYear));
            case "季":
                return DateHandlerUtil.getQuarters(String.valueOf(currentYear - yearOffset), String.valueOf(currentYear));
            case "月":
                return DateHandlerUtil.getMonths(currentYear - yearOffset + "-01", currentYear + "-12");
            default:
                return Collections.emptyList();
        }
    }

    /**
     * 获取时间段内的所有年份
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getYears(final String startTime, final String endTime) {
        // 返回的日期集合
        final List<String> years = new ArrayList<String>();
        final DateFormat dateFormat = new SimpleDateFormat("yyyy");
        try {
            final Date start = dateFormat.parse(startTime);
            final Date end = dateFormat.parse(endTime);

            final Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            final Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.YEAR, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                years.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.YEAR, 1);
            }
        } catch (final Exception e) {
            e.printStackTrace();
        }
        return years;
    }

    /**
     * 将LocalDateTime转为yyyy-MM-dd格式的字符串
     */
    public static String toYMD(LocalDate dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    public static String formatTagDuration(LocalDate startTime, LocalDate endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");

        String start = "";
        String end = "";

        if (startTime != null) {
            start = startTime.format(formatter);
        }

        if (endTime != null) {
            end = endTime.format(formatter);
        }

        return start + "-" + end;
    }

    public static void main(final String[] args) {
        System.out.println(getQuarters("2015", "2017"));
        System.out.println(getMonths("2015-01", "2017-12"));
    }

    public static List<String> getQuarters(final String startTime, final String endTime) {
        // 返回的季度集合
        final List<String> quarters = new ArrayList<>();
        final DateFormat dateFormat = new SimpleDateFormat("yyyy");

        try {
            final Date start = new SimpleDateFormat("yyyy").parse(startTime);
            final Date end = new SimpleDateFormat("yyyy").parse(endTime);

            final Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            final Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.MONTH, 12); // 日期加12个月 (包含结束的季度)
            int quartz = 1;

            while (tempStart.before(tempEnd)) {
                // 计算季度
                quarters.add(dateFormat.format(tempStart.getTime()) + "-Q" + quartz);
                quartz++;
                tempStart.add(Calendar.MONTH, 3); // 每次增加3个月，跳到下一个季度
                if (quartz > 4) {
                    quartz = 1;
                }
            }
        } catch (final Exception e) {
            e.printStackTrace();
        }
        return quarters;
    }


}
