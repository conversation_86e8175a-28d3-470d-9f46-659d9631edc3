package com.quantchi.application.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.application.model.bo.ApplicationDecorationBO;
import com.quantchi.application.model.entity.*;
import com.quantchi.application.mapper.ApplicationDecorationMapper;
import com.quantchi.application.model.enums.ApplicationStatus;
import com.quantchi.application.model.enums.ApplyType;
import com.quantchi.application.model.enums.FileTypeEnum;
import com.quantchi.application.model.enums.ProcessStatus;
import com.quantchi.application.model.vo.ApplicationDecorationVO;
import com.quantchi.application.model.vo.ProcessVO;
import com.quantchi.application.service.IApplicationDecorationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.application.service.IApplicationFileService;
import com.quantchi.application.service.IApplicationProcessService;
import com.quantchi.application.service.IApplicationService;
import com.quantchi.common.model.FileInfo;
import com.quantchi.common.service.impl.FileInfoServiceImpl;
import com.quantchi.company.model.entity.Company;
import com.quantchi.company.service.ICompanyService;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.quantchi.common.config.properties.AliyunProperties;
import com.quantchi.sys.mapper.SysUserMapper;
import com.quantchi.sys.service.impl.CaptchaSendService;
import org.apache.commons.io.IOUtils;
import com.quantchi.sys.model.entity.SysUser;
import com.quantchi.sys.service.impl.SysUserService;
import com.quantchi.sys.utils.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.quantchi.application.model.entity.ApplicationFile.handleFile;
import static com.quantchi.common.model.FileInfo.getFileInfos;

/**
 * <p>
 * 装修申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@Slf4j
public class ApplicationDecorationServiceImpl extends ServiceImpl<ApplicationDecorationMapper, ApplicationDecoration> implements IApplicationDecorationService {

    @Autowired
    private IApplicationService applicationService;
    @Autowired
    private IApplicationProcessService processService;
    @Autowired
    private FileInfoServiceImpl fileInfoService;
    @Autowired
    private IApplicationFileService applicationFileService;
    @Autowired
    private FileInfoServiceImpl fileService;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private OSS ossClient;
    @Autowired
    private AliyunProperties ossProperties;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private CaptchaSendService captchaSendService;

    @Value("${self.address}")
    private String downloadAddress;
    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${file.save.choose}")
    private int fileSaveChoose;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applicationDecorate(ApplicationDecorationBO bo) {

        try {

            if (StringUtils.isBlank(bo.getCompanyId()) && StringUtils.isNotBlank(bo.getCompanyName())) {
                Company company = new Company();
                company.setName(bo.getCompanyName());
                bo.setCompanyId(companyService.addCompanyInfo(company));
                if (StringUtils.isBlank(bo.getCompanyId())) {
                    throw new IllegalArgumentException("企业创建失败");
                }
            }

            String user = LoginHelper.getUsername();
            // 1. 保存主表 Application
            Application application = new Application();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                BeanUtils.copyProperties(bo, application);
                application.setId(UUID.randomUUID().toString().replace("-", ""));
                application.setApplicantId(StpUtil.getLoginIdAsString());
                application.setApprover(bo.getApprover());
                application.setApplyType(Application.DECORATE);
                application.setApplyTime(LocalDate.now());
                long count = applicationService.count(Wrappers.lambdaQuery(Application.class).eq(Application::getApplyType, Application.DECORATE));
                application.setApplyCode(LocalDate.now().getYear() + "_" + (count + 1));
                application.setStatus(Application.PROCESSING);
                application.setCreateTime(LocalDateTime.now());
                application.setUpdateTime(LocalDateTime.now());
                application.setCreateUser(user);
                application.setUpdateUser(user);
                applicationService.save(application);
            }else {
                List<Application> list = applicationService.list(Wrappers.lambdaQuery(Application.class)
                        .eq(Application::getId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请不存在");
                }
                application = list.get(0);
                application.setApprover(bo.getApprover());
                application.setStatus(Application.PROCESSING);
                application.setUpdateTime(LocalDateTime.now());
                application.setUpdateUser(user);
                applicationService.updateById(application);
            }
            String mainId = application.getId();

            //2. 保存装修申请表 ApplicationDecoration
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                this.remove(Wrappers.lambdaQuery(ApplicationDecoration.class)
                        .eq(ApplicationDecoration::getApplicationId, bo.getApplicationId()));
            }
            ApplicationDecoration decoration = new ApplicationDecoration();
            BeanUtils.copyProperties(bo, decoration);
            decoration.setId(UUID.randomUUID().toString().replace("-", ""));
            decoration.setApplicationId(mainId);
            decoration.setCreateUser(user);
            decoration.setCreateTime(LocalDateTime.now());
            this.save(decoration);

            //3. 保存流程 ApplicationProcess
            ApplicationProcess process = new ApplicationProcess();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                process.setId(UUID.randomUUID().toString().replace("-", ""));
                process.setApplicationId(mainId);
                process.setProcessor(user);
                process.setProcess(ApplicationProcess.START_APPLY);
                process.setProcessTime(LocalDate.now());
                process.setCreateTime(LocalDateTime.now());
                process.setCreateUser(user);
                processService.save(process);
            } else {
                List<ApplicationProcess> list = processService.list(Wrappers.lambdaQuery(ApplicationProcess.class)
                        .eq(ApplicationProcess::getApplicationId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请流程不存在");
                }
                process = list.get(0);
                process.setUpdateTime(LocalDateTime.now());
                process.setUpdateUser(user);
                processService.updateById(process);
            }

            //4. 保存文件
            List<ApplicationFile> applicationFiles = new ArrayList<>();
            List<FileInfo> currentFiles = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(bo.getDecorateApplyInstructions())) {
                for (FileInfo fileInfo : bo.getDecorateApplyInstructions()) {
                    fileInfo.setDownloadUrl(fileInfo.getDownloadUrl().replace(downloadAddress, ""));
                }
                currentFiles.addAll(bo.getDecorateApplyInstructions());
                applicationFiles.addAll(bo.getDecorateApplyInstructions().stream().filter(Objects::nonNull)
                        .map(f -> handleFile(ApplicationFile.DECORATE_APPLY, mainId, user, f))
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(bo.getBusinessLicenses())) {
                for (FileInfo fileInfo : bo.getBusinessLicenses()) {
                    fileInfo.setDownloadUrl(fileInfo.getDownloadUrl().replace(downloadAddress, ""));
                }
                currentFiles.addAll(bo.getBusinessLicenses());
                applicationFiles.addAll(bo.getBusinessLicenses().stream().filter(Objects::nonNull)
                        .map(f -> handleFile(ApplicationFile.BUSINESS_LICENSE, mainId, user, f))
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(bo.getLeaseContracts())) {
                for (FileInfo fileInfo : bo.getLeaseContracts()) {
                    fileInfo.setDownloadUrl(fileInfo.getDownloadUrl().replace(downloadAddress, ""));
                }
                currentFiles.addAll(bo.getLeaseContracts());
                applicationFiles.addAll(bo.getLeaseContracts().stream().filter(Objects::nonNull)
                        .map(f -> handleFile(ApplicationFile.LEASE_CONTRACT, mainId, user, f))
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(bo.getDecorateUndertakings())) {
                for (FileInfo fileInfo : bo.getDecorateUndertakings()) {
                    fileInfo.setDownloadUrl(fileInfo.getDownloadUrl().replace(downloadAddress, ""));
                }
                currentFiles.addAll(bo.getDecorateUndertakings());
                applicationFiles.addAll(bo.getDecorateUndertakings().stream().filter(Objects::nonNull)
                        .map(f -> handleFile(ApplicationFile.DECORATE_UNDERTAKING, mainId, user, f))
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(bo.getDesignDrawings())) {
                for (FileInfo fileInfo : bo.getDesignDrawings()) {
                    fileInfo.setDownloadUrl(fileInfo.getDownloadUrl().replace(downloadAddress, ""));
                }
                currentFiles.addAll(bo.getDesignDrawings());
                applicationFiles.addAll(bo.getDesignDrawings().stream().filter(Objects::nonNull)
                        .map(f -> handleFile(ApplicationFile.DESIGN_DRAWING, mainId, user, f))
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(bo.getDecorateCompanies())) {
                for (FileInfo fileInfo : bo.getDecorateCompanies()) {
                    fileInfo.setDownloadUrl(fileInfo.getDownloadUrl().replace(downloadAddress, ""));
                }
                currentFiles.addAll(bo.getDecorateCompanies());
                applicationFiles.addAll(bo.getDecorateCompanies().stream().filter(Objects::nonNull)
                        .map(f -> handleFile(ApplicationFile.DECORATE_COMPANY, mainId, user, f))
                        .collect(Collectors.toList()));
            }

            List<String> preFileIds = new ArrayList<>();
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                List<ApplicationFile> list = applicationFileService.list(Wrappers.lambdaQuery(ApplicationFile.class)
                        .select(ApplicationFile::getFileId)
                        .eq(ApplicationFile::getApplicationId, bo.getApplicationId()));
                if (CollectionUtils.isNotEmpty(list)) {
                    preFileIds = list.stream().map(ApplicationFile::getFileId).collect(Collectors.toList());
                }
            }

            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                // 更新模式：只移除被删除的文件，不插入新增的FileInfo，但需要保存新增的ApplicationFile
                if (CollectionUtils.isNotEmpty(preFileIds)) {
                    // 获取BO中所有文件的ID列表
                    List<String> currentFileIds = currentFiles.stream()
                            .filter(Objects::nonNull)
                            .map(FileInfo::getFileId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());

                    // 找出已被删除的文件ID（在preFileIds中但不在currentFileIds中的文件）
                    List<String> deletedFileIds = preFileIds.stream()
                            .filter(fileId -> !currentFileIds.contains(fileId))
                            .collect(Collectors.toList());

                    // 只删除已被删除的文件
                    if (CollectionUtils.isNotEmpty(deletedFileIds)) {
                        fileService.remove(Wrappers.lambdaQuery(FileInfo.class).in(FileInfo::getFileId, deletedFileIds));
                    }

                    applicationFileService.remove(Wrappers.lambdaQuery(ApplicationFile.class).in(ApplicationFile::getFileId, preFileIds));
                }
            }

            applicationFileService.saveBatch(applicationFiles);

            SysUser sysUser = sysUserService.selectUserById(Long.valueOf(application.getApplicantId()));
            captchaSendService.sendApplicationByAliyun(sysUser.getPhonenumber(), bo.getCompanyName(), "SMS_489645467");

        } catch (Exception e) {
            log.error("error occurred when submit decorate application: ", e);
            throw e;
        }

    }

    @Override
    public ApplicationDecorationVO applicationDecorateDetail(String applicationId) {
        try {
            if (StringUtils.isBlank(applicationId)) {
                throw new IllegalArgumentException("申请ID不能为空");
            }
            
            ApplicationDecorationVO vo = new ApplicationDecorationVO();
            
            // 1. 查询申请主表
            Application application = applicationService.getOne(
                new LambdaQueryWrapper<Application>()
                    .eq(Application::getId, applicationId)
                    .last("limit 1")
            );
            
            if (application == null) {
                throw new IllegalArgumentException("该申请不存在");
            }
            
            // 设置申请基本信息
            BeanUtils.copyProperties(application, vo);
            vo.setApplicationId(application.getId());
            vo.setApplyTypeCN(application.getApplyType() != null ? ApplyType.getInfoByCode(application.getApplyType()) : null);
            vo.setStatusCN(application.getStatus() != null ? ApplicationStatus.getInfoByCode(application.getStatus()) : null);

            // 查询审批人姓名
            if (application.getApprover() != null) {
                SysUser approver = sysUserMapper.selectById(application.getApprover());
                if (approver != null) {
                    vo.setApproverName(approver.getUserName());
                }
            }
            
            // 2. 查询装修申请详情
            ApplicationDecoration decoration = this.getOne(
                new LambdaQueryWrapper<ApplicationDecoration>()
                    .eq(ApplicationDecoration::getApplicationId, applicationId)
                    .last("limit 1")
            );
            
            if (decoration != null) {
                BeanUtils.copyProperties(decoration, vo);
                
                // 设置公司信息
                if (StringUtils.isNotBlank(decoration.getCompanyId())) {
                    Company company = companyService.getById(decoration.getCompanyId());
                    if (company != null) {
                        vo.setCompanyName(company.getName());
                    }
                }
            }
            
            // 3. 查询申请文件
            List<ApplicationFile> fileList = applicationFileService.list(
                new LambdaQueryWrapper<ApplicationFile>()
                    .eq(ApplicationFile::getApplicationId, applicationId)
            );
            
            if (CollectionUtils.isNotEmpty(fileList)) {
                // 装修申请说明
                List<FileInfo> decorateApplyInstructions = getFileInfos(fileList, ApplicationFile.DECORATE_APPLY, downloadAddress);
                if (CollectionUtils.isNotEmpty(decorateApplyInstructions)) {
                    vo.setDecorateApplyInstructions(decorateApplyInstructions);
                }
                
                // 营业执照
                List<FileInfo> businessLicenses = getFileInfos(fileList, ApplicationFile.BUSINESS_LICENSE, downloadAddress);
                if (CollectionUtils.isNotEmpty(businessLicenses)) {
                    vo.setBusinessLicenses(businessLicenses);
                }
                
                // 有效期内租赁合同
                List<FileInfo> leaseContracts = getFileInfos(fileList, ApplicationFile.LEASE_CONTRACT, downloadAddress);
                if (CollectionUtils.isNotEmpty(leaseContracts)) {
                    vo.setLeaseContracts(leaseContracts);
                }
                
                // 装修承诺书
                List<FileInfo> decorateUndertakings = getFileInfos(fileList, ApplicationFile.DECORATE_UNDERTAKING, downloadAddress);
                if (CollectionUtils.isNotEmpty(decorateUndertakings)) {
                    vo.setDecorateUndertakings(decorateUndertakings);
                }
                
                // 设计院盖章的设计图纸
                List<FileInfo> designDrawings = getFileInfos(fileList, ApplicationFile.DESIGN_DRAWING, downloadAddress);
                if (CollectionUtils.isNotEmpty(designDrawings)) {
                    vo.setDesignDrawings(designDrawings);
                }
                
                // 装修公司营业执照及资质证明等
                List<FileInfo> decorateCompanies = getFileInfos(fileList, ApplicationFile.DECORATE_COMPANY, downloadAddress);
                if (CollectionUtils.isNotEmpty(decorateCompanies)) {
                    vo.setDecorateCompanies(decorateCompanies);
                }
            }

            // 4. 查询流程
            vo.setProcess(processService.getProcessVOList(applicationId, fileList));
            
            return vo;
        } catch (Exception e) {
            log.error("查询装修申请详情异常", e);
            throw e;
        }
    }


    @Override
    public void downloadDecorationFiles(final String applicationId, final HttpServletResponse response) {
        if (StringUtils.isBlank(applicationId)) {
            throw new IllegalArgumentException("申请ID不能为空");
        }

        try {
            // 查询申请信息，用于设置ZIP文件名
            Application application = applicationService.getOne(
                new LambdaQueryWrapper<Application>()
                    .eq(Application::getId, applicationId)
                    .last("limit 1")
            );

            if (application == null) {
                throw new IllegalArgumentException("该申请不存在");
            }

            // 查询装修申请详情
            ApplicationDecoration decoration = this.getOne(
                new LambdaQueryWrapper<ApplicationDecoration>()
                    .eq(ApplicationDecoration::getApplicationId, applicationId)
                    .last("limit 1")
            );

            if (decoration == null) {
                throw new IllegalArgumentException("装修申请详情不存在");
            }

            // 查询申请文件
            List<ApplicationFile> fileList = applicationFileService.list(
                new LambdaQueryWrapper<ApplicationFile>()
                    .eq(ApplicationFile::getApplicationId, applicationId)
            );

            if (CollectionUtils.isEmpty(fileList)) {
                throw new IllegalArgumentException("该申请没有相关文件");
            }

            String applyCode = application.getApplyCode();
            String zipName = "装修申请_" + applyCode + "_附件.zip";

            // 在确认所有数据都正确后，再设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipName, "UTF-8"));
            
            // 按文件类型分组
            Map<Integer, List<ApplicationFile>> fileGroups = fileList.stream()
                .collect(Collectors.groupingBy(ApplicationFile::getFileType));
            
            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                // 遍历各类型文件
                for (Map.Entry<Integer, List<ApplicationFile>> entry : fileGroups.entrySet()) {
                    Integer fileType = entry.getKey();
                    List<ApplicationFile> files = entry.getValue();
                    
                    // 根据文件类型创建文件夹
                    String folderName = FileTypeEnum.getFolderNameByCode(fileType);
                    
                    for (ApplicationFile file : files) {
                        // 获取文件信息
                        FileInfo fileInfo = fileService.getById(file.getFileId());
                        if (fileInfo == null) {
                            log.error("文件不存在：fileId={}", file.getFileId());
                            continue;
                        }

                        // 文件名
                        String fileName = fileInfo.getOriginalFileName();

                        // 创建ZIP条目，添加文件夹路径
                        String entryName = folderName + "/" + fileName;
                        ZipEntry zipEntry = new ZipEntry(entryName);
                        zipOut.putNextEntry(zipEntry);

                        try {
                            // 判断文件存储位置：OSS还是本地
                            if (fileSaveChoose == 0) {
                                // 从OSS下载文件
                                log.info("从OSS下载文件：{}", fileInfo.getFileNameAlias());
                                OSSObject ossObject = ossClient.getObject(ossProperties.getBucketName(), fileInfo.getFileNameAlias());
                                IOUtils.copy(ossObject.getObjectContent(), zipOut);
                                ossObject.close();
                            } else {
                                // 从本地文件系统下载文件
                                Path filePath = Paths.get(fileInfo.getRelativeDownloadUrl());
                                if (!Files.exists(filePath)) {
                                    log.error("本地文件不存在：path={}", filePath);
                                    continue;
                                }
                                log.info("从本地下载文件：{}", filePath);
                                Files.copy(filePath, zipOut);
                            }
                        } catch (Exception e) {
                            log.error("下载文件失败：fileId={}, fileName={}", file.getFileId(), fileName, e);
                            // 继续处理下一个文件，不中断整个下载过程
                        }

                        // 关闭当前ZIP条目
                        zipOut.closeEntry();
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("批量下载装修申请相关文件异常", e);
            throw new RuntimeException("下载文件失败：" + e.getMessage(), e);
        }
    }
}
