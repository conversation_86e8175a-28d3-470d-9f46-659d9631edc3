package com.quantchi.common.model;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Operator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/22 17:44
 */
@Data
@ApiModel("构建boolQueryBuilder基础查询类")
@AllArgsConstructor
@NoArgsConstructor
public class BasicBoolQuery {

    @ApiModelProperty("索引名称")
    private String index;

    @ApiModelProperty("布尔查询参数")
    private BoolQuery.Builder boolQueryBuilder;

    @ApiModelProperty("查询字段")
    private String field;

    @ApiModelProperty("查询字段的关键词名称")
    private String fieldKeyword;

    @ApiModelProperty("查询字段的值")
    private List<String> values;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("关键词查询范围")
    private List<KeywordSearchProperty> keywordFields;

    @ApiModelProperty("匹配类型，精确或模糊")
    private List<String> matchTypes;

    @ApiModelProperty("筛选项查询类型，详见SearchTypeConstants")
    private Integer searchType;

    @ApiModelProperty("操作符类型，与或者或")
    private Operator operator;

    @ApiModelProperty("是否使用filter，为true时不需要算分，提升查询性能")
    private Boolean isFilter = false;

}
