package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel("企业租赁合同简要信息")
public class CompanyLeaseContractSimpleVO {

    @ApiModelProperty("合同ID")
    private String contractId;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("租赁开始时间")
    private LocalDate leaseStartTime;

    @ApiModelProperty("租赁结束时间")
    private LocalDate leaseEndTime;

    @ApiModelProperty("合同状态：2-未生效，1-生效中，0-已失效")
    private Integer contractStatus;

    @ApiModelProperty("合同扫描件文件名称")
    private String contractScanCopyName;

    @ApiModelProperty("合同扫描件文件路径")
    private String contractScanCopyPath;
}
