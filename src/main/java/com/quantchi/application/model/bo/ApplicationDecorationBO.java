package com.quantchi.application.model.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.quantchi.common.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 装修申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@ApiModel(value = "ApplicationDecorationBO", description = "装修申请业务对象")
public class ApplicationDecorationBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 审批人ID */
    @ApiModelProperty("审批人ID")
    private Long approver;

    /** 公司ID */
    @ApiModelProperty("公司ID")
    private String companyId;

    @ApiModelProperty("公司名称")
    private String companyName;

    /** 联系方式 */
    @ApiModelProperty("联系方式")
    private String contactInfo;

    @ApiModelProperty("装修申请说明")
    private List<FileInfo> decorateApplyInstructions;

    @ApiModelProperty("营业执照")
    private List<FileInfo> businessLicenses;

    @ApiModelProperty("有效期内租赁合同")
    private List<FileInfo> leaseContracts;

    @ApiModelProperty("装修承诺书")
    private List<FileInfo> decorateUndertakings;

    @ApiModelProperty("设计院盖章的设计图纸（及电子版）")
    private List<FileInfo> designDrawings;

    @ApiModelProperty("装修公司营业执照及资质证明等")
    private List<FileInfo> decorateCompanies;



}
