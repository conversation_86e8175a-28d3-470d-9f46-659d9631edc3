package com.quantchi.application.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 迁出申请业务对象
 */
@Data
@ApiModel(value = "ApplicationMoveOutVO", description = "迁出申请业务对象")
public class ApplicationMoveOutVO implements Serializable {

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 审批人ID */
    @ApiModelProperty("审批人ID")
    private Long approver;

    /** 审批人姓名 */
    @ApiModelProperty("审批人姓名")
    private String approverName;

    /** 申请编号 */
    @ApiModelProperty("申请编号")
    private String applyCode;

    /** 申请时间 */
    @ApiModelProperty("申请时间")
    private LocalDate applyTime;

    /** 申请时间 */
    @ApiModelProperty("完成时间")
    private LocalDate finishTime;

    /** 申请类型 */
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private Integer applyType;

    /** 申请类型 */
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private String applyTypeCN;

    /** 当前状态 */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private Integer status;

    /** 当前状态 */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private String statusCN;

    // Application主表相关字段
    @ApiModelProperty("公司名称")
    private String companyName;

    /** 公司ID */
    @ApiModelProperty("公司ID")
    private String companyId;

    /** 注册资本 */
    @NotNull(message = "注册资本不能为空")
    @ApiModelProperty("注册资本")
    private String registeredCapital;

    /** 成立时间 */
    @ApiModelProperty("成立时间")
    private LocalDate establishmentDate;

    /** 入驻时间 */
    @ApiModelProperty("入驻时间")
    private LocalDate moveInDate;

    /** 登记地址 */
    @ApiModelProperty("登记地址")
    private String registeredAddress;

    /** 实际地址 */
    @ApiModelProperty("实际地址")
    private String actualAddress;

    // 法人代表（ApplicationLeader）
    @ApiModelProperty("法人代表姓名")
    private String leaderName;

    @ApiModelProperty("法人代表性别")
    private String leaderGender;

    @ApiModelProperty("法人代表国籍")
    private String leaderNationality;

    @ApiModelProperty("法人代表学历")
    private String leaderEducation;

    @ApiModelProperty("法人代表学位")
    private String leaderDegree;

    @ApiModelProperty("法人代表毕业学校")
    private String leaderGraduateSchool;

    @ApiModelProperty("法人代表年龄")
    private String leaderAge;

    @ApiModelProperty("法人代表职称")
    private String leaderTitle;

    @ApiModelProperty("法人代表专业")
    private String leaderMajor;

    @ApiModelProperty("法人代表当前单位")
    private String leaderCurrentOrg;

    @ApiModelProperty("法人代表联系电话")
    private String leaderPhone;

    @ApiModelProperty("法人代表邮箱")
    private String leaderEmail;

    // 联系人（ApplicationContact）
    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系人性别")
    private String contactGender;

    @ApiModelProperty("联系人职位")
    private String contactPosition;

    @ApiModelProperty("联系人手机")
    private String contactMobile;

    @ApiModelProperty("联系人传真")
    private String contactFax;

    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("联系人邮箱")
    private String contactEmail;

    // ApplicationMoveOut相关
    /** 收入总额 */
    @ApiModelProperty("收入总额")
    private String totalRevenue;

    /** 总利润 */
    @ApiModelProperty("总利润")
    private String totalProfit;

    /** 净利润 */
    @ApiModelProperty("净利润")
    private String netProfit;

    /** 发明专利 */
    @ApiModelProperty("发明专利")
    private String inventionPatents;

    /** 软著/实用新型 */
    @ApiModelProperty("软著/实用新型")
    private String softwareOrUtilityPatents;

    /** 科技研发人员 */
    @ApiModelProperty("科技研发人员")
    private String techStaff;

    /** 累计研发经费 */
    @ApiModelProperty("累计研发经费")
    private String totalRAndDExpenses;

    /** 纳税总额 */
    @ApiModelProperty("纳税总额")
    private String totalTax;

    /** 企业所得税 */
    @ApiModelProperty("企业所得税")
    private String corporateIncomeTax;

    /** 增值税 */
    @ApiModelProperty("增值税")
    private String valueAddedTax;

    /** 营业税 */
    @ApiModelProperty("营业税")
    private String businessTax;

    /** 个人所得税 */
    @ApiModelProperty("个人所得税")
    private String personalIncomeTax;

    @ApiModelProperty("所获扶持政策明细")
    private String supportPolicyDetail;

    @ApiModelProperty("迁出原因id")
    private Integer moveOutReason;

    @ApiModelProperty("迁出原因")
    private String moveOutReasonCN;

    @ApiModelProperty("其他原因")
    private String moveOutOtherReason;

    @ApiModelProperty("前往地址")
    private String destinationAddress;

    @ApiModelProperty("其他需说明的情况")
    private String otherDescription;

    @ApiModelProperty("审批进度")
    private List<ProcessVO> process;
}
