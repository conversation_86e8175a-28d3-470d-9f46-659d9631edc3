package com.quantchi.company.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.common.exception.BusinessException;
import com.quantchi.company.constants.Constant;
import com.quantchi.company.mapper.DmDivisionMapper;
import com.quantchi.company.model.entity.DmDivision;
import com.quantchi.company.model.vo.DmDivisionWithCompeteInfoVO;
import com.quantchi.company.service.IDmDivisionService;
import com.quantchi.sys.model.constants.CacheConstants;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 地区代码表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DmDivisionServiceImpl extends ServiceImpl<DmDivisionMapper, DmDivision> implements IDmDivisionService {


    @Override
    public List<DmDivisionWithCompeteInfoVO> divisionTree(final Boolean needArea, final String index) {
        int level;
        if (needArea) {
            level = 3;
        } else {
            level = 2;
        }
        if (Objects.equals(index, "hangzhuan_center_organization")) {
            level = 1;
        }
        if (Objects.equals(index, "hangzhuan_center_policy")) {
            level = 3;
        }
        final List<DmDivisionWithCompeteInfoVO> divisions = china(level);
        final Map<String, DmDivisionWithCompeteInfoVO> divisionMap = divisions.stream()
                .collect(Collectors.toMap(DmDivisionWithCompeteInfoVO::getId, Function.identity()));
        DmDivisionWithCompeteInfoVO root = null;
        for (final DmDivisionWithCompeteInfoVO division : divisions) {
            if (StringUtils.isEmpty(division.getParentId())) {
                root = division;
                continue;
            }
            final DmDivisionWithCompeteInfoVO parent = divisionMap.get(division.getParentId());
            if (parent.getChildren() == null) {
                parent.setChildren(new ArrayList<>());
            }
            parent.getChildren().add(division);
        }
        // 浙江省放在第一个
        final List<DmDivisionWithCompeteInfoVO> children;
        if (root != null) {
            children = root.getChildren();
            final List<DmDivisionWithCompeteInfoVO> zhejiangFirst = children.stream()
                    .filter(item -> item.getName().equals("浙江省")).collect(Collectors.toList());
            children.removeIf(item -> item.getName().equals("浙江省"));
            zhejiangFirst.addAll(children);
            root.setChildren(zhejiangFirst);
            // 去除港澳台
            final List<String> gangaotai = Arrays.asList("division/710000", "division/810000", "division/820000");
            zhejiangFirst.removeIf(item -> gangaotai.contains(item.getId()));
            // 将直辖市变为1级
            zhejiangFirst.forEach(item -> {
                final String id = item.getId();
                if (Constant.zhiXiaShiOfProvince.contains(id)) {
//                    item.setName(item.getName() + "市");
                    // 直辖市不显示下面一级
                    item.setChildren(null);
                }
            });
            return zhejiangFirst;
        }
        return Collections.emptyList();

    }

    @Override
    public DmDivisionWithCompeteInfoVO divisionTreeOfGlobal() {
        // 获取省数据，浙江省下沉到市一级
//        final List<DmDivisionWithCompeteInfoVO> divisions = china(2).stream()
//                .filter(item -> item.getLevel() < 2
//                || (item.getLevel() == 2 && Objects.equals(item.getParentId(), "division/330000")))
//                .collect(Collectors.toList());
        // 获取省数据，浙江省下沉到县一级
        final List<DmDivisionWithCompeteInfoVO> divisions = china(3).stream()
                .filter(item -> item.getLevel() < 2
                        || item.getParentId().startsWith("division/33"))
                .collect(Collectors.toList());
        final Map<String, DmDivisionWithCompeteInfoVO> divisionMap = divisions.stream()
                .collect(Collectors.toMap(DmDivisionWithCompeteInfoVO::getId, Function.identity()));
        DmDivisionWithCompeteInfoVO root = null;
        for (final DmDivisionWithCompeteInfoVO division : divisions) {
            if (StringUtils.isEmpty(division.getParentId())) {
                root = division;
                continue;
            }
            final DmDivisionWithCompeteInfoVO parent = divisionMap.get(division.getParentId());
            if (parent.getChildren() == null) {
                parent.setChildren(new ArrayList<>());
            }
            parent.getChildren().add(division);
        }
        // 浙江省放在第一个
        final List<DmDivisionWithCompeteInfoVO> children;
        if (root != null) {
            children = root.getChildren();
            final List<DmDivisionWithCompeteInfoVO> zhejiangFirst = children.stream()
                    .filter(item -> item.getName().equals("浙江省")).collect(Collectors.toList());
            children.removeIf(item -> item.getName().equals("浙江省"));
            zhejiangFirst.addAll(children);
            root.setChildren(zhejiangFirst);
            // 去除港澳台
            final List<String> gangaotai = Arrays.asList("division/710000", "division/810000", "division/820000");
            zhejiangFirst.removeIf(item -> gangaotai.contains(item.getId()));
        }
        // 增加其他国家
        final List<DmDivisionWithCompeteInfoVO> globalDivision = new LinkedList<>();
        globalDivision.add(root);
        final List<DmDivision> otherNation = this.list(Wrappers.<DmDivision>lambdaQuery()
                .eq(DmDivision::getLevel, 0)
                .ne(DmDivision::getId, "division/000000"));
        globalDivision.addAll(otherNation.stream().map(DmDivisionWithCompeteInfoVO::toVO).collect(Collectors.toList()));
        final DmDivisionWithCompeteInfoVO vo = new DmDivisionWithCompeteInfoVO();
        vo.setName("全球");
        vo.setId("全球");
        vo.setChildren(globalDivision);
        return vo;
    }

    /**
     * 根据id获取地区详细信息
     */
    @Override
    @Cacheable(value = CacheConstants.DIVISION_CACHE, key = "#id")
    public DmDivision getInfoById(final String id) {
        final DmDivision division = this.getById(id);
        if (division == null) {
            throw new BusinessException("找不到对应的地区");
        }
        if ("division/000000".equals(id)) {
            division.setName("全国");
        }
        return division;
    }

    private List<DmDivisionWithCompeteInfoVO> china(final int level) {
        final List<DmDivision> dmDivisionList = this.list(
                Wrappers.<DmDivision>lambdaQuery()
                        .gt(DmDivision::getLevel, 0)
                        .lt(DmDivision::getLevel, level + 1)
                        .orderByAsc(DmDivision::getId));
        dmDivisionList.add(this.getById("division/000000"));
        // 直辖市只取省份级别
        final List<DmDivisionWithCompeteInfoVO> resultList = new ArrayList<>(dmDivisionList.size());
        dmDivisionList.forEach(dmDivision -> {
            final DmDivisionWithCompeteInfoVO vo = new DmDivisionWithCompeteInfoVO();
            BeanUtil.copyProperties(dmDivision, vo);
            if (!Objects.equals("id", "division/000000")) {
                vo.setDisabled(false);
            }
            resultList.add(vo);
        });
        return resultList;
    }
}
