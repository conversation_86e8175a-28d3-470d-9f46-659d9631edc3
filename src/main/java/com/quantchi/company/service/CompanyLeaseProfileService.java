package com.quantchi.company.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.company.model.entity.CompanyLeaseProfile;
import com.quantchi.company.model.bo.CompanyLeaseContractEditBO;
import com.quantchi.company.model.vo.CompanyLeaseContractSimpleListVO;
import com.quantchi.company.model.vo.CompanyLeaseContractVO;
import com.quantchi.company.model.vo.CompanyLeaseRentBillVO;

import java.util.List;

public interface CompanyLeaseProfileService extends IService<CompanyLeaseProfile> {
    /**
     * 根据companyId查询合同及租赁概况信息
     */
    CompanyLeaseContractVO getLeaseContractInfo(String companyId);

    /**
     * 编辑企业租赁合同信息
     */
    boolean editLeaseContractInfo(CompanyLeaseContractEditBO editBO);

    /**
     * 查询企业租金账单
     */
    List<CompanyLeaseRentBillVO> getCompanyLeaseRentBill(String companyId);

    /**
     * 查询企业租赁合同简要信息（返回所有合同列表）
     */
    CompanyLeaseContractSimpleListVO getLeaseContractSimple(String companyId);
}