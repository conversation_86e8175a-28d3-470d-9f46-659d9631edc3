package com.quantchi.company.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.quantchi.company.model.entity.DmDivision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <p>
 * 地区代码表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@ApiModel(value = "地区树展示类")
public class DmDivisionWithCompeteInfoVO {

    @ApiModelProperty("省市区编码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String id;

    @ApiModelProperty("上级编码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String parentId;

    @ApiModelProperty("省市区名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String name;

    @ApiModelProperty("省市区简称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String shortName;

    @ApiModelProperty("省市区编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String code;

    @ApiModelProperty("省市区层级位置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer level;

    @ApiModelProperty("经度")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String lng;

    @ApiModelProperty("维度")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String lat;

    @ApiModelProperty(value = "子区域")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<DmDivisionWithCompeteInfoVO> children;

    @ApiModelProperty(value = "是否禁止显示")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean disabled = false;

    public DmDivisionWithCompeteInfoVO() {
    }

    public static DmDivisionWithCompeteInfoVO toVO(final DmDivision dmDivision) {
        final DmDivisionWithCompeteInfoVO vo = new DmDivisionWithCompeteInfoVO();
        BeanUtils.copyProperties(dmDivision, vo);
        return vo;
    }

}
