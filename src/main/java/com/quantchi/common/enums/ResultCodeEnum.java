package com.quantchi.common.enums;

/**
 * <AUTHOR>
 * @date 2020/5/22
 * 通用返回code
 */
public enum ResultCodeEnum {
    /**
     * 错误码 错误信息
     */

    IMPORT_ERROR(6000, "参数错误"),

    PERMISSION_NOT_ALLOWED(6001, "暂无权限"),

    LOGIN_FAILED(6002, "用户名或密码错误"),

    ACCOUNT_EXIST(6003, "账号已存在"),

    NO_NODE_MATCH(6004, "未匹配到节点"),

    USER_PASSWORD_ERROR(6005, "原密码错误"),

    NO_LOGIN_ERROR(6006, "Token无效"),

    USER_NO_LOGIN(401, "当前用户没有登录"),

    QUERY_ERROR(6008, "数据查询失败"),

    CANCEL_COLLECT_ERROR(6009, "取消收藏失败"),

    ADD_COLLECT_ERROR(6010, "添加收藏失败"),

    ADD_MARK_ERROR(6011, "添加标注失败"),

    WITH_DRAW_ERROR(6012, "撤回失败"),

    SUCCESS(200, "success");

    private final Integer code;

    private final String message;

    ResultCodeEnum(final Integer code, final String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
