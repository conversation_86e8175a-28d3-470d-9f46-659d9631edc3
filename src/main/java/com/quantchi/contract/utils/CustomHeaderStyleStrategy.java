package com.quantchi.contract.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * 自定义表头样式策略
 * 设置表头为透明背景，无灰色填充，并设置列宽
 */
public class CustomHeaderStyleStrategy implements CellWriteHandler {

    private final int quarterCount;

    public CustomHeaderStyleStrategy(int quarterCount) {
        this.quarterCount = quarterCount;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // 在创建单元格之前执行
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 在创建单元格之后执行
        if (isHead) {
            Sheet sheet = writeSheetHolder.getSheet();
            Workbook workbook = sheet.getWorkbook();
            int columnIndex = cell.getColumnIndex();

            // 设置列宽（单位：字符宽度 * 256）
            if (columnIndex == 0) {
                // 合同编号
                sheet.setColumnWidth(columnIndex, 20 * 256);
            } else if (columnIndex == 1) {
                // 承租方
                sheet.setColumnWidth(columnIndex, 25 * 256);
            } else if (columnIndex == 2) {
                // 房号/楼栋/园区
                sheet.setColumnWidth(columnIndex, 30 * 256);
            } else if (columnIndex == 3) {
                // 租金标准
                sheet.setColumnWidth(columnIndex, 30 * 256);
            } else {
                // 动态季度列
                int dynamicColumnIndex = columnIndex - 4;
                int fieldIndex = dynamicColumnIndex % 5;

                switch (fieldIndex) {
                    case 0: // 折扣系数
                        sheet.setColumnWidth(columnIndex, 12 * 256);
                        break;
                    case 1: // 减免金额
                    case 2: // 应收金额
                    case 3: // 已收金额
                    case 4: // 未收金额
                        sheet.setColumnWidth(columnIndex, 20 * 256);
                        break;
                }
            }

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();

            // 设置透明背景
            headerStyle.setFillPattern(FillPatternType.NO_FILL);
            headerStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFillBackgroundColor(IndexedColors.WHITE.getIndex());

            // 设置边框
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            // 设置字体
            Font font = workbook.createFont();
            font.setBold(false);
            font.setColor(IndexedColors.BLACK.getIndex());
            headerStyle.setFont(font);

            // 设置对齐方式
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 应用样式
            cell.setCellStyle(headerStyle);
        }
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 在处理单元格之后执行
    }
}
