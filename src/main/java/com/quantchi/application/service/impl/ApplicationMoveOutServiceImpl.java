package com.quantchi.application.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.application.model.entity.*;
import com.quantchi.application.mapper.ApplicationMoveOutMapper;
import com.quantchi.application.model.enums.ApplicationStatus;
import com.quantchi.application.model.enums.ApplyType;
import com.quantchi.application.model.enums.ProcessStatus;
import com.quantchi.application.model.vo.ApplicationMoveInVO;
import com.quantchi.application.model.vo.ApplicationMoveOutVO;
import com.quantchi.application.model.vo.ProcessVO;
import com.quantchi.application.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.common.domain.InMemoryCache;
import com.quantchi.company.model.entity.Company;
import com.quantchi.company.service.ICompanyService;
import com.quantchi.sys.mapper.SysUserMapper;
import com.quantchi.sys.model.entity.SysUser;
import com.quantchi.sys.service.impl.CaptchaSendService;
import com.quantchi.sys.service.impl.SysUserService;
import com.quantchi.sys.utils.LoginHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.quantchi.application.model.bo.ApplicationMoveOutBO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import com.quantchi.common.utils.WordUtils;

/**
 * <p>
 * 迁出申请内容表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@Slf4j
public class ApplicationMoveOutServiceImpl extends ServiceImpl<ApplicationMoveOutMapper, ApplicationMoveOut> implements IApplicationMoveOutService {

    @Autowired
    private IApplicationService applicationService;
    @Autowired
    private IApplicationLeaderService leaderService;
    @Autowired
    private IApplicationContactService contactService;
    @Autowired
    private IApplicationProcessService processService;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private IApplicationFileService applicationFileService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private CaptchaSendService captchaSendService;

    /**
     * 迁出申请提交，包含BO转Entity、主表及子表保存等逻辑
     * @param bo 迁出申请BO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitMoveOutApplication(ApplicationMoveOutBO bo) {
        try {

            if (StringUtils.isBlank(bo.getCompanyId()) && StringUtils.isNotBlank(bo.getCompanyName())) {
                Company company = new Company();
                company.setName(bo.getCompanyName());
                company.setAddress(bo.getRegisteredAddress());
                company.setLegalPerson(bo.getLeaderName());
                company.setEstablishDate(bo.getEstablishmentDate());
                bo.setCompanyId(companyService.addCompanyInfo(company));
                if (StringUtils.isBlank(bo.getCompanyId())) {
                    throw new IllegalArgumentException("企业创建失败");
                }
            }

            String user = LoginHelper.getUsername();
            // 1. 保存主表 Application
            Application application = new Application();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                BeanUtils.copyProperties(bo, application);
                application.setId(UUID.randomUUID().toString().replace("-", ""));
                application.setApplicantId(StpUtil.getLoginIdAsString());
                application.setApprover(bo.getApprover());
                application.setApplyType(Application.MOVE_OUT);
                application.setApplyTime(LocalDate.now());
                long count = applicationService.count(Wrappers.lambdaQuery(Application.class).eq(Application::getApplyType, Application.MOVE_OUT));
                application.setApplyCode(LocalDate.now().getYear() + "_" + (count + 1));
                application.setStatus(Application.PROCESSING);
                application.setCreateTime(LocalDateTime.now());
                application.setUpdateTime(LocalDateTime.now());
                application.setCreateUser(user);
                application.setUpdateUser(user);
                applicationService.save(application);
            } else {
                List<Application> list = applicationService.list(Wrappers.lambdaQuery(Application.class)
                        .eq(Application::getId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请不存在");
                }
                application = list.get(0);
                application.setApprover(bo.getApprover());
                application.setStatus(Application.PROCESSING);
                application.setUpdateTime(LocalDateTime.now());
                application.setUpdateUser(user);
                applicationService.updateById(application);
            }
            String mainId = application.getId();

            // 2. 保存迁出详情表 ApplicationMoveOut
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                this.remove(Wrappers.lambdaQuery(ApplicationMoveOut.class)
                        .eq(ApplicationMoveOut::getApplicationId, bo.getApplicationId()));
            }
            ApplicationMoveOut moveOut = new ApplicationMoveOut();
            BeanUtils.copyProperties(bo, moveOut);
            moveOut.setId(UUID.randomUUID().toString().replace("-", ""));
            moveOut.setApplicationId(mainId);
            moveOut.setCreateTime(LocalDateTime.now());
            moveOut.setCreateUser(user);
            this.baseMapper.insert(moveOut);

            // 3. 保存法人代表 ApplicationLeader
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                leaderService.remove(Wrappers.lambdaQuery(ApplicationLeader.class)
                        .eq(ApplicationLeader::getApplicationId, bo.getApplicationId()));
            }
            ApplicationLeader leader = new ApplicationLeader();
            leader.setId(UUID.randomUUID().toString().replace("-", ""));
            leader.setApplicationId(mainId);
            leader.setName(bo.getLeaderName());
            leader.setGender(bo.getLeaderGender());
            leader.setNationality(bo.getLeaderNationality());
            leader.setAge(bo.getLeaderAge());
            leader.setEducation(bo.getLeaderEducation());
            leader.setDegree(bo.getLeaderDegree());
            leader.setTitle(bo.getLeaderTitle());
            leader.setMajor(bo.getLeaderMajor());
            leader.setGraduateSchool(bo.getLeaderGraduateSchool());
            leader.setCurrentEmployer(bo.getLeaderCurrentOrg());
            leader.setContactPhone(bo.getLeaderPhone());
            leader.setEmail(bo.getLeaderEmail());
            leader.setCreateTime(LocalDateTime.now());
            leader.setCreateUser(user);
            leaderService.save(leader);

            // 4. 保存联系人 ApplicationContact
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                contactService.remove(Wrappers.lambdaQuery(ApplicationContact.class)
                        .eq(ApplicationContact::getApplicationId, bo.getApplicationId()));
            }
            ApplicationContact contact = new ApplicationContact();
            contact.setId(UUID.randomUUID().toString().replace("-", ""));
            contact.setApplicationId(mainId);
            contact.setCompanyId(bo.getCompanyId());
            contact.setName(bo.getContactName());
            contact.setGender(bo.getContactGender());
            contact.setPosition(bo.getContactPosition());
            contact.setMobile(bo.getContactMobile());
            contact.setFax(bo.getContactFax());
            contact.setTelephone(bo.getContactPhone());
            contact.setEmail(bo.getContactEmail());
            contact.setCreateUser("入驻申请联系人");
            contact.setJobStatus(1);
            contact.setCreateTime(LocalDateTime.now());
            contactService.save(contact);

            // 5. 保存流程 ApplicationProcess
            ApplicationProcess process = new ApplicationProcess();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                process.setId(UUID.randomUUID().toString().replace("-", ""));
                process.setApplicationId(mainId);
                process.setProcessor(user);
                process.setProcess(ApplicationProcess.START_APPLY);
                process.setProcessTime(LocalDate.now());
                process.setCreateTime(LocalDateTime.now());
                process.setCreateUser(user);
                processService.save(process);
            } else {
                List<ApplicationProcess> list = processService.list(Wrappers.lambdaQuery(ApplicationProcess.class)
                        .eq(ApplicationProcess::getApplicationId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请流程不存在");
                }
                process = list.get(0);
                process.setUpdateTime(LocalDateTime.now());
                process.setUpdateUser(user);
                processService.updateById(process);
            }

            SysUser sysUser = sysUserService.selectUserById(Long.valueOf(application.getApplicantId()));
            captchaSendService.sendApplicationByAliyun(sysUser.getPhonenumber(), bo.getCompanyName(), "SMS_489830501");

        } catch (Exception e) {
            log.error("error occurred when submit move out application: ", e);
            throw e;
        }

    }

    @Override
    public ApplicationMoveOutVO applicationMoveOutDetail(String applicationId) {
        try {
            if (StringUtils.isBlank(applicationId)) {
                throw new IllegalArgumentException("申请ID不能为空");
            }
            
            ApplicationMoveOutVO vo = new ApplicationMoveOutVO();
            
            // 1. 查询申请主表
            Application application = applicationService.getOne(
                new LambdaQueryWrapper<Application>()
                    .eq(Application::getId, applicationId)
                    .last("limit 1")
            );
            
            if (application == null) {
                throw new IllegalArgumentException("该申请不存在");
            }
            
            // 设置申请基本信息
            BeanUtils.copyProperties(application, vo);
            vo.setApplicationId(application.getId());
            vo.setApplyTypeCN(application.getApplyType() != null ? ApplyType.getInfoByCode(application.getApplyType()) : null);
            vo.setStatusCN(application.getStatus() != null ? ApplicationStatus.getInfoByCode(application.getStatus()) : null);

            // 查询审批人姓名
            if (application.getApprover() != null) {
                SysUser approver = sysUserMapper.selectById(application.getApprover());
                if (approver != null) {
                    vo.setApproverName(approver.getUserName());
                }
            }
            
            // 2. 查询迁出申请详情
            ApplicationMoveOut moveOut = this.getOne(
                new LambdaQueryWrapper<ApplicationMoveOut>()
                    .eq(ApplicationMoveOut::getApplicationId, applicationId)
                    .last("limit 1")
            );
            
            if (moveOut != null) {
                BeanUtils.copyProperties(moveOut, vo);
                vo.setMoveOutReasonCN(InMemoryCache.getMoveOutReasonMap().getOrDefault(moveOut.getMoveOutReason(),""));
                
                // 设置公司信息
                if (StringUtils.isNotBlank(moveOut.getCompanyId())) {
                    Company company = companyService.getById(moveOut.getCompanyId());
                    if (company != null) {
                        vo.setCompanyName(company.getName());
                    }
                }
            }
            
            // 3. 查询法人代表
            ApplicationLeader leader = leaderService.getOne(
                new LambdaQueryWrapper<ApplicationLeader>()
                    .eq(ApplicationLeader::getApplicationId, applicationId)
                    .last("limit 1")
            );
            
            if (leader != null) {
                vo.setLeaderName(leader.getName());
                vo.setLeaderGender(leader.getGender());
                vo.setLeaderNationality(leader.getNationality());
                vo.setLeaderAge(leader.getAge());
                vo.setLeaderEducation(leader.getEducation());
                vo.setLeaderDegree(leader.getDegree());
                vo.setLeaderTitle(leader.getTitle());
                vo.setLeaderMajor(leader.getMajor());
                vo.setLeaderGraduateSchool(leader.getGraduateSchool());
                vo.setLeaderCurrentOrg(leader.getCurrentEmployer());
                vo.setLeaderPhone(leader.getContactPhone());
                vo.setLeaderEmail(leader.getEmail());
            }
            
            // 4. 查询联系人
            ApplicationContact contact = contactService.getOne(
                new LambdaQueryWrapper<ApplicationContact>()
                    .eq(ApplicationContact::getApplicationId, applicationId)
                    .last("limit 1")
            );
            
            if (contact != null) {
                vo.setContactName(contact.getName());
                vo.setContactGender(contact.getGender());
                vo.setContactPosition(contact.getPosition());
                vo.setContactMobile(contact.getMobile());
                vo.setContactFax(contact.getFax());
                vo.setContactPhone(contact.getTelephone());
                vo.setContactEmail(contact.getEmail());
            }

            // 5. 查询审批流程
            List<ApplicationFile> fileList = applicationFileService.list(
                    new LambdaQueryWrapper<ApplicationFile>()
                            .eq(ApplicationFile::getApplicationId, applicationId)
            );
            vo.setProcess(processService.getProcessVOList(applicationId, fileList));
            
            return vo;
        } catch (Exception e) {
            log.error("查询迁出申请详情异常", e);
            throw e;
        }
    }
    
    @Override
    public void downloadMoveOutApplication(final String applicationId, final HttpServletResponse response) throws Exception {
        log.info("开始下载迁出申请表，申请ID：{}", applicationId);
        
        // 获取申请详情数据
        ApplicationMoveOutVO vo = applicationMoveOutDetail(applicationId);
        if (vo == null) {
            throw new IllegalArgumentException("找不到对应的迁出申请");
        }

        // 获取公司信息
        String companyName = vo.getCompanyName();
        if (StringUtils.isBlank(companyName)) {
            throw new IllegalArgumentException("企业名称不存在");
        }

        log.info("获取到企业名称: {}", companyName);
        
        // Word模板文件路径
        final String fileName = companyName + "迁出申请表.docx";
        final String templatePath = "templates/企业迁出审批表.docx";
        
        // 准备填充数据
        Map<String, String> params = new HashMap<>();

        // 申请时间
        if (vo.getApplyTime() != null) {
            params.put("yy", String.valueOf(vo.getApplyTime().getYear()));
            params.put("mm", String.valueOf(vo.getApplyTime().getMonthValue()));
            params.put("dd", String.valueOf(vo.getApplyTime().getDayOfMonth()));
        }

        // 申请编号
        if (StringUtils.isNotBlank(vo.getApplyCode()) && vo.getApplyCode().contains("_")) {
            String[] applyCode = vo.getApplyCode().split("_");
            params.put("year", applyCode[0]);
            params.put("num", applyCode[1]);
        }

        // 设置基本信息
        params.put("companyName", vo.getCompanyName());
        params.put("registerCapital", vo.getRegisteredCapital());
        params.put("establishDate", vo.getEstablishmentDate() != null ? vo.getEstablishmentDate().toString() : "");
        params.put("moveInDate", vo.getMoveInDate() != null ? vo.getMoveInDate().toString() : "");
        params.put("rAddress", vo.getRegisteredAddress());
        params.put("aAddress", vo.getActualAddress());
        
        // 法人信息
        params.put("lName", vo.getLeaderName());
        params.put("lSex", vo.getLeaderGender());
        params.put("lNation", vo.getLeaderNationality());
        params.put("lAge", vo.getLeaderAge());
        params.put("lEdu", vo.getLeaderEducation());
        params.put("lDegree", vo.getLeaderDegree());
        params.put("lTitle", vo.getLeaderTitle());
        params.put("lMajor", vo.getLeaderMajor());
        params.put("lSchool", vo.getLeaderGraduateSchool());
        params.put("lOrg", vo.getLeaderCurrentOrg());
        params.put("lPhone", vo.getLeaderPhone());
        params.put("lEmail", vo.getLeaderEmail());
        
        // 联系人信息
        params.put("cName", vo.getContactName());
        params.put("cSex", vo.getContactGender());
        params.put("cPosition", vo.getContactPosition());
        params.put("cMobile", vo.getContactMobile());
        params.put("cFax", vo.getContactFax());
        params.put("cPhone", vo.getContactPhone());
        params.put("cEmail", vo.getContactEmail());

        // 添加财务信息
        params.put("totalRevenue", vo.getTotalRevenue());
        params.put("totalProfit", vo.getTotalProfit());
        params.put("netProfit", vo.getNetProfit());

        // 添加专利和研发信息
        params.put("iPatents", vo.getInventionPatents());
        params.put("suPatents", vo.getSoftwareOrUtilityPatents());
        params.put("techStaff", vo.getTechStaff());
        params.put("RDExpenses", vo.getTotalRAndDExpenses());

        // 添加纳税信息
        params.put("totalTax", vo.getTotalTax());
        params.put("corporateT", vo.getCorporateIncomeTax());
        params.put("valueAddT", vo.getValueAddedTax());
        params.put("businessT", vo.getBusinessTax());
        params.put("personalT", vo.getPersonalIncomeTax());

        // 添加迁出相关信息
        params.put("supportPolicyDetail", vo.getSupportPolicyDetail());
        params.put("otherMoveOutReason", vo.getMoveOutOtherReason());
        params.put("destinationAddress", vo.getDestinationAddress());
        params.put("otherDescription", vo.getOtherDescription());

        handleCheckboxes(vo, params);
        
        // 输出参数用于调试
        WordUtils.logParams(params);
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setCharacterEncoding("UTF-8");
        String encodedFilename = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + encodedFilename);
        
        // 获取资源文件
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath)) {
            if (is == null) {
                log.error("模板文件不存在: {}", templatePath);
                throw new java.io.FileNotFoundException("模板文件不存在: " + templatePath);
            }
            
            log.info("成功读取模板文件: {}", templatePath);
            
            // 处理Word文档
            WordUtils.replaceTextInDocument(is, response.getOutputStream(), params);
            log.info("迁出申请表文件生成完成");
        } catch (IOException e) {
            log.error("处理Word文档异常", e);
            throw e;
        }
    }

    /**
     * 处理复选框勾选
     *
     * @param vo 申请详情对象
     * @param params 参数映射
     */
    private void handleCheckboxes(ApplicationMoveOutVO vo, Map<String, String> params) {
        log.info("处理复选框勾选");

        // 处理迁出原因复选框勾选
        if (StringUtils.isNotBlank(vo.getMoveOutReasonCN())) {
            String moveOutReasonCN = vo.getMoveOutReasonCN();
            log.info("迁出原因：{}", moveOutReasonCN);

            // 使用InMemoryCache缓存的产业链数据
            List<MoveOutReason> moveOutReasonList = InMemoryCache.getMoveOutReasonList();

            boolean matched = false;
            if (CollectionUtils.isNotEmpty(moveOutReasonList)) {
                for (MoveOutReason reason : moveOutReasonList) {
                    // 判断申请中的迁出原因是否匹配
                    if (moveOutReasonCN.contains(reason.getMoveOutReason())) {
                        params.put("checkbox_" + reason.getMoveOutReason(), "true");
                        matched = true;
                        log.info("匹配到迁出原因复选框: {}", reason.getMoveOutReason());
                        break; // 只匹配一个迁出原因
                    }
                }
            }

            // 如果没有匹配到任何预定义的迁出原因，则勾选"其他"
            if (!matched) {
                params.put("checkbox_其他", "true");
                // 如果是其他，则填写具体内容
                params.put("otherReason", moveOutReasonCN);
                log.info("未匹配到预定义迁出原因，设置为其他: {}", moveOutReasonCN);
            }
        }
    }


}
