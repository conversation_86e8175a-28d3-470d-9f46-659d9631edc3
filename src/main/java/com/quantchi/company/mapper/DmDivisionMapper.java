package com.quantchi.company.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.company.model.entity.DmDivision;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 地区代码表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface DmDivisionMapper extends BaseMapper<DmDivision> {
    /**
     * 根据idList获取nameList
     *
     * @param divisionIdList
     * @return
     */
    List<String> getNameListByIdList(@Param("divisionIdList") List<String> divisionIdList);
}
