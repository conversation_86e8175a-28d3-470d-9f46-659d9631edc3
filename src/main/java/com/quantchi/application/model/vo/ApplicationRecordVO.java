package com.quantchi.application.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 办事记录返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@ApiModel(value = "ApplicationRecordVO对象", description = "办事记录返回对象")
public class ApplicationRecordVO {
    
    @ApiModelProperty("申请ID")
    private String applicationId;
    
    @ApiModelProperty("申请单位")
    private String applicantName;
    
    @ApiModelProperty("申请事项")
    private String applyTypeName;
    
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private Integer applyType;
    
    @ApiModelProperty("发起时间")
    private LocalDate applyTime;
    
    @ApiModelProperty("完成时间")
    private LocalDate finishTime;
    
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private Integer status;
    
    @ApiModelProperty("状态名称")
    private String statusName;
}
