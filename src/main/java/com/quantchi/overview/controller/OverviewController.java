package com.quantchi.overview.controller;

import com.quantchi.company.service.ICompanyService;
import com.quantchi.overview.model.bo.ExpiringContractQueryBO;
import com.quantchi.overview.model.bo.NewCompanyQueryBO;
import com.quantchi.overview.model.vo.ExpiringContractPageVO;
import com.quantchi.overview.model.vo.NewCompanyPageVO;
import com.quantchi.overview.model.vo.OverviewVO;
import com.quantchi.overview.model.vo.MoveInTrendVO;
import com.quantchi.overview.service.impl.OverviewServiceImpl;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "园区总览相关接口")
@RequestMapping("/overview")
@Slf4j
@RequiredArgsConstructor
public class OverviewController {

    private final ICompanyService companyService;
    private final OverviewServiceImpl overviewService;

    /**
     * 园区概况接口
     */
    @GetMapping("/statistic")
    @ApiOperation("园区概况统计")
    public ResultInfo<OverviewVO> getOverview(
            @ApiParam(value = "园区ID", required = false)
            @RequestParam(value = "parkId", required = false) String parkId) {
        return ResultConvert.success(overviewService.getOverviewData(parkId));
    }
    
    /**
     * 分页查询新入驻企业列表
     */
    @PostMapping("/newCompanies")
    @ApiOperation("分页查询新入驻企业列表")
    public ResultInfo<NewCompanyPageVO> pageNewCompanies(@RequestBody NewCompanyQueryBO queryBO) {
        try {
            NewCompanyPageVO pageVO = overviewService.pageNewCompanies(queryBO);
            return ResultConvert.success(pageVO);
        } catch (Exception e) {
            log.error("分页查询新入驻企业列表异常", e);
            return ResultConvert.error("分页查询新入驻企业列表异常: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询合同即将到期企业列表
     */
    @PostMapping("/expiringContracts")
    @ApiOperation("分页查询合同即将到期企业列表")
    public ResultInfo<ExpiringContractPageVO> pageExpiringContracts(@RequestBody ExpiringContractQueryBO queryBO) {
        try {
            ExpiringContractPageVO pageVO = overviewService.pageExpiringContracts(queryBO);
            return ResultConvert.success(pageVO);
        } catch (Exception e) {
            log.error("分页查询合同即将到期企业列表异常", e);
            return ResultConvert.error("分页查询合同即将到期企业列表异常: " + e.getMessage());
        }
    }
    
    /**
     * 查询当前年份企业入驻增长趋势
     */
    @GetMapping("/moveInTrend")
    @ApiOperation("查询当前年份企业入驻增长趋势")
    public ResultInfo<List<MoveInTrendVO>> queryMoveInTrend(
            @ApiParam(value = "园区ID", required = false)
            @RequestParam(value = "parkId", required = false) String parkId) {
        try {
            final List<MoveInTrendVO> trendList = overviewService.queryMoveInTrend(parkId);
            return ResultConvert.success(trendList);
        } catch (Exception e) {
            log.error("查询企业入驻增长趋势异常", e);
            return ResultConvert.error("查询企业入驻增长趋势异常: " + e.getMessage());
        }
    }
}
