package com.quantchi.common.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.common.config.properties.AliyunProperties;
import com.quantchi.common.exception.BusinessException;
import com.quantchi.common.mapper.FileInfoMapper;
import com.quantchi.common.model.FileInfo;
import com.quantchi.common.service.IFileInfoService;
import com.quantchi.sys.utils.SpringUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

@Service
@Slf4j
public class FileInfoServiceImpl extends ServiceImpl<FileInfoMapper, FileInfo> implements IFileInfoService {

    /**
     * 使用oss的环境
     */
    private static final String PROFILE_PROD = "prod";

    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${file.save.choose}")
    private int fileSaveChoose;

    @Value("${file.upload.root}")
    private String uploadRoot;
    @Value("${file.upload.dir}")
    private String uploadDir;

    @Autowired
    private AliyunProperties ossProperties;

    @Autowired
    private OSS ossClient;


    @Override
    @SneakyThrows
    public String[] saveFile(MultipartFile file, String saveSubDir) {
        if (file == null) {
            throw new IllegalArgumentException("文件不能为空");
        }

        FileInfo fileInfo = upload(file, saveSubDir);

        String path = fileInfo.getDownloadUrl() + "&inline=true";

        return new String[]{path, fileInfo.getOriginalFileName()};
    }

    @Override
    public void download(String fileId, boolean inline, HttpServletResponse response) {
        FileInfo fileInfo = this.getById(fileId);
        if (fileInfo == null) {
            throw new BusinessException("文件已被删除，无法下载");
        }
        // 获取文件原始名称
        String fileName = fileInfo.getOriginalFileName();
        // 获取文件后缀名
        String fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        InputStream inputStream = null;
        try {
            response.setContentType("application/" + fileSuffix);
            response.addHeader("Content-Disposition", (inline ? "inline" : "attachment") + ";filename=" + URLEncoder.encode(fileName, "UTF-8"));

            if (fileSaveChoose == 0) {
                // 从oss下载
                OSSObject ossObject = getFileFromOSS(fileInfo.getFileNameAlias());
                IOUtils.copy(ossObject.getObjectContent(), response.getOutputStream());
            } else {
                Path path = Paths.get(fileInfo.getRelativeDownloadUrl());
                log.info("下载路径" + path);
                if (Files.exists(path)) {
                    log.info("该文件路径：" + path);
                    Files.copy(path, response.getOutputStream());
                }  else {
                    log.error("文件不存在！");
                    throw new NoSuchFieldException();
                }
            }

        } catch (Exception e) {
            log.error("文件下载失败，异常：", e);
            throw new BusinessException("文件下载失败，异常：", e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public FileInfo upload(MultipartFile file, String saveSubDir) throws Exception {
        // 保存文件信息
        FileInfo fileInfo = new FileInfo();
        if (StpUtil.isLogin()) {
            fileInfo.setCreateUserId(StpUtil.getLoginIdAsString());
        }
        String originalFilename = file.getOriginalFilename();
        String ext = originalFilename != null && originalFilename.lastIndexOf('.') > 0 ? originalFilename.substring(originalFilename.lastIndexOf('.')) : ".pdf";
        String fileName;
        String fileId;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(originalFilename.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : md.digest()) {
                sb.append(String.format("%02x", b));
            }
            fileId = UUID.randomUUID().toString().replace("-", "");
            fileName = fileId + ext;
        } catch (NoSuchAlgorithmException e) {
            log.error("生成文件名hash失败: ", e);
            throw new RuntimeException("生成文件名hash失败" + e.getMessage());
        }
        String fullPath = "";
        boolean isOss = fileSaveChoose == 0;
        if (isOss) {
            saveFileToOSS(file, fileName);
        } else {
            String directoryPath = uploadRoot + File.separator + uploadDir + File.separator + saveSubDir;
            // 确保目标文件夹存在
            File dir = new File(directoryPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            fullPath = directoryPath + File.separator + fileName;
            try {
                file.transferTo(new File(fullPath));
            } catch (IOException e) {
                log.error("文件保存失败: ", e);
                throw new RuntimeException("文件保存失败! " + e.getMessage());
            }
        }

        fileInfo.setFileId(fileId);
        fileInfo.setOriginalFileName(originalFilename);
        fileInfo.setFileNameAlias(fileName);
        fileInfo.setRelativeDownloadUrl(fullPath);
        fileInfo.setDownloadUrl("/api/file/download?fileId=" + fileId);
        this.save(fileInfo);

        return fileInfo;
    }

    @Override
    public boolean delete(String fileId) {
        FileInfo fileInfo = this.getById(fileId);
        try {
            this.remove(Wrappers.lambdaQuery(FileInfo.class).eq(FileInfo::getFileId, fileId));
            boolean isOss = PROFILE_PROD.equals(profile);
            if (isOss) {
                deleteFileFromOSS(fileInfo.getOriginalFileName());
            } else {
                Path path = Paths.get(ResourceUtils.getURL("classpath:").getPath().substring(1), fileInfo.getRelativeDownloadUrl());
                if (Files.exists(path)) {
                    Files.delete(path);
                }
            }
        } catch (Exception e) {
            log.error("文件删除失败: ", e);
            throw new RuntimeException("文件删除失败! " + e.getMessage());
        }

        return true;
    }


    private String saveFileToOSS(final MultipartFile file, String storageFileName) throws IOException {
        ossClient.putObject(ossProperties.getBucketName(), "nanjing-complex" + File.separator + storageFileName, file.getInputStream());
        return storageFileName;
    }

    /**
     * 保存文件到oss上
     */
    public String saveTempFileToOSS(final File file, String storageFileName) throws IOException {
        ossClient.putObject(ossProperties.getBucketName(), storageFileName, Files.newInputStream(file.toPath()));
        return storageFileName;
    }

    public boolean deleteFileFromOSS(final String fileName) {
        ossClient.deleteObject(ossProperties.getBucketName(), "nanjing-complex" + File.separator + fileName);
        return true;
    }

    public OSSObject getFileFromOSS(final String fileName) {
        return ossClient.getObject(ossProperties.getBucketName(), "nanjing-complex" + File.separator + fileName);
    }

}
