package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 企业类型
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_type")
@ApiModel(value = "CompanyType对象", description = "企业类型")
public class CompanyType implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty("名称（企业类型）")
    private String name;

    @ApiModelProperty("英文名称（企业类型英文名）")
    private String nameEn;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否有效")
    private Byte isValid;

    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String NAME_EN = "name_en";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String IS_VALID = "is_valid";
}
