package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

import com.quantchi.common.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 申请文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_file")
@ApiModel(value = "ApplicationFile对象", description = "申请文件表")
@Slf4j
public class ApplicationFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private String id;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 流程ID */
    @ApiModelProperty("流程ID")
    private String processId;

    /**
     * 文件id
     */
    @ApiModelProperty("文件id")
    private String fileId;

    /** 文件名称 */
    @ApiModelProperty("文件名称")
    private String fileName;

    /** 文件路径 */
    @ApiModelProperty("文件路径")
    private String filePath;

    /** 文件类型：1-装修申请说明；2-营业执照；3-有效期内租赁合同；4-装修承诺书；5-设计院盖章的设计图纸（及电子版）；6-装修公司营业执照及资质证明等；7-审批材料*/
    @ApiModelProperty("文件类型：1-装修申请说明；2-营业执照；3-有效期内租赁合同；4-装修承诺书；5-设计院盖章的设计图纸（及电子版）；6-装修公司营业执照及资质证明等；7-审批材料")
    private Integer fileType;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String FILE_NAME = "file_name";

    public static final String FILE_PATH = "file_path";

    public static final String FILE_TYPE = "file_type";

    public static final String CREATE_TIME = "create_time";

    public static final String CREATE_USER = "create_user";

    public static final Integer DECORATE_APPLY = 1;

    public static final Integer BUSINESS_LICENSE = 2;

    public static final Integer LEASE_CONTRACT = 3;

    public static final Integer DECORATE_UNDERTAKING = 4;

    public static final Integer DESIGN_DRAWING = 5;

    public static final Integer DECORATE_COMPANY = 6;

    public static final Integer AUDIT = 7;

    public static final Integer SUPPLEMENTARY = 8;

    /**
     * 保存装修申请相关文件
     */
    public static ApplicationFile handleFile(Integer type, String mainId, String user, FileInfo file) {
        try {
            ApplicationFile applicationFile = new ApplicationFile();

            applicationFile.setId(UUID.randomUUID().toString().replace("-", ""));
            applicationFile.setApplicationId(mainId);
            applicationFile.setFileType(type);
            applicationFile.setFileId(file.getFileId());
            applicationFile.setFilePath(file.getDownloadUrl() + "&inline=true");
            applicationFile.setFileName(file.getOriginalFileName());
            applicationFile.setCreateUser(user);
            applicationFile.setCreateTime(LocalDateTime.now());

            return applicationFile;

        } catch (Exception e) {
            log.error("error occurred when handle application related file: ", e);
            throw e;
        }
    }

    /**
     * 保存装修申请相关文件
     */
    public static ApplicationFile handleFile(Integer type, String mainId, String processId, String user, FileInfo file) {
        try {
            ApplicationFile applicationFile = new ApplicationFile();

            applicationFile.setId(UUID.randomUUID().toString().replace("-", ""));
            applicationFile.setApplicationId(mainId);
            applicationFile.setProcessId(processId);
            applicationFile.setFileType(type);
            applicationFile.setFileId(file.getFileId());
            applicationFile.setFilePath(file.getDownloadUrl() + "&inline=true");
            applicationFile.setFileName(file.getOriginalFileName());
            applicationFile.setCreateUser(user);
            applicationFile.setCreateTime(LocalDateTime.now());

            return applicationFile;

        } catch (Exception e) {
            log.error("error occurred when handle application related file: ", e);
            throw e;
        }
    }
}
