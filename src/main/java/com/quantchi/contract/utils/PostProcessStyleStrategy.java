package com.quantchi.contract.utils;

import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

/**
 * 后处理样式策略，在所有数据写入完成后移除表头灰色背景
 */
public class PostProcessStyleStrategy implements WorkbookWriteHandler {

    @Override
    public void beforeWorkbookCreate() {
        // Workbook创建前执行
    }

    @Override
    public void afterWorkbookCreate(WriteWorkbookHolder writeWorkbookHolder) {
        // Workbook创建后执行
    }

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        // 在Workbook处理完成后执行，此时所有数据都已写入
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        
        // 遍历所有Sheet
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            processSheet(sheet, workbook);
        }
    }

    private void processSheet(Sheet sheet, Workbook workbook) {
        // 创建透明样式
        CellStyle transparentStyle = workbook.createCellStyle();
        transparentStyle.setFillPattern(FillPatternType.NO_FILL);
        
        // 设置边框
        transparentStyle.setBorderTop(BorderStyle.THIN);
        transparentStyle.setBorderBottom(BorderStyle.THIN);
        transparentStyle.setBorderLeft(BorderStyle.THIN);
        transparentStyle.setBorderRight(BorderStyle.THIN);
        
        // 设置字体
        Font font = workbook.createFont();
        font.setBold(false);
        font.setColor(IndexedColors.BLACK.getIndex());
        transparentStyle.setFont(font);
        
        // 设置对齐方式
        transparentStyle.setAlignment(HorizontalAlignment.CENTER);
        transparentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 处理前两行（表头行）
        for (int rowIndex = 0; rowIndex < Math.min(2, sheet.getLastRowNum() + 1); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                for (int colIndex = 0; colIndex < row.getLastCellNum(); colIndex++) {
                    Cell cell = row.getCell(colIndex);
                    if (cell != null) {
                        // 保留原有的值，只改变样式
                        String cellValue = "";
                        switch (cell.getCellType()) {
                            case STRING:
                                cellValue = cell.getStringCellValue();
                                break;
                            case NUMERIC:
                                cellValue = String.valueOf(cell.getNumericCellValue());
                                break;
                            default:
                                cellValue = cell.toString();
                                break;
                        }
                        
                        // 应用新样式
                        cell.setCellStyle(transparentStyle);
                        cell.setCellValue(cellValue);
                    }
                }
            }
        }
    }
}
