package com.quantchi.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.quantchi.application.model.entity.ApplicationFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel("文件信息")
@TableName("file_info")
public class FileInfo {

    @ApiModelProperty("文件id")
    @TableId(value = "id")
    private String fileId;

    @ApiModelProperty("创建用户id")
    @TableField(value = "create_user_id")
    private String createUserId;

    @ApiModelProperty("文件原始名称")
    @TableField(value = "original_file_name")
    private String originalFileName;

    @ApiModelProperty("文件别名")
    @TableField(value = "file_name_alias")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String fileNameAlias;

    @ApiModelProperty("文件相对下载地址")
    @TableField(value = "relative_download_url")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String relativeDownloadUrl;

    @ApiModelProperty("文件下载地址")
    @TableField(value = "download_url")
    private String downloadUrl;

    /**
     * 根据文件类型获取文件信息列表
     */
    public static List<FileInfo> getFileInfos(final List<ApplicationFile> fileList,
                                              final Integer fileType, String downloadAddress) {
        if (CollectionUtils.isEmpty(fileList)) {
            return new ArrayList<>();
        }

        return fileList.stream()
                .filter(file -> fileType.equals(file.getFileType()))
                .map(file -> {
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.setFileId(file.getFileId());
                    fileInfo.setOriginalFileName(file.getFileName());
                    fileInfo.setDownloadUrl(downloadAddress + file.getFilePath());
                    return fileInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据文件类型获取文件信息列表
     */
    public static List<FileInfo> getFileInfos(final String processId, final List<ApplicationFile> fileList,
                                              final Integer fileType, String downloadAddress) {
        if (CollectionUtils.isEmpty(fileList)) {
            return new ArrayList<>();
        }

        return fileList.stream()
                .filter(file -> fileType.equals(file.getFileType()) && processId.equals(file.getProcessId()))
                .map(file -> {
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.setFileId(file.getFileId());
                    fileInfo.setOriginalFileName(file.getFileName());
                    fileInfo.setDownloadUrl(downloadAddress + file.getFilePath());
                    return fileInfo;
                })
                .collect(Collectors.toList());
    }
}
