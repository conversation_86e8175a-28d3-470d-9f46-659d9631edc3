package com.quantchi.contract.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同空间关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("lease_contract_zone")
@ApiModel(value = "LeaseContractZone对象", description = "合同空间关联表")
public class LeaseContractZone implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("合同id")
    private String contractId;

    @ApiModelProperty("空间id")
    private String zoneId;

    public static final String ID = "id";

    public static final String CONTRACT_ID = "contract_id";

    public static final String ZONE_ID = "zone_id";
}
