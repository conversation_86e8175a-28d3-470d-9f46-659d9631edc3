package com.quantchi.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额工具类，提供数字金额转换为中文大写金额的功能
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public class MoneyUtils {

    /**
     * 中文数字
     */
    private static final String[] CN_NUMBERS = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
    
    /**
     * 中文金额单位（个位开始）
     */
    private static final String[] CN_INTEGER_UNIT = {"元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "兆"};
    
    /**
     * 中文金额单位（小数部分）
     */
    private static final String[] CN_DECIMAL_UNIT = {"角", "分"};
    
    /**
     * 特殊字符：整
     */
    private static final String CN_FULL = "整";
    
    /**
     * 特殊字符：负
     */
    private static final String CN_NEGATIVE = "负";
    
    /**
     * 特殊字符：人民币
     */
    private static final String CN_YUAN = "人民币";
    
    /**
     * 将数字金额转换为中文大写金额
     *
     * @param money 金额，支持到万亿级别
     * @return 中文大写金额
     */
    public static String toChinese(Number money) {
        if (money == null) {
            return "";
        }
        
        // 转为BigDecimal并进行四舍五入，保留2位小数
        BigDecimal numberOfMoney = new BigDecimal(money.toString());
        numberOfMoney = numberOfMoney.setScale(2, RoundingMode.HALF_UP);
        
        // 如果为0，则直接返回零元整
        if (numberOfMoney.compareTo(BigDecimal.ZERO) == 0) {
            return CN_YUAN + CN_NUMBERS[0] + CN_INTEGER_UNIT[0] + CN_FULL;
        }
        
        // 负数处理
        boolean isNegative = false;
        if (numberOfMoney.compareTo(BigDecimal.ZERO) < 0) {
            isNegative = true;
            numberOfMoney = numberOfMoney.abs();
        }
        
        // 将金额拆分为整数部分和小数部分
        String strValue = numberOfMoney.toString();
        int dotIndex = strValue.indexOf(".");
        String integerStr = dotIndex != -1 ? strValue.substring(0, dotIndex) : strValue;
        String decimalStr = dotIndex != -1 ? strValue.substring(dotIndex + 1) : "";
        
        // 补齐小数部分为两位
        if (decimalStr.length() < 2) {
            StringBuilder sb = new StringBuilder(decimalStr);
            for (int i = 0; i < 2 - decimalStr.length(); i++) {
                sb.append("0");
            }
            decimalStr = sb.toString();
        }
        
        // 构建大写金额
        StringBuilder sb = new StringBuilder();
        
        // 添加前缀和负号（如果需要）
        sb.append(CN_YUAN);
        if (isNegative) {
            sb.append(CN_NEGATIVE);
        }
        
        // 处理整数部分
        if (!"0".equals(integerStr)) {
            sb.append(convertIntegerPart(integerStr));
        }
        
        // 处理小数部分
        if ("00".equals(decimalStr)) {
            // 如果没有小数部分，添加"元整"
            if (!"0".equals(integerStr)) {
                // 检查最后是否已经有"元"
                if (!sb.toString().endsWith(CN_INTEGER_UNIT[0])) {
                    sb.append(CN_INTEGER_UNIT[0]);
                }
                sb.append(CN_FULL);
            } else {
                // 如果整数部分也是0，表示为"零元整"
                sb.append(CN_NUMBERS[0]).append(CN_INTEGER_UNIT[0]).append(CN_FULL);
            }
        } else {
            // 处理特殊情况：如果整数部分为0，且小数部分不为0
            if ("0".equals(integerStr)) {
                // 不添加"元"，直接处理小数部分
            } else {
                // 确保添加"元"
                if (!sb.toString().endsWith(CN_INTEGER_UNIT[0])) {
                    sb.append(CN_INTEGER_UNIT[0]);
                }
            }
            
            // 处理角
            if (decimalStr.charAt(0) != '0') {
                sb.append(CN_NUMBERS[decimalStr.charAt(0) - '0']).append(CN_DECIMAL_UNIT[0]);
            } else {
                // 处理特殊情况：角为0但分不为0
                if (decimalStr.charAt(1) != '0') {
                    sb.append(CN_NUMBERS[0]);
                }
            }
            
            // 处理分
            if (decimalStr.charAt(1) != '0') {
                sb.append(CN_NUMBERS[decimalStr.charAt(1) - '0']).append(CN_DECIMAL_UNIT[1]);
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 处理整数部分
     */
    private static String convertIntegerPart(String integerStr) {
        StringBuilder sb = new StringBuilder();
        int length = integerStr.length();
        boolean hasYuan = false; // 标记是否已添加"元"单位
        
        // 处理整数部分
        for (int i = 0; i < length; i++) {
            int digit = integerStr.charAt(i) - '0';
            int unitIndex = length - i - 1; // 单位索引
            
            if (digit == 0) {
                // 处理数字为0的情况
                
                // 当数字为0但不是个位，且下一位不是万位或亿位或元位时，不需要添加零
                if (i < length - 1 && integerStr.charAt(i + 1) != '0') {
                    sb.append(CN_NUMBERS[digit]);
                }
                
                // 如果是万位或亿位且其前面的数字不全为0，则显示万位或亿位单位
                if (unitIndex == 4 || unitIndex == 8) {
                    boolean hasNonZero = false;
                    // 检查前面是否有非零数字
                    for (int j = 0; j < i; j++) {
                        if (integerStr.charAt(j) != '0') {
                            hasNonZero = true;
                            break;
                        }
                    }
                    
                    if (hasNonZero) {
                        sb.append(CN_INTEGER_UNIT[unitIndex]);
                    }
                }
            } else {
                // 非零数字：显示数字和对应单位
                sb.append(CN_NUMBERS[digit]);
                
                // 添加单位
                sb.append(CN_INTEGER_UNIT[unitIndex]);
                
                // 如果是元位，标记已添加元
                if (unitIndex == 0) {
                    hasYuan = true;
                }
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 简化版：将数字金额转换为中文大写金额（不带"人民币"前缀）
     *
     * @param money 金额
     * @return 中文大写金额
     */
    public static String toChineseSimple(Number money) {
        return toChinese(money).replaceFirst(CN_YUAN, "");
    }
}
