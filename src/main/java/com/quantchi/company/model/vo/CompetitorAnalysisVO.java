package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/27 下午4:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompetitorAnalysisVO {

    @ApiModelProperty(value = "指标名称")
    private String indexName;

    @ApiModelProperty(value = "当前公司的值")
    private String currentValue;

    @ApiModelProperty(value = "竞争对手的值")
    private String competitorValue;

}
