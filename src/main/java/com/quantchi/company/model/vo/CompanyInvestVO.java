package com.quantchi.company.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@ApiModel(value = "对外投资企业信息")
public class CompanyInvestVO implements Serializable {

    @ApiModelProperty("被投企业ID")
    private String investedId;

    @ApiModelProperty("持股比例")
    private String investRatio;

    @ApiModelProperty("认缴出资额")
    private String investAmount;

    @ApiModelProperty("被投企业名称")
    private String investedName;

    @ApiModelProperty("被投企业成立日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date investedEstablishedDate;

    @ApiModelProperty("被投企业经营状态")
    private String investedStatus;

    @ApiModelProperty("被投企业注册行业")
    private String investedIndustry;

    @ApiModelProperty("所属地区")
    private String belongArea;

    @ApiModelProperty("投资特征")
    private String investType;


}
