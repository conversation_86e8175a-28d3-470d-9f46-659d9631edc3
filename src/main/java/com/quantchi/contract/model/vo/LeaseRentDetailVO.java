package com.quantchi.contract.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
@ApiModel("租金明细及扩展信息展示")
public class LeaseRentDetailVO extends LeaseContractBaseVO {

    @ApiModelProperty("承租方")
    private String lessee;

    @ApiModelProperty("租赁起止时间（yyyy.MM.dd-yyyy.MM.dd）")
    private String leasePeriod;

    @ApiModelProperty("租金明细列表")
    private List<LeaseRentVO> rentList;
}
