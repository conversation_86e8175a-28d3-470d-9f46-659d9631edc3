package com.quantchi.application.model.vo;

import com.quantchi.common.model.FileInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ProcessVO {
    /** 处理人 */
    @ApiModelProperty("处理人：发起人/审批人")
    private String processor;

    /** 审批流程 */
    @ApiModelProperty("审批流程")
    private String process;
    /**
     * 流转给
     */
    @ApiModelProperty("流转给")
    private String transferTo;

    /** 审批时间 */
    @ApiModelProperty("审批时间")
    private LocalDate processTime;

    /** 审批状态：1-已同意；0-已驳回 */
    @ApiModelProperty("审批状态：1-已同意；0-已驳回")
    private Integer processStatus;

    /** 审批状态：1-已同意；0-已驳回 */
    @ApiModelProperty("审批状态：1-已同意；0-已驳回")
    private String processStatusCN;

    /** 审批意见 */
    @ApiModelProperty("审批意见")
    private String processAdvice;

    /** 审批材料
     *
     */
    @ApiModelProperty("审批材料")
    private List<FileInfo> auditFiles;
}
