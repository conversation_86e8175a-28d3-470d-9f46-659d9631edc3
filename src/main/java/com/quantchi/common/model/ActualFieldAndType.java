package com.quantchi.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 15:16
 */
@ApiModel("字段实际类型")
@Data
public class ActualFieldAndType {

    @ApiModelProperty("字段名称")
    private String field;

    @ApiModelProperty("字段类型")
    private String type;

    /**
     * 如果是嵌套类型，那么分内部和外部字段
     */
    @ApiModelProperty("嵌套类型的外部字段名称")
    private String outerField;

    @ApiModelProperty("嵌套类型的内部字段名称")
    private String innerField;

    @ApiModelProperty("内部字段的类型")
    private String innerType;

    @ApiModelProperty("关键词")
    private List<String> keywordList;

}
