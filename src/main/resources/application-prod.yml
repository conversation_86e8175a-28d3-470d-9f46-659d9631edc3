es:
  cluster:
    hosts: ************:8199
  username: elastic
  password: LrbJAJyqPxy8Ly987

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ******************************************************************************************************************************************************************************************************************************************
    username: njxggx
    password: "LrbJAJyqPxy8Ly3306"
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池配置
    hikari:
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接数
      maximum-pool-size: 15
      # 空闲连接超时时间，默认600000（10分钟）
      idle-timeout: 600000
      # 连接最大存活时间，0表示永久存活，默认1800000（30分钟）
      max-lifetime: 1800000
      # 连接超时时间，默认30000（30秒）
      connection-timeout: 30000
      # 用于测试连接是否可用的查询语句
      connection-test-query: SELECT 1
  redis:
    database: 13
    host: ************
    port: 6379
    # Redis服务器连接密码
    username: njxggx
    password: LrbJAJyqPxy8Ly6379
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

self:
  address: http://************/

# 文件上传目录
file:
  save:
    choose: 0  #0为oss 1为本地
