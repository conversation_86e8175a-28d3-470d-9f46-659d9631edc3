package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 企业注册形式：实体注册/虚拟注册
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_registration_form")
@ApiModel(value = "CompanyRegistrationForm对象", description = "企业注册形式：实体注册/虚拟注册")
public class CompanyRegistrationForm implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("企业ID")
    private String cid;

    @ApiModelProperty("是否实体：0-虚拟；1-实体")
    private Integer isEntity;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String IS_ENTITY = "is_entity";
}
