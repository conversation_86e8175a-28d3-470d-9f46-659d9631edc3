package com.quantchi.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.application.model.bo.AddContactBO;
import com.quantchi.application.model.bo.ContactListQueryBO;
import com.quantchi.application.model.bo.EditContactBO;
import com.quantchi.application.model.entity.ApplicationContact;
import com.quantchi.application.mapper.ApplicationContactMapper;
import com.quantchi.application.model.vo.CompanyContactVO;
import com.quantchi.application.model.vo.ContactListPageVO;
import com.quantchi.application.service.IApplicationContactService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.sys.mapper.SysUserMapper;
import com.quantchi.sys.model.entity.SysUser;
import com.quantchi.sys.service.impl.SysUserService;
import com.quantchi.sys.utils.AesUtil;
import com.quantchi.sys.utils.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.AbstractList;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@Slf4j
public class ApplicationContactServiceImpl extends ServiceImpl<ApplicationContactMapper, ApplicationContact> implements IApplicationContactService {

    @Autowired
    private SysUserMapper sysUserMapper;
    @Override
    public List<CompanyContactVO> getContactsByCompanyId(String companyId) {

        // 查询指定企业的所有联系人
        List<ApplicationContact> contacts = this.list(
            Wrappers.lambdaQuery(ApplicationContact.class)
                .eq(ApplicationContact::getCompanyId, companyId)
                .orderByDesc(ApplicationContact::getCreateTime)
        );

        List<CompanyContactVO> res = contacts.stream().map(this::convertToVO).collect(Collectors.toList());

        List<SysUser> sysUsers = sysUserMapper.selectList(
                Wrappers.lambdaQuery(SysUser.class)
                        .eq(SysUser::getCompanyId, companyId)
        );

        res.addAll(sysUsers.stream().map(this::convertToVO).collect(Collectors.toList()));

        return res;

    }

    @Override
    public ContactListPageVO pageContactsByCondition(ContactListQueryBO queryBO) {
        // 1. 查询ApplicationContact表中的联系人（分页）
        LambdaQueryWrapper<ApplicationContact> contactWrapper = new LambdaQueryWrapper<>();
        contactWrapper.eq(ApplicationContact::getCompanyId, queryBO.getCompanyId());

        // 添加关键字搜索条件
        if (StringUtils.isNotBlank(queryBO.getKeyword())) {
            contactWrapper.and(wrapper -> wrapper
                .like(ApplicationContact::getName, queryBO.getKeyword())
                .or().like(ApplicationContact::getPosition, queryBO.getKeyword())
            );
        }

        // 添加在职状态过滤
        if (queryBO.getJobStatus() != null) {
            contactWrapper.eq(ApplicationContact::getJobStatus, queryBO.getJobStatus());
        }

        contactWrapper.orderByDesc(ApplicationContact::getCreateTime);

        Page<ApplicationContact> contactPage = new Page<>(queryBO.getPageNum(), queryBO.getPageSize());
        IPage<ApplicationContact> contactResult = this.page(contactPage, contactWrapper);

        // 2. 查询SysUser表中的联系人（不分页，因为通常数量较少）
        List<SysUser> sysUsers = sysUserMapper.selectList(
            Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getCompanyId, queryBO.getCompanyId())
        );

        // 3. 转换ApplicationContact为VO
        List<CompanyContactVO> contactVOList = contactResult.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        // 4. 转换SysUser为VO并添加到结果中（如果有关键字搜索，需要过滤）
        List<CompanyContactVO> userVOList = sysUsers.stream()
            .map(this::convertToVO)
            .filter(vo -> {
                // 如果有关键字搜索，需要过滤SysUser数据
                if (StringUtils.isNotBlank(queryBO.getKeyword())) {
                    String keyword = queryBO.getKeyword().toLowerCase();
                    return (vo.getName() != null && vo.getName().toLowerCase().contains(keyword)) ||
                           (vo.getPosition() != null && vo.getPosition().toLowerCase().contains(keyword));
                }
                return true;
            })
            .filter(vo -> {
                // 如果有在职状态过滤，需要过滤SysUser数据
                if (queryBO.getJobStatus() != null) {
                    return vo.getJobStatus() != null && vo.getJobStatus().equals(queryBO.getJobStatus());
                }
                return true;
            })
            .collect(Collectors.toList());

        // 5. 合并结果
        contactVOList.addAll(userVOList);

        // 6. 构建分页结果
        ContactListPageVO pageVO = new ContactListPageVO();
        pageVO.setTotal(contactResult.getTotal() + userVOList.size());
        pageVO.setPageSize(queryBO.getPageSize());
        pageVO.setPageNum(queryBO.getPageNum());
        pageVO.setPages((long) Math.ceil((double) (contactResult.getTotal() + userVOList.size()) / queryBO.getPageSize()));
        pageVO.setRecords(contactVOList);

        return pageVO;
    }

    /**
     * 入驻申请联系人
     */
    private CompanyContactVO convertToVO(ApplicationContact contact) {
        CompanyContactVO vo = new CompanyContactVO();
        BeanUtils.copyProperties(contact, vo);
        vo.setContactInfo(contact.getMobile());
        if (StringUtils.isNotBlank(contact.getApplicationId())) {
            vo.setCreateUser("入驻申请联系人");
        }

        return vo;
    }

    /**
     * 用户注册
     */
    private CompanyContactVO convertToVO(SysUser sysUser) {
        CompanyContactVO vo = new CompanyContactVO();
        BeanUtils.copyProperties(sysUser, vo);
        vo.setName(sysUser.getRealName());
        vo.setContactInfo(AesUtil.decrypt(sysUser.getPhonenumber()));
        vo.setId(String.valueOf(sysUser.getUserId()));
        vo.setCreateUser("用户注册");

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editContact(EditContactBO editContactBO) {
        try {

            boolean applicationContactUpdated = false;
            boolean sysUserUpdated = false;

            // 1. 尝试在ApplicationContact表中查询和更新
            ApplicationContact applicationContact = this.getById(editContactBO.getContactId());
            if (applicationContact != null) {
                updateApplicationContact(applicationContact, editContactBO);
                applicationContactUpdated = this.updateById(applicationContact);
            }

            // 2. 尝试在SysUser表中查询和更新
            List<SysUser> sysUsers = sysUserMapper.selectList(
                    Wrappers.lambdaQuery(SysUser.class)
                            .eq(SysUser::getUserId, editContactBO.getContactId())
            );

            if (!sysUsers.isEmpty()) {
                SysUser sysUser = sysUsers.get(0); // 取第一个匹配的用户
                updateSysUser(sysUser, editContactBO);
                sysUserUpdated = sysUserMapper.updateById(sysUser) > 0;
            }

            boolean result = applicationContactUpdated || sysUserUpdated;

            return result;

        } catch (Exception e) {
            log.error("编辑联系人信息失败，联系人ID：{}", editContactBO.getContactId(), e);
            throw e;
        }
    }

    /**
     * 更新ApplicationContact对象
     */
    private void updateApplicationContact(ApplicationContact contact, EditContactBO editContactBO) {
        contact.setName(editContactBO.getName());
        contact.setMobile(editContactBO.getContactInfo());
        contact.setPosition(editContactBO.getPosition());
        contact.setJobStatus(editContactBO.getJobStatus());
        contact.setRemark(editContactBO.getRemark());
    }

    /**
     * 更新SysUser对象
     */
    private void updateSysUser(SysUser sysUser, EditContactBO editContactBO) {
        sysUser.setRealName(editContactBO.getName());
        sysUser.setPosition(editContactBO.getPosition());

        // 手机号需要加密存储
        if (StringUtils.isNotBlank(editContactBO.getContactInfo())) {
            sysUser.setPhonenumber(AesUtil.encrypt(editContactBO.getContactInfo()));
        }
        sysUser.setJobStatus(editContactBO.getJobStatus());
        sysUser.setRemark(editContactBO.getRemark());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addContact(AddContactBO addContactBO) {
        try {

            // 创建ApplicationContact对象
            ApplicationContact contact = new ApplicationContact();

            // 生成32位UUID作为ID
            String contactId = UUID.randomUUID().toString().replace("-", "");
            contact.setId(contactId);

            // 设置基本信息
            contact.setCompanyId(addContactBO.getCompanyId());
            contact.setName(addContactBO.getName());
            contact.setMobile(addContactBO.getContactInfo());
            contact.setPosition(addContactBO.getPosition());
            contact.setJobStatus(addContactBO.getJobStatus() != null ? addContactBO.getJobStatus() : 1);
            contact.setRemark(addContactBO.getRemark());

            // 设置创建信息
            contact.setCreateTime(LocalDateTime.now());

            // 获取当前登录用户作为创建人
            String currentUserName = LoginHelper.getUsername();
            contact.setCreateUser(currentUserName);

            // 保存到数据库
            boolean result = this.save(contact);

            return result;

        } catch (Exception e) {
            log.error("新增企业联系人失败，企业ID：{}，姓名：{}", addContactBO.getCompanyId(), addContactBO.getName(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContact(String contactId) {
        try {

            boolean applicationContactDeleted = false;
            boolean sysUserDeleted = false;

            // 1. 尝试在ApplicationContact表中查询和删除
            ApplicationContact applicationContact = this.getById(contactId);
            if (applicationContact != null) {
                applicationContactDeleted = this.removeById(contactId);
            } else {
                log.info("ApplicationContact表中未找到联系人，联系人ID：{}", contactId);
            }

            // 2. 尝试在SysUser表中查询和删除（通过userId匹配）
            try {
                // 尝试将contactId转换为Long类型的userId
                Long userId = Long.valueOf(contactId);
                SysUser sysUser = sysUserMapper.selectById(userId);
                if (sysUser != null) {
                    int deleteResult = sysUserMapper.deleteById(userId);
                    sysUserDeleted = deleteResult > 0;
                } else {
                    log.info("SysUser表中未找到用户，用户ID：{}", userId);
                }
            } catch (NumberFormatException e) {
                log.info("联系人ID不是有效的用户ID格式，跳过SysUser表删除，联系人ID：{}", contactId);
            }

            boolean result = applicationContactDeleted || sysUserDeleted;

            if (!result) {
                log.warn("未在任何表中找到并删除联系人，联系人ID：{}", contactId);
            }

            return result;

        } catch (Exception e) {
            log.error("删除企业联系人失败，联系人ID：{}", contactId, e);
            throw e;
        }
    }

}
