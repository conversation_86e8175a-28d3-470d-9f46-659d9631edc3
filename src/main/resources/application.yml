spring:
  profiles:
    active: dev
  application:
    name: nanjing_complex
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

mybatis-plus:
  config-location: classpath:mybatis/mybatis-config.xml
  mapper-locations: classpath:mybatis/mapper/*.xml

server:
  port: 8806
  servlet:
    context-path: /api
  tomcat:
    uri-encoding: UTF-8
logging:
  config: classpath:logback.xml
  level:
    org.springframework: INFO


# 文件上传目录
file:
  save:
    choose: 1  #0为oss 1为本地
  upload:
    root: /home/<USER>/nanjing_complex
    dir: upload
    contractScanCopy: contractScanCopy
    investAgreement: investAgreement
    decorateApply: decorateApply
    # 临时文件存放目录
    temp: temp

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认30分钟）
    lockTime: 30

# Sa-Token配置
sa-token:
  # token 名称 (同时也是cookie名称)
  token-name: Authorization
  # token 有效期，单位s 目前设置为8小时, -1代表永不过期
  timeout: 604800
  # token 临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  maxLoginCount: 100
  oauth2:
    is-client: true

## 阿里云 OSS配置
#aliyun:
#  oss:
#    keyId: LTAI5tFhcd5AKJhpJEXhMo7B
#    keySecret: ******************************
#    bucketName: oss-prod-njxggx
#    endpoint: nanjing-1622348395202064.oss-cn-hangzhou-internal.aliyuncs.com

# 阿里云 OSS配置
aliyun:
  oss:
    keyId: LTAI5t5hAajzzsroaDmsUM2w
    keySecret: ******************************
    bucketName: cz-lcw
    endpoint: oss-cn-hangzhou.aliyuncs.com

