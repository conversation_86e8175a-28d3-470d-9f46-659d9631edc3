package com.quantchi.common.component;

import com.quantchi.application.model.entity.CompanyType;
import com.quantchi.application.model.entity.IndustryChain;
import com.quantchi.application.model.entity.MoveOutReason;
import com.quantchi.application.service.impl.CompanyTypeServiceImpl;
import com.quantchi.application.service.impl.IndustryChainServiceImpl;
import com.quantchi.application.service.impl.MoveOutReasonServiceImpl;
import com.quantchi.common.domain.InMemoryCache;
import com.quantchi.contract.model.entity.*;
import com.quantchi.contract.service.impl.*;
import com.quantchi.sys.model.entity.SysOrg;
import com.quantchi.sys.service.impl.SysOrgServiceImpl;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.ZoneFunction;
import com.quantchi.zone.service.impl.ParkServiceImpl;
import com.quantchi.zone.service.impl.ZoneFunctionServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Component
@RequiredArgsConstructor
public class DataInitializerCompopnent {

    private final SysOrgServiceImpl sysOrgService;
    private final ParkServiceImpl parkingService;
    private final ZoneFunctionServiceImpl zoneFunctionService;
    private final LeaseFunctionServiceImpl leaseFunctionService;
    private final UtilitiesDepositStandardServiceImpl utilitiesDepositStandardService;
    private final PowerConsumptionStandardServiceImpl powerConsumptionStandardService;
    private final FirstRentPayTimeServiceImpl firstRentPayTimeService;
    private final DecorateTimeLimitServiceImpl decorateTimeLimitService;
    private final IndustryChainServiceImpl industryChainService;
    private final CompanyTypeServiceImpl companyTypeService;
    private final MoveOutReasonServiceImpl moveOutReasonService;

    @PostConstruct
    public void init() {

        System.out.println("初始化数据...");

        List<SysOrg> sysOrgList = sysOrgService.list();
        List<Park> parkingList = parkingService.list();
        List<ZoneFunction> zoneFunctions = zoneFunctionService.list();
        List<LeaseFunction> leaseFunctions = leaseFunctionService.list();
        List<UtilitiesDepositStandard> utilitiesDepositStandardList =  utilitiesDepositStandardService.list();
        List<PowerConsumptionStandard> powerConsumptionStandardList = powerConsumptionStandardService.list();
        List<FirstRentPayTime> firstRentPayTimeList = firstRentPayTimeService.list();
        List<DecorateTimeLimit> decorateTimeLimitList = decorateTimeLimitService.list();
        List<IndustryChain> industryChainList = industryChainService.list();
        List<CompanyType> companyTypeList = companyTypeService.list();
        List<MoveOutReason> moveOutReasonList = moveOutReasonService.list();

        InMemoryCache.setData(sysOrgList, parkingList, zoneFunctions, leaseFunctions, utilitiesDepositStandardList,
                powerConsumptionStandardList, firstRentPayTimeList, decorateTimeLimitList, industryChainList, companyTypeList,
                moveOutReasonList);

        System.out.println("初始化数据完成...");
    }
}
