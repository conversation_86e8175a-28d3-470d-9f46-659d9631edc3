<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.overview.mapper.OverviewMapper">

    <!-- 查询新入驻企业列表 -->
    <select id="pageNewCompanies" resultType="com.quantchi.overview.model.vo.NewCompanyVO">
        SELECT DISTINCT
            c.name AS companyName,
            a.finish_time AS entryTime,
            CONCAT(lc.lease_start_time, ' - ', lc.lease_end_time) AS contractPeriod,
            GROUP_CONCAT(lcz.zone_id) AS roomNumber,
            lc.rental_collect AS contractAmount
        FROM
            application a
            INNER JOIN application_move_in ami ON a.id = ami.application_id
            INNER JOIN company c ON ami.company_id = c.id
            INNER JOIN (
                SELECT lc1.*
                FROM lease_contract lc1
                INNER JOIN (
                    SELECT lessee_id, MAX(lease_start_time) AS latest_start_time 
                    FROM lease_contract 
                    GROUP BY lessee_id
                ) lc2 ON lc1.lessee_id = lc2.lessee_id AND lc1.lease_start_time = lc2.latest_start_time
            ) lc ON c.id = lc.lessee_id
            INNER JOIN lease_contract_zone lcz ON lc.id = lcz.contract_id
        WHERE
            a.apply_type = 1  -- 申请类型为入驻
            AND a.status = 1  -- 审批通过
        GROUP BY
            c.name, a.finish_time, lc.lease_start_time, lc.lease_end_time, lc.rental_collect
        ORDER BY
            a.finish_time DESC
    </select>
    
    <!-- 根据园区ID查询新入驻企业列表 -->
    <select id="pageNewCompaniesByPark" resultType="com.quantchi.overview.model.vo.NewCompanyVO">
        SELECT DISTINCT
            c.name AS companyName,
            a.finish_time AS entryTime,
            CONCAT(lc.lease_start_time, ' - ', lc.lease_end_time) AS contractPeriod,
            GROUP_CONCAT(lcz.zone_id) AS roomNumber,
            lc.rental_collect AS contractAmount
        FROM
            application a
            INNER JOIN application_move_in ami ON a.id = ami.application_id
            INNER JOIN company c ON ami.company_id = c.id
            INNER JOIN (
                SELECT lc1.*
                FROM lease_contract lc1
                INNER JOIN (
                    SELECT lessee_id, MAX(lease_start_time) AS latest_start_time 
                    FROM lease_contract 
                    GROUP BY lessee_id
                ) lc2 ON lc1.lessee_id = lc2.lessee_id AND lc1.lease_start_time = lc2.latest_start_time
            ) lc ON c.id = lc.lessee_id
            INNER JOIN lease_contract_zone lcz ON lc.id = lcz.contract_id
        WHERE
            a.apply_type = 1  -- 申请类型为入驻
            AND a.status = 1  -- 审批通过
            AND lcz.zone_id IN (SELECT z.id FROM zone z WHERE z.park_id = #{parkId})  -- 按园区ID筛选
        GROUP BY
            c.name, a.finish_time, lc.lease_start_time, lc.lease_end_time, lc.rental_collect
        ORDER BY
            a.finish_time DESC
    </select>

    <!-- 查询合同即将到期企业列表 -->
    <select id="pageExpiringContracts" resultType="com.quantchi.overview.model.vo.ExpiringContractVO">
        SELECT
            lc.id AS contractId,
            c.name AS companyName,
            MIN(a.finish_time) AS entryTime,
            GROUP_CONCAT(lcz.zone_id) AS roomNumber,
            DATEDIFF(lc.lease_end_time, #{currentDate}) AS remainingDays,
            lc.rental_collect AS contractAmount,
            CASE
                WHEN EXISTS (
                    SELECT 1 FROM lease_contract lc2
                    WHERE lc2.lessee_id = lc.lessee_id
                    AND lc2.lease_start_time > lc.lease_end_time
                ) THEN '已续租'
                ELSE '未续租'
            END AS renewalStatus
        FROM
            lease_contract lc
            INNER JOIN company c ON lc.lessee_id = c.id
            INNER JOIN application_move_in ami ON ami.company_id = c.id
            INNER JOIN application a ON a.id = ami.application_id
            INNER JOIN lease_contract_zone lcz ON lc.id = lcz.contract_id
        WHERE
            lc.lease_end_time BETWEEN #{currentDate} AND #{expiringDate}
        GROUP BY
            lc.id, c.name, lc.lease_end_time, lc.rental_collect
        ORDER BY
            remainingDays ASC
    </select>
    
    <!-- 根据园区ID查询合同即将到期企业列表 -->
    <select id="pageExpiringContractsByPark" resultType="com.quantchi.overview.model.vo.ExpiringContractVO">
        SELECT
            lc.id AS contractId,
            c.name AS companyName,
            MIN(a.finish_time) AS entryTime,
            GROUP_CONCAT(lcz.zone_id) AS roomNumber,
            DATEDIFF(lc.lease_end_time, #{currentDate}) AS remainingDays,
            lc.rental_collect AS contractAmount,
            CASE
                WHEN EXISTS (
                    SELECT 1 FROM lease_contract lc2
                    WHERE lc2.lessee_id = lc.lessee_id
                    AND lc2.lease_start_time > lc.lease_end_time
                ) THEN '已续租'
                ELSE '未续租'
            END AS renewalStatus
        FROM
            lease_contract lc
            INNER JOIN company c ON lc.lessee_id = c.id
            INNER JOIN application_move_in ami ON ami.company_id = c.id
            INNER JOIN application a ON a.id = ami.application_id
            INNER JOIN lease_contract_zone lcz ON lc.id = lcz.contract_id
        WHERE
            lc.lease_end_time BETWEEN #{currentDate} AND #{expiringDate}
            AND lcz.zone_id IN (SELECT z.id FROM zone z WHERE z.park_id = #{parkId})  -- 按园区ID筛选
        GROUP BY
            lc.id, c.name, lc.lease_end_time, lc.rental_collect
        ORDER BY
            remainingDays ASC
    </select>

    <!-- 查询当前年份企业入驻增长趋势（按月统计） -->
    <select id="queryMoveInTrend" resultType="com.quantchi.overview.model.vo.MoveInTrendVO">
        SELECT 
            monthValue AS month,
            COUNT(DISTINCT companyId) AS companyCount
        FROM (
            SELECT 
                CONCAT(MONTH(a.finish_time), '月') AS monthValue,
                ami.company_id AS companyId
            FROM
                application a
                INNER JOIN application_move_in ami ON a.id = ami.application_id
                INNER JOIN company c ON ami.company_id = c.id
                INNER JOIN lease_contract lc ON c.id = lc.lessee_id
            WHERE
                a.apply_type = 1  -- 申请类型为入驻
                AND a.status = 1  -- 审批通过
                AND YEAR(a.finish_time) = #{year}
        ) t
        GROUP BY 
            monthValue
        ORDER BY
            LENGTH(monthValue), monthValue
    </select>
    
    <!-- 根据园区ID查询当前年份企业入驻增长趋势（按月统计） -->
    <select id="queryMoveInTrendByPark" resultType="com.quantchi.overview.model.vo.MoveInTrendVO">
        SELECT 
            monthValue AS month,
            COUNT(DISTINCT companyId) AS companyCount
        FROM (
            SELECT 
                CONCAT(MONTH(a.finish_time), '月') AS monthValue,
                ami.company_id AS companyId
            FROM
                application a
                INNER JOIN application_move_in ami ON a.id = ami.application_id
                INNER JOIN company c ON ami.company_id = c.id
                INNER JOIN lease_contract lc ON c.id = lc.lessee_id
                INNER JOIN lease_contract_zone lcz ON lc.id = lcz.contract_id
            WHERE
                a.apply_type = 1  -- 申请类型为入驻
                AND a.status = 1  -- 审批通过
                AND YEAR(a.finish_time) = #{year}
                AND lcz.zone_id IN (SELECT z.id FROM zone z WHERE z.park_id = #{parkId})  -- 按园区ID筛选
        ) t
        GROUP BY 
            monthValue
        ORDER BY
            LENGTH(monthValue), monthValue
    </select>
</mapper>
