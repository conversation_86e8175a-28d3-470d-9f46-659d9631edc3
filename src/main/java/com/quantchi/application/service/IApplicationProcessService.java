package com.quantchi.application.service;

import com.quantchi.application.model.entity.ApplicationFile;
import com.quantchi.application.model.entity.ApplicationProcess;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.application.model.vo.ProcessVO;

import java.util.List;

/**
 * <p>
 * 审批流程 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IApplicationProcessService extends IService<ApplicationProcess> {

    List<ProcessVO> getProcessVOList(String applicationId, List<ApplicationFile> fileList);

}
