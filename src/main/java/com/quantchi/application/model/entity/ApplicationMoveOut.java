package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 迁出申请内容表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_move_out")
@ApiModel(value = "ApplicationMoveOut对象", description = "迁出申请内容表")
public class ApplicationMoveOut implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private String id;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 公司ID */
    @ApiModelProperty("公司ID")
    private String companyId;

    /** 注册资本 */
    @ApiModelProperty("注册资本")
    private String registeredCapital;

    /** 成立时间 */
    @ApiModelProperty("成立时间")
    private LocalDate establishmentDate;

    /** 入驻时间 */
    @ApiModelProperty("入驻时间")
    private LocalDate moveInDate;

    /** 登记地址 */
    @ApiModelProperty("登记地址")
    private String registeredAddress;

    /** 实际地址 */
    @ApiModelProperty("实际地址")
    private String actualAddress;

    /** 收入总额 */
    @ApiModelProperty("收入总额")
    private String totalRevenue;

    /** 总利润 */
    @ApiModelProperty("总利润")
    private String totalProfit;

    /** 净利润 */
    @ApiModelProperty("净利润")
    private String netProfit;

    /** 发明专利 */
    @ApiModelProperty("发明专利")
    private String inventionPatents;

    /** 软著/实用新型 */
    @ApiModelProperty("软著/实用新型")
    private String softwareOrUtilityPatents;

    /** 科技研发人员 */
    @ApiModelProperty("科技研发人员")
    private String techStaff;

    /** 累计研发经费 */
    @ApiModelProperty("累计研发经费")
    private String totalRAndDExpenses;

    /** 纳税总额 */
    @ApiModelProperty("纳税总额")
    private String totalTax;

    /** 企业所得税 */
    @ApiModelProperty("企业所得税")
    private String corporateIncomeTax;

    /** 增值税 */
    @ApiModelProperty("增值税")
    private String valueAddedTax;

    /** 营业税 */
    @ApiModelProperty("营业税")
    private String businessTax;

    /** 个人所得税 */
    @ApiModelProperty("个人所得税")
    private String personalIncomeTax;

    /** 所获扶持政策明细 */
    @ApiModelProperty("所获扶持政策明细")
    private String supportPolicyDetail;

    /** 迁出原因 */
    @ApiModelProperty("迁出原因")
    private Integer moveOutReason;

    /** 其他原因 */
    @ApiModelProperty("其他原因")
    private String moveOutOtherReason;

    /** 前往地址 */
    @ApiModelProperty("前往地址")
    private String destinationAddress;

    /** 其他情况 */
    @ApiModelProperty("其他情况")
    private String otherDescription;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String COMPANY_ID = "company_id";

    public static final String REGISTERED_CAPITAL = "registered_capital";

    public static final String ESTABLISHMENT_DATE = "establishment_date";

    public static final String MOVE_IN_DATE = "move_in_date";

    public static final String REGISTERED_ADDRESS = "registered_address";

    public static final String ACTUAL_ADDRESS = "actual_address";

    public static final String TOTAL_REVENUE = "total_revenue";

    public static final String TOTAL_PROFIT = "total_profit";

    public static final String NET_PROFIT = "net_profit";

    public static final String INVENTION_PATENTS = "invention_patents";

    public static final String SOFTWARE_OR_UTILITY_PATENTS = "software_or_utility_patents";

    public static final String R_AND_D_STAFF = "r_and_d_staff";

    public static final String TOTAL_R_AND_D_EXPENSES = "total_r_and_d_expenses";

    public static final String TOTAL_TAX = "total_tax";

    public static final String CORPORATE_INCOME_TAX = "corporate_income_tax";

    public static final String VALUE_ADDED_TAX = "value_added_tax";

    public static final String BUSINESS_TAX = "business_tax";

    public static final String PERSONAL_INCOME_TAX = "personal_income_tax";

    public static final String SUPPORT_POLICY_DETAILS = "support_policy_details";

    public static final String MOVE_OUT_REASON = "move_out_reason";

    public static final String OTHER_REASON = "other_reason";

    public static final String DESTINATION_ADDRESS = "destination_address";

    public static final String OTHER_INFO = "other_info";

    public static final String CREATE_TIME = "create_time";

    public static final String CREATE_USER = "create_user";
}
