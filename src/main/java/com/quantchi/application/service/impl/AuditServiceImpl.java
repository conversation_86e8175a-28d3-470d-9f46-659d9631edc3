package com.quantchi.application.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.application.mapper.ApplicationMapper;
import com.quantchi.application.model.bo.AuditQueryBO;
import com.quantchi.application.model.bo.AuditProcessBO;
import com.quantchi.application.model.bo.InitiateTransferBO;
import com.quantchi.application.model.entity.*;
import com.quantchi.application.model.enums.ApplicationStatus;
import com.quantchi.application.model.enums.ApplyType;
import com.quantchi.application.model.enums.ProcessStatus;
import com.quantchi.application.model.vo.AuditOptionalListVO;
import com.quantchi.application.model.vo.AuditPageVO;
import com.quantchi.application.model.vo.AuditRecordVO;
import com.quantchi.application.model.vo.AuditRecordExportVO;
import com.quantchi.application.service.IApplicationFileService;
import com.quantchi.application.service.IAuditService;
import com.quantchi.application.service.IApplicationProcessService;
import com.quantchi.application.service.IApplicationService;
import com.quantchi.common.domain.CustomIndexNavSetting;
import com.quantchi.sys.model.entity.SysMenu;
import com.quantchi.sys.model.entity.SysUser;
import com.quantchi.sys.service.impl.CaptchaSendService;
import com.quantchi.sys.service.impl.SysUserService;
import com.quantchi.sys.utils.AesUtil;
import com.quantchi.sys.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.excel.EasyExcel;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.poi.ss.usermodel.*;

import static com.quantchi.application.model.entity.ApplicationFile.handleFile;
import static com.quantchi.application.model.entity.ApplicationProcess.PROCESSOR_COMMENTS;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuditServiceImpl implements IAuditService {

    private final SysUserService sysUserService;
    private final ApplicationMapper applicationMapper;
    private final IApplicationService applicationService;
    private final IApplicationProcessService applicationProcessService;
    private final IApplicationFileService applicationFileService;
    private final CaptchaSendService captchaSendService;

    @Override
    public List<CustomIndexNavSetting> optionalList() {

        List<CustomIndexNavSetting> list = new ArrayList<>();

        // 1. 获取用户菜单并填充申请类型列表
        CustomIndexNavSetting applicationType = new CustomIndexNavSetting();
        List<String> codes = new LinkedList<>();
        List<String> types = new LinkedList<>();
        List<SysMenu> userMenus = sysUserService.getUserMenus();

        // 根据menuId判断并添加对应的申请类型
        for (SysMenu menu : userMenus) {
            Long menuId = menu.getMenuId();
            
            if (menuId == 401) {
                // 入驻申请
                codes.add(ApplyType.MOVE_IN.getCode().toString());
                types.add(ApplyType.MOVE_IN.getInfo());
            } else if (menuId == 402) {
                // 装修申请
                codes.add(ApplyType.DECORATE.getCode().toString());
                types.add(ApplyType.DECORATE.getInfo());
            } else if (menuId == 403) {
                // 迁出申请
                codes.add(ApplyType.MOVE_OUT.getCode().toString());
                types.add(ApplyType.MOVE_OUT.getInfo());
            }
        }

        if (CollectionUtils.isNotEmpty(codes) && codes.size() == 1) {
            codes = new ArrayList<>();
            types = new ArrayList<>();
        }

        if (CollectionUtils.isNotEmpty(codes)) {
            // 设置申请类型列表
            applicationType.setField("applyTypes");
            applicationType.setFieldName("申请事项");
            applicationType.setRange(codes);
            applicationType.setScope(types);
            list.add(applicationType);
        }
        
        // 2. 转换ApplicationStatus枚举值为AuditStatus列表
        CustomIndexNavSetting auditStatus = new CustomIndexNavSetting();
        List<String> auditCodes = new LinkedList<>();
        List<String> auditTypes = new LinkedList<>();
        for (ApplicationStatus status : ApplicationStatus.values()) {
            auditCodes.add(status.getCode().toString());
            auditTypes.add(status.getInfo());
        }
        
        // 设置审批状态列表
        auditStatus.setField("statuses");
        auditStatus.setFieldName("审批状态");
        auditStatus.setRange(auditCodes);
        auditStatus.setScope(auditTypes);
        list.add(auditStatus);

        return list;
    }
    
    @Override
    public AuditPageVO pageAuditRecords(final AuditQueryBO queryBO) {
        // 1. 获取用户有权限的申请事项类型
        final List<SysMenu> userMenus = sysUserService.getUserMenus();
        final Set<Integer> allowedApplyTypes = new HashSet<>();
        Long approver = null;
        
        // 根据menuId判断可查询的申请类型
        for (SysMenu menu : userMenus) {
            final Long menuId = menu.getMenuId();
            
            if (menuId == 401 || menuId == 4010 || menuId == 4011) {
                allowedApplyTypes.add(ApplyType.MOVE_IN.getCode());
            } else if (menuId == 402 || menuId == 4020 || menuId == 4021) {
                allowedApplyTypes.add(ApplyType.DECORATE.getCode());
            } else if (menuId == 403 || menuId == 4030 || menuId == 4031) {
                allowedApplyTypes.add(ApplyType.MOVE_OUT.getCode());
            }

            if (menuId == 4010 || menuId == 4020 || menuId == 4030) {
                approver = StpUtil.getLoginIdAsLong();
            }
        }
        
        // 如果没有任何权限，直接返回空结果
        if (allowedApplyTypes.isEmpty()) {
            final AuditPageVO pageVO = new AuditPageVO();
            pageVO.setTotal(0L);
            pageVO.setPageSize(queryBO.getPageSize());
            pageVO.setPageNum(queryBO.getPageNum());
            pageVO.setPages(0L);
            pageVO.setRecords(new ArrayList<>());
            return pageVO;
        }
        
        // 2. 在SQL查询前过滤用户选择的申请类型，确保只查询有权限的申请类型
        List<Integer> filteredApplyTypes = null;
        if (CollectionUtils.isNotEmpty(queryBO.getApplyTypes())) {
            filteredApplyTypes = queryBO.getApplyTypes().stream()
                .filter(allowedApplyTypes::contains)
                .collect(Collectors.toList());
            
            // 如果过滤后为空（表示用户选择了没有权限的申请类型），直接返回空结果
            if (filteredApplyTypes.isEmpty()) {
                final AuditPageVO pageVO = new AuditPageVO();
                pageVO.setTotal(0L);
                pageVO.setPageSize(queryBO.getPageSize());
                pageVO.setPageNum(queryBO.getPageNum());
                pageVO.setPages(0L);
                pageVO.setRecords(new ArrayList<>());
                return pageVO;
            }
        } else {
            filteredApplyTypes = new ArrayList<>(allowedApplyTypes);
        }
        
        // 3. 执行分页查询，使用XML中的联表查询
        final Page<AuditRecordVO> page = new Page<>(queryBO.getPageNum(), queryBO.getPageSize());
        final IPage<AuditRecordVO> resultPage = applicationMapper.pageAuditRecordsWithJoin(
            page,
            filteredApplyTypes,
            queryBO.getStatuses(),
            queryBO.getKeyword(),
            approver
        );
        
        // 4. 设置审批类型和状态的中文名称
        if (CollectionUtils.isNotEmpty(resultPage.getRecords())) {
            for (AuditRecordVO record : resultPage.getRecords()) {
                // 设置申请事项中文名称
                if (record.getApplyType() != null) {
                    record.setApplyTypeName(ApplyType.getInfoByCode(record.getApplyType()));
                }
                
                // 设置状态中文名称
                if (record.getStatus() != null) {
                    record.setStatusName(ApplicationStatus.getInfoByCode(record.getStatus()));
                }
                getApplicant(record);
            }
        }
        
        // 5. 构建返回结果
        final AuditPageVO pageVO = new AuditPageVO();
        pageVO.setTotal(resultPage.getTotal());
        pageVO.setPageSize(queryBO.getPageSize());
        pageVO.setPageNum(queryBO.getPageNum());
        pageVO.setPages(resultPage.getPages());
        pageVO.setRecords(resultPage.getRecords());
        
        return pageVO;
    }

    private void getApplicant(AuditRecordVO record) {
        if (StringUtils.isBlank(record.getApplicantId())) {
            return;
        }
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(record.getApplicantId()));
        record.setApplicant(sysUser.getUserName());
        record.setContactInfo(sysUser.getPhonenumber());
    }

    /**
     * 处理审批申请
     *
     * @param auditProcessBO 审批处理参数
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processAudit(final AuditProcessBO auditProcessBO) {
        if (auditProcessBO == null || StringUtils.isBlank(auditProcessBO.getApplicationId())) {
            log.error("审批处理失败：参数为空");
            return false;
        }

        final String applicationId = auditProcessBO.getApplicationId();
        
        // 1. 查询申请信息
        final Application application = applicationService.getById(applicationId);
        if (application == null) {
            log.error("审批处理失败：未找到申请记录，applicationId={}", applicationId);
            return false;
        }
        
        // 2. 判断当前申请状态是否可审批（只有处理中的申请可以审批）
        if (!ApplicationStatus.PROCESSING.getCode().equals(application.getStatus())) {
            log.error("审批处理失败：当前状态不可审批，applicationId={}，status={}", 
                    applicationId, ApplicationStatus.getInfoByCode(application.getStatus()));
            return false;
        }

        String applicantPhone = getApplicantPhone(application);
        
        // 3. 根据审批结果更新申请状态
        if (ProcessStatus.AGREE.getCode().equals(auditProcessBO.getProcessStatus())) {
            // 同意
            application.setStatus(ApplicationStatus.PROCESS_GRANTED.getCode());
            captchaSendService.sendSuccessByAliyun(applicantPhone);
        } else {
            // 驳回
            application.setStatus(ApplicationStatus.PROCESS_REFUSE.getCode());
            captchaSendService.sendFailByAliyun(applicantPhone);
        }
        
        // 设置完成时间
        application.setFinishTime(LocalDate.now());
        
        // 4. 创建审批流程记录
        final ApplicationProcess process = new ApplicationProcess();
        process.setId(UUID.randomUUID().toString().replace("-", ""));
        process.setApplicationId(applicationId);
        process.setProcessor(StpUtil.getLoginIdAsString());
        process.setProcessStatus(auditProcessBO.getProcessStatus());
        process.setProcessAdvice(auditProcessBO.getProcessAdvice());
        process.setProcessTime(LocalDate.now());
        process.setProcess(PROCESSOR_COMMENTS);

        // 设置创建者和更新者
        final String currentUser = StpUtil.getLoginIdAsString();
        process.setCreateUser(currentUser);
        process.setUpdateUser(currentUser);
        process.setCreateTime(LocalDateTime.now());
        process.setUpdateTime(LocalDateTime.now());

        // 保存审批材料
        if (CollectionUtils.isNotEmpty(auditProcessBO.getAuditFiles())) {
            List<ApplicationFile> applicationFiles = new ArrayList<>();
            applicationFiles.addAll(auditProcessBO.getAuditFiles().stream().filter(Objects::nonNull)
                    .map(f -> handleFile(ApplicationFile.AUDIT, applicationId, process.getId(), currentUser, f))
                    .collect(Collectors.toList()));
            applicationFileService.saveBatch(applicationFiles);
        }
        
        // 5. 保存更改
        final boolean updateResult = applicationService.updateById(application);
        final boolean saveResult = applicationProcessService.save(process);
        
        return updateResult && saveResult;
    }

    private String getApplicantPhone(Application application) {
        if (StringUtils.isBlank(application.getApplicantId())) {
            return "";
        }
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(application.getApplicantId()));
        return sysUser.getPhonenumber();
    }

    @Override
    public void exportAuditRecords(AuditQueryBO queryBO, HttpServletResponse response) throws Exception {
        try {
            // 查询所有符合条件的数据（不分页）
            List<AuditRecordVO> dataList = listAuditRecordsByCondition(queryBO);

            // 转换为导出VO
            List<AuditRecordExportVO> exportList = convertToExportVO(dataList);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 生成文件名
            String fileName = "审批记录_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);

            // 先生成到临时字节数组
            ByteArrayOutputStream tempOutput = new ByteArrayOutputStream();
            EasyExcel.write(tempOutput, AuditRecordExportVO.class)
                    .sheet("审批记录")
                    .doWrite(exportList);

            // 用POI重新处理，移除灰色背景并设置列宽
            processAuditExcelStyle(tempOutput.toByteArray(), response.getOutputStream());

            log.info("审批记录Excel导出完成，共导出{}条数据", exportList.size());

        } catch (IOException e) {
            log.error("导出审批记录Excel失败", e);
            throw new Exception("导出Excel失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有符合条件的审批记录（不分页）
     */
    private List<AuditRecordVO> listAuditRecordsByCondition(AuditQueryBO queryBO) {
        // 1. 获取用户有权限的申请事项类型
        final List<SysMenu> userMenus = sysUserService.getUserMenus();
        final Set<Integer> allowedApplyTypes = new HashSet<>();
        Long approver = null;

        // 根据menuId判断可查询的申请类型
        for (SysMenu menu : userMenus) {
            final Long menuId = menu.getMenuId();

            if (menuId == 401) {
                allowedApplyTypes.add(ApplyType.MOVE_IN.getCode());
            } else if (menuId == 402) {
                allowedApplyTypes.add(ApplyType.DECORATE.getCode());
            } else if (menuId == 403) {
                allowedApplyTypes.add(ApplyType.MOVE_OUT.getCode());
            } else if (menuId == 4010 || menuId == 4020 || menuId == 4030) {
                approver = StpUtil.getLoginIdAsLong();
            }
        }

        // 如果没有任何权限，直接返回空结果
        if (allowedApplyTypes.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 在SQL查询前过滤用户选择的申请类型，确保只查询有权限的申请类型
        List<Integer> filteredApplyTypes = null;
        if (CollectionUtils.isNotEmpty(queryBO.getApplyTypes())) {
            filteredApplyTypes = queryBO.getApplyTypes().stream()
                .filter(allowedApplyTypes::contains)
                .collect(Collectors.toList());

            // 如果过滤后为空（表示用户选择了没有权限的申请类型），直接返回空结果
            if (filteredApplyTypes.isEmpty()) {
                return new ArrayList<>();
            }
        } else {
            filteredApplyTypes = new ArrayList<>(allowedApplyTypes);
        }

        // 3. 执行查询，不分页
        List<AuditRecordVO> resultList = applicationMapper.listAuditRecordsWithJoin(
            filteredApplyTypes,
            queryBO.getStatuses(),
            queryBO.getKeyword(),
            approver
        );

        // 4. 设置审批类型和状态的中文名称
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (AuditRecordVO record : resultList) {
                // 设置申请事项中文名称
                if (record.getApplyType() != null) {
                    record.setApplyTypeName(ApplyType.getInfoByCode(record.getApplyType()));
                }

                // 设置状态中文名称
                if (record.getStatus() != null) {
                    record.setStatusName(ApplicationStatus.getInfoByCode(record.getStatus()));
                }
                getApplicant(record);
            }
        }

        return resultList;
    }

    /**
     * 转换为导出VO
     */
    private List<AuditRecordExportVO> convertToExportVO(List<AuditRecordVO> dataList) {
        return dataList.stream().map(item -> {
            AuditRecordExportVO exportVO = new AuditRecordExportVO();
            exportVO.setApplicantName(item.getApplicantName());
            exportVO.setApplyTypeName(item.getApplyTypeName());
            exportVO.setApplicant(item.getApplicant());
            exportVO.setContactInfo(item.getContactInfo());

            // 格式化发起时间
            if (item.getApplyTime() != null) {
                exportVO.setApplyTime(item.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }

            exportVO.setStatusName(item.getStatusName());

            return exportVO;
        }).collect(Collectors.toList());
    }

    /**
     * 处理审批记录Excel样式，移除灰色背景并设置列宽
     */
    private void processAuditExcelStyle(byte[] excelData, java.io.OutputStream outputStream) throws IOException {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(excelData);
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);

            // 设置列宽
            setAuditColumnWidths(sheet);

            // 创建透明样式
            CellStyle transparentStyle = createAuditTransparentStyle(workbook);

            // 处理表头行（第一行）
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                for (int colIndex = 0; colIndex < headerRow.getLastCellNum(); colIndex++) {
                    Cell cell = headerRow.getCell(colIndex);
                    if (cell != null) {
                        // 保留原有的值，只改变样式
                        String cellValue = getCellValueAsString(cell);
                        cell.setCellStyle(transparentStyle);
                        cell.setCellValue(cellValue);
                    }
                }
            }

            // 写入到输出流
            workbook.write(outputStream);
        }
    }

    /**
     * 设置审批记录Excel列宽
     */
    private void setAuditColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 30 * 256); // 申请单位
        sheet.setColumnWidth(1, 15 * 256); // 申请事项
        sheet.setColumnWidth(2, 15 * 256); // 发起人
        sheet.setColumnWidth(3, 20 * 256); // 联系方式
        sheet.setColumnWidth(4, 15 * 256); // 发起时间
        sheet.setColumnWidth(5, 15 * 256); // 当前进度
    }

    /**
     * 创建审批记录Excel透明样式
     */
    private CellStyle createAuditTransparentStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillPattern(FillPatternType.NO_FILL);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(false);
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 简化处理，直接转换为字符串
                double numericValue = cell.getNumericCellValue();
                if (numericValue == (long) numericValue) {
                    return String.valueOf((long) numericValue);
                } else {
                    return String.valueOf(numericValue);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initiateTransfer(InitiateTransferBO initiateTransferBO) {
        if (initiateTransferBO == null || StringUtils.isBlank(initiateTransferBO.getApplicationId())) {
            log.error("发起流转失败：参数为空");
            return false;
        }

        final String applicationId = initiateTransferBO.getApplicationId();

        // 1. 查询申请信息
        final Application application = applicationService.getById(applicationId);
        if (application == null) {
            log.error("发起流转失败：未找到申请记录，applicationId={}", applicationId);
            return false;
        }

        // 2. 更新申请的审批人
        application.setApprover(initiateTransferBO.getApprover());

        // 3. 创建审批流程记录
        final ApplicationProcess process = new ApplicationProcess();
        process.setId(UUID.randomUUID().toString().replace("-", ""));
        process.setApplicationId(applicationId);
        process.setProcessor(LoginHelper.getUsername());
        process.setTransferTo(sysUserService.selectUserById(initiateTransferBO.getApprover()).getUserName());
        process.setProcessStatus(null); // 流转不设置处理状态
        process.setProcessAdvice(initiateTransferBO.getDescription());
        process.setProcessTime(LocalDate.now());
        process.setProcess(ApplicationProcess.PROCESSOR_CHANGE);

        // 设置创建者和更新者
        final String currentUser = StpUtil.getLoginIdAsString();
        process.setCreateUser(currentUser);
        process.setUpdateUser(currentUser);
        process.setCreateTime(LocalDateTime.now());
        process.setUpdateTime(LocalDateTime.now());

        // 4. 保存补充材料
        if (CollectionUtils.isNotEmpty(initiateTransferBO.getSupplementaryFiles())) {
            List<ApplicationFile> applicationFiles = new ArrayList<>();
            applicationFiles.addAll(initiateTransferBO.getSupplementaryFiles().stream().filter(Objects::nonNull)
                    .map(f -> handleFile(ApplicationFile.AUDIT, applicationId, process.getId(), currentUser, f))
                    .collect(Collectors.toList()));
            applicationFileService.saveBatch(applicationFiles);
        }

        // 5. 保存更改
        final boolean updateResult = applicationService.updateById(application);
        final boolean saveResult = applicationProcessService.save(process);

        if (updateResult && saveResult) {
            log.info("发起流转成功，applicationId={}，新审批人={}", applicationId, initiateTransferBO.getApprover());
        } else {
            log.error("发起流转失败，applicationId={}，updateResult={}，saveResult={}",
                    applicationId, updateResult, saveResult);
        }

        return updateResult && saveResult;
    }
}
