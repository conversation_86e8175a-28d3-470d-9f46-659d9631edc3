package com.quantchi.contract.model.vo;

import com.quantchi.contract.model.entity.*;
import com.quantchi.sys.model.entity.SysOrg;
import com.quantchi.zone.model.entity.ZoneFunction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class OptionalListVO {

    @ApiModelProperty("租赁物用途")
    private List<LeaseFunction> leaseFunctions;

    @ApiModelProperty("房屋用途")
    private List<ZoneFunction> zoneFunctions;

    @ApiModelProperty("装修改造期限制")
    private List<DecorateTimeLimit> decorateTimeLimitList;

    @ApiModelProperty("水电费押金标准")
    private List<UtilitiesDepositStandard> utilitiesDepositStandardList;

    @ApiModelProperty("房屋设计标准用电量")
    private List<PowerConsumptionStandard> powerConsumptionStandardList;

    @ApiModelProperty("首期租金支付时间")
    private List<FirstRentPayTime> firstRentPayTimeList;

}
