package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 申请人股权结构表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_equity_structure")
@ApiModel(value = "ApplicationEquityStructure对象", description = "申请人股权结构表")
public class ApplicationEquityStructure implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private String id;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 股东名称 */
    @ApiModelProperty("股东名称")
    private String shareholderName;

    /** 股权占比（%） */
    @ApiModelProperty("股权占比（%）")
    private String equityRatio;

    /** 出资金额 */
    @ApiModelProperty("出资金额")
    private String capitalContribution;

    /** 出资形式 */
    @ApiModelProperty("出资形式")
    private String contributionType;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String SHAREHOLDER_NAME = "shareholder_name";

    public static final String EQUITY_RATIO = "equity_ratio";

    public static final String CAPITAL_CONTRIBUTION = "capital_contribution";

    public static final String CONTRIBUTION_TYPE = "contribution_type";

    public static final String CREATE_TIME = "create_time";

    public static final String CREATE_USER = "create_user";
}
