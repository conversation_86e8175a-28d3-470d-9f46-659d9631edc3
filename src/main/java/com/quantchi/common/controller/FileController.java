package com.quantchi.common.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.model.FileInfo;
import com.quantchi.common.service.impl.FileInfoServiceImpl;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.sys.config.annotation.Log;
import com.quantchi.sys.model.enums.BusinessType;
import com.quantchi.sys.utils.LoginHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/1 11:06
 */
@RestController
@RequestMapping("/file")
@Api(tags = "文件管理")
@Validated
public class FileController {

    /**
     * 文件大小限制
     */
    private static final Long FILE_SIZE_LIMIT = Long.valueOf(20 * 1048576);
    /**
     * 文件格式类型限制
     */
    private static final List<String> FILE_TYPE_LIMIT = Arrays.asList(".png", ".jpg", ".jpeg", ".bmp", ".gif", ".webp",
            ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf");

    @Autowired
    private FileInfoServiceImpl fileService;

    @ApiOperation(value = "文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件内容", required = true, dataType = "MultipartFile")
    })
    @PostMapping("/upload")
    @Log(title = "文件管理-上传", businessType = BusinessType.IMPORT, isSaveRequestData = false)
    public ResultInfo<FileInfo> upload(@RequestBody MultipartFile file) throws Exception {

        return ResultConvert.success(fileService.upload(file, LoginHelper.getUsername()));

    }

    @ApiOperation(value = "文件下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    @GetMapping("/download")
    @Log(title = "文件管理-下载", businessType = BusinessType.EXPORT)
    @SaIgnore
    public void download(@RequestParam @NotBlank String fileId, boolean inline, HttpServletResponse response) {
        fileService.download(fileId, inline, response);
    }

    @ApiOperation(value = "文件删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    @GetMapping("/delete")
    @Log(title = "文件管理-删除", businessType = BusinessType.DELETE)
    public ResultInfo delete(@RequestParam @NotBlank String fileId) {
        return ResultConvert.success(fileService.remove(Wrappers.lambdaQuery(FileInfo.class).eq(FileInfo::getFileId, fileId)));
    }

    @ApiOperation(value = "文件预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "token", value = "验证参数", required = true, dataType = "String")
    })
    @GetMapping("/preview")
    @Log(title = "文件管理-预览", businessType = BusinessType.EXPORT)
    public void preview(@RequestParam @NotBlank String fileId, HttpServletResponse response) {

        fileService.download(fileId, true, response);

    }
}
