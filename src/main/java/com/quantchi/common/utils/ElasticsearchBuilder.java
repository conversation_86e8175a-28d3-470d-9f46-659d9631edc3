package com.quantchi.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.mapping.KeywordProperty;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TextProperty;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.TotalHits;
import co.elastic.clients.json.JsonData;
import com.quantchi.common.constant.SearchTypeConstants;
import com.quantchi.common.enums.WildCardQueryStrategyEnum;
import com.quantchi.common.helper.ElasticsearchHelper;
import com.quantchi.common.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static com.quantchi.common.model.FieldSearchSetting.MATCH_EXACT;
import static com.quantchi.common.model.FieldSearchSetting.MATCH_FUZZY;

/**
 * <AUTHOR>
 * @date 2022/10/24 14:43
 * es查询参数构建
 */
@Slf4j
public class ElasticsearchBuilder {

    /**
     * bucket聚合统计字段名默认前缀
     * why:不管是bucket统计还是metric统计,字段名都不会改变,避免混淆,使用前缀区分开来
     */
    public static final String TERMS_BUCKET_PREFIX = "bucket.";

    public static final String SUM_BUCKET_PREFIX = "sum.";

    private static final String EMPTY_STRING = "";

    public static final String LABEL_BEFORE = "<font color=\"#fc4b4b\">";

    public static final String LABEL_REAR = "</font>";

    public static final String ES_SCORE = "esScore";


    /**
     * 从原始的SearchResponse结果中构建需要分页结果
     */
    public static EsPageResult buildPageResult(final SearchResponse searchResponse) {
        final EsPageResult pageResult = new EsPageResult();
        try {
            final TotalHits totalHits = searchResponse.hits().total();
            final List<Hit> hits = searchResponse.hits().hits();
            final List<Map<String, Object>> list = new ArrayList<>(hits.size());
            for (final Hit<Map<String, Object>> hit : hits) {
                final Map<String, Object> sourceAsMap = hit.source();
                // 获取原本需要高亮的字段列表
                list.add(sourceAsMap);
            }
            pageResult.setList(list);
            pageResult.setTotal(totalHits.value());
            return pageResult;
        } catch (final Exception e) {
            log.error("构建分页查询结果失败", e);
            return pageResult;
        }
    }


    /**
     * 构建SearchSource查询
     */
    public static SearchRequest.Builder buildSearchSource(final SearchSourceQuery query) {
        final BoolQuery.Builder queryBuilder = query.getQueryBuilder();
        final String sort = query.getSort();
        final String[] includes = query.getIncludes();
        final String[] excludes = query.getExcludes();
        
        // 如果没有高亮设置，则创建一个默认的高亮构建器
        if (query.getHighlightBuilder() == null) {
            final Highlight.Builder highlightBuilder = new Highlight.Builder();
            //设置高亮字段
            highlightBuilder.fields("name", f -> f
                    .fragmentSize(150)
                    .numberOfFragments(5)
                    .highlightFilter(true)
                    .preTags("<font color=\"red\">")
                    .postTags("</font>")
            );
            query.setHighlightBuilder(highlightBuilder);
        }
        
        Integer pageNum = query.getPageNum();
        Integer pageSize = query.getPageSize();
        if (pageNum == null) {
            pageNum = 0;
        }
        if (pageSize == null) {
            pageSize = 10000;
        }


        final SearchRequest.Builder searchBuilder = new SearchRequest.Builder();
        
        // 设置查询和高亮
        searchBuilder.query(q -> q.bool(queryBuilder.build()));
        searchBuilder.highlight(query.getHighlightBuilder().build()); // ES8 需要直接传入 Highlight 对象

        // 设置包含和排除字段
        if (!ArrayUtil.isEmpty(includes) || !ArrayUtil.isEmpty(excludes)) {
            searchBuilder.source(s -> {
                s.filter(f -> {
                    if (!ArrayUtil.isEmpty(includes)) {
                        f.includes(Arrays.asList(includes));
                    }
                    if (!ArrayUtil.isEmpty(excludes)) {
                        f.excludes(Arrays.asList(excludes));
                    }
                    return f;
                });
                return s;
            });
        }
        
        // 设置分页
        ElasticsearchHelper.pagingSet(searchBuilder, pageNum, pageSize);
        
        // 设置排序
        if (StrUtil.isNotEmpty(sort)) {
            ElasticsearchHelper.addMultiSort(searchBuilder, sort);
        }
        
        return searchBuilder;
    }

    /**
     * 从原始的SearchResponse结果中构建需要分页结果，并高亮结果
     */
    public static EsPageResult buildPageResultWithHighlight(final SearchResponse searchResponse,
                                                            final List<KeywordSearchProperty> keywordSearchList,
                                                            final String keyword) {
        final EsPageResult pageResult = new EsPageResult();
        if (searchResponse == null) {
            return pageResult;
        }
        try {
            final TotalHits totalHits = searchResponse.hits().total();
            final List<Hit> hits = searchResponse.hits().hits();
            final List<Map<String, Object>> list = new ArrayList<>(hits.size());
            final List<String> alreadyHighlightFields = new ArrayList<>();
            for (final Hit<Map<String, Object>> hit : hits) {
                final Map<String, Object> sourceAsMap = hit.source();
                // 获取原本需要高亮的字段列表，手动进行高亮
                if (StrUtil.isNotBlank(keyword)) {
                    final Set<String> allHighlightFields = keywordSearchList.stream()
                            .filter(KeywordSearchProperty::getIsHighlight)
                            .map(KeywordSearchProperty::getField)
                            .collect(Collectors.toSet());
                    allHighlightFields.forEach(field -> {
                        if (alreadyHighlightFields.contains(field)) {
                            return;
                        }
                        final Object fieldObject = sourceAsMap.get(field);
                        if (fieldObject instanceof String) {
                            sourceAsMap.put(field,
                                    replacementInfo((String) fieldObject, keyword, LABEL_BEFORE, LABEL_REAR));
                        } else if (fieldObject instanceof List) {
                            final List<String> fieldValueList = new ArrayList<>();
                            ((List) fieldObject).forEach(item -> {
                                if (item instanceof String) {
                                    fieldValueList.add(replacementInfo((String) item, keyword, LABEL_BEFORE, LABEL_REAR));
                                }
                            });
                            if (CollUtil.isNotEmpty(fieldValueList)) {
                                sourceAsMap.put(field, fieldValueList);
                            }
                        }
                    });
                }
                list.add(sourceAsMap);
            }
            pageResult.setList(list);
            pageResult.setTotal(totalHits.value());
            return pageResult;
        } catch (final Exception e) {
            log.error("构建分页查询结果失败", e);
            return pageResult;
        }
    }

    /**
     * 根据参数的值构建BoolQueryBuilder
     */
    public static void buildBoolQuery(final BasicBoolQuery queryBO) {
        final String index = queryBO.getIndex();
        final String field = queryBO.getField();
        final String fieldKeyword = queryBO.getFieldKeyword();
        final String keyword = queryBO.getKeyword();
        final List<String> values = queryBO.getValues();
        final List<String> matchTypes = queryBO.getMatchTypes();
        final BoolQuery.Builder boolQueryBuilder = queryBO.getBoolQueryBuilder();
        Integer searchType = queryBO.getSearchType();
        if (searchType == null) {
            searchType = SearchTypeConstants.USUAL_QUERY;
        }
        final Operator operator = queryBO.getOperator();
        final Boolean isFilter = queryBO.getIsFilter();
        final List<KeywordSearchProperty> keywordFields = queryBO.getKeywordFields();
        final BoolQuery.Builder temp = new BoolQuery.Builder();
        
        // 关键词查询
        if (StrUtil.isNotBlank(keyword)) {
            final DisMaxQuery.Builder disMaxQueryBuilder = new DisMaxQuery.Builder();
            for (final KeywordSearchProperty keywordField : keywordFields) {
                final float boost = keywordField.getBoost() != null ? keywordField.getBoost() : 1.0f;
                final String keywordFieldStr = keywordField.getField();
                final ActualFieldAndType keywordFieldTypeByMapping = getFieldTypeByMapping(index, keywordFieldStr);
                final String keywordType = keywordFieldTypeByMapping.getType();
                if (Objects.equals(keywordType, "nested")) {
                    final String outerField = keywordFieldTypeByMapping.getOuterField();
                    final String innerType = keywordFieldTypeByMapping.getInnerType();
                    final Query query;
                    if (Objects.equals(innerType, "text")) {
                        query = Query.of(q -> 
                            q.match(m -> m.field(keywordFieldStr).query(keyword)));
                    } else {
                        query = Query.of(q -> 
                            q.wildcard(w -> w.field(keywordFieldStr).value(WildCardQueryStrategyEnum.getStrategyKeyword(keyword, 3)).boost(boost)));
                    }
                    final Query nestedQuery = Query.of(q ->
                        q.nested(n -> n.path(outerField).query(query).scoreMode(ChildScoreMode.Avg).boost(boost)));
                    disMaxQueryBuilder.queries(nestedQuery);
                } else {
                    final Query matchQuery = Query.of(q ->
                        q.match(m -> m.field(keywordFieldStr).query(keyword).boost(boost)));
                    disMaxQueryBuilder.queries(matchQuery);
                }
            }
            final Query disMaxQuery = Query.of(q -> q.disMax(disMaxQueryBuilder.build()));
            if (isFilter) {
                boolQueryBuilder.filter(disMaxQuery);
            } else {
                boolQueryBuilder.must(disMaxQuery);
            }
        }
        
        if (field == null || CollUtil.isEmpty(values)) {
            return;
        }
        
        for (final String v : values) {
            switch (searchType) {
                case SearchTypeConstants.DATE_MONTH:
                    String from = v.split(",")[0];
                    String to = v.split(",")[1];
                    // 带-的表示传入的是具体日期
                    if (from.contains("-")) {
                        temp.should(q -> q.range(r -> r
                                .field(field)
                                .gte(JsonData.of(from))
                                .lt(JsonData.of(to))
                                .format(NORM_DATE_PATTERN)));
                    } else {
                        final Date fromDate;
                        final Date toDate;
                        // 判断是否是特殊的日期查询，默认是月份单位
                        final Date date = new Date();
                        if (!Objects.equals(from, "Unlimited")) {
                            if (from.contains("d")) {
                                toDate = DateUtil.offsetDay(date, -Integer.parseInt(from.split("d")[0]));
                            } else {
                                toDate = DateUtil.offsetMonth(date, -Integer.parseInt(from));
                            }
                        } else {
                            toDate = DateUtil.offsetMonth(date, 10000);
                        }
                        if (to.contains("d")) {
                            fromDate = DateUtil.offsetDay(date, -Integer.parseInt(to.split("d")[0]));
                        } else {
                            fromDate = DateUtil.offsetMonth(date, -Integer.parseInt(to));
                        }
                        temp.should(q -> q.range(r -> r
                                .field(field)
                                .gte(JsonData.of(DateUtil.format(fromDate, NORM_DATE_PATTERN)))
                                .lt(JsonData.of(DateUtil.format(toDate, NORM_DATE_PATTERN)))
                                .format(NORM_DATE_PATTERN)));
                    }
                    break;
                case SearchTypeConstants.AMOUNT_YUAN:
                    final String first = v.split(",")[0];
                    final String second = v.split(",")[1];
                    RangeQuery.Builder builder = new RangeQuery.Builder().field(field);
                    if (!Objects.equals(first, "min")) {
                        builder.gte(JsonData.of(Double.parseDouble(first) * 10000));
                    }
                    if (!Objects.equals(second, "max")) {
                        builder.lt(JsonData.of(Double.parseDouble(second) * 10000));
                    }
                    temp.should(builder.build()._toQuery());
                    break;
                case SearchTypeConstants.USUAL_QUERY:
                    if (Objects.equals(field, "patent_type") && Objects.equals(v, "其它")) {
                        final BoolQuery.Builder othersQuery = new BoolQuery.Builder();
                        List<FieldValue> fieldValues = Stream.of("发明申请", "实用新型", "外观设计", "发明公布", "发明授权", "实用新型更正", "发明公布更正").map(FieldValue::of).collect(Collectors.toList());
                        othersQuery.mustNot(q -> q.terms(t -> t.field("patent_type").terms(ts -> ts.value(fieldValues))));
                        temp.should(othersQuery.build()._toQuery());
                        continue;
                    }
                    if (Objects.equals(v, "其他") || Objects.equals(v, "其它")) {
                        // todo 如果是想要选择其他，需要获取这个filed所有可能的值，然后取与展示field的交集，但是目前的方法是取这个不存在此字段的记录，因为目前的情况是这个field所有的值已经被选项罗列，只需要查询不存在此字段的记录
                        //            valueList = (List<String>) CollUtil.subtract(valueList, Collections.singletonList("其他"));
                        final BoolQuery.Builder othersQuery = new BoolQuery.Builder();
                        othersQuery.mustNot(q -> q.exists(e -> e.field(field)));
                        temp.should(othersQuery.build()._toQuery());
                        continue;
                    }
                    // 判断字段类型，如果是nested需要使用nestedQuery
                    final ActualFieldAndType fieldTypeByMapping = getFieldTypeByMapping(index, field);
                    final String type = fieldTypeByMapping.getType();
                    if (Objects.equals(type, "nested")) {
                        temp.should(q -> q.nested(n -> n
                                .path(fieldTypeByMapping.getOuterField())
                                .query(nestedInner -> nestedInner.term(t -> t.field(field).value(v)))
                                .scoreMode(ChildScoreMode.None)
                        ));
                    } else {
                        if (CollUtil.isEmpty(matchTypes)) {
                            if (Objects.equals("text", type) && !field.contains("keyword")) {
                                // 模糊搜索
                                temp.should(q -> q.matchPhrase(MatchPhraseQuery.of(m -> m.field(field).query(v))));
                            } else {
                                // 精确匹配
                                temp.should(q -> q.term(TermQuery.of(t -> t.field(field).value(v))));
                            }
                        } else {
                            if (matchTypes.contains(MATCH_EXACT)) {
                                if (StrUtil.isNotBlank(fieldKeyword)) {
                                    temp.should(q -> q.term(TermQuery.of(t -> t.field(fieldKeyword).value(v))));
                                } else {
                                    temp.should(q -> q.term(TermQuery.of(t -> t.field(field).value(v))));
                                }
                            }
                            if (matchTypes.contains(MATCH_FUZZY)) {
                                temp.should(q -> q.matchPhrase(MatchPhraseQuery.of(m -> m.field(field).query(v))));
                            }
                        }
                    }
            }
        }
        
        temp.minimumShouldMatch("1");
        if (operator.equals(Operator.And)) {
            if (isFilter) {
                boolQueryBuilder.filter(temp.build()._toQuery());
            } else {
                boolQueryBuilder.must(temp.build()._toQuery());
            }
        } else if (operator.equals(Operator.Or)) {
            boolQueryBuilder.should(temp.build()._toQuery());
        }
    }


    /**
     * 设置分页
     *
     * @param searchBuilder
     * @param from          起始页数
     * @param size          每页显示数量
     */
    /**
     * 设置分页 - ES8版本
     *
     * @param searchBuilder SearchRequest.Builder
     * @param from          起始页数
     * @param size          每页显示数量
     */
    public static void pagingSet(final SearchRequest.Builder searchBuilder, Integer from, Integer size) {
        from = from == null || from <= 0 ? 0 : from - 1;
        size = size == null || size < 0 ? 5 : size;
        //分页数量不能超过每页20,防止恶意爬取数据
        //size = size > 20 ? 20 : size;
        searchBuilder.from(from * size);
        searchBuilder.size(size);
    }

    /**
     * 构建同一个条件中不同field的查询参数
     */
    public static void buildMultiBoolQuery(final BoolQuery.Builder filterBuilder,
                                           final Map<String, List<String>> fieldValueMap) {
        final BoolQuery.Builder temp = new BoolQuery.Builder();
        for (final Map.Entry<String, List<String>> entry : fieldValueMap.entrySet()) {
            final String field = entry.getKey();
            final List<String> values = entry.getValue();
            if (field == null || CollUtil.isEmpty(values)) {
                continue;
            }
            for (final String v : values) {
                temp.should(q -> q.term(t -> t.field(field).value(v)));
            }
        }
        temp.minimumShouldMatch("1");
        filterBuilder.must(temp.build()._toQuery());
    }

    /**
     * 设置需要高亮的字段
     */
    public static void setHighlightBuilder(final Highlight.Builder highlightBuilder,
                                           final Set<String> fields) {
        //设置高亮字段
        for (final String field : fields) {
            highlightBuilder.fields(field, f -> f
                .fragmentSize(150)  // 设置片段大小
                .numberOfFragments(5)  // 设置片段数量
                .highlightFilter(true)
            );
        }
        
        //设置高亮标签
        highlightBuilder.preTags(Collections.singletonList(LABEL_BEFORE));
        highlightBuilder.postTags(Collections.singletonList(LABEL_REAR));
        highlightBuilder.requireFieldMatch(false);
    }

    /**
     * 在某字符前后添加字段
     *
     * @param stringBuilder：原字符串
     * @param keyword：字符
     * @param before：在字符前需要插入的字段
     * @param rear：在字符后需要插入的字段
     * @return
     */
    public static String replacementInfo(final StringBuilder stringBuilder, final String keyword, final String before, final String rear) {
        if (CharSequenceUtil.isBlank(stringBuilder)) {
            return stringBuilder.toString();
        }
        //字符第一次出现的位置
        int index = stringBuilder.indexOf(keyword);
        while (index != -1) {
            stringBuilder.insert(index, before);
            stringBuilder.insert(index + before.length() + keyword.length(), rear);
            //下一次出现的位置，
            index = stringBuilder.indexOf(keyword, index + before.length() + keyword.length() + rear.length() - 1);
        }
        return stringBuilder.toString();
    }

    public static String replacementInfoForKeywordList(final StringBuilder stringBuilder, final List<String> keywordList, final String before, final String rear) {
        String string = stringBuilder.toString();
        if (CharSequenceUtil.isBlank(stringBuilder) || CollUtil.isEmpty(keywordList)) {
            return string;
        }
        final Set<String> highlightKeywordList = new HashSet<>();
        keywordList.forEach(keyword -> {
            // 遍历keyword
            if (CharSequenceUtil.isNotBlank(keyword)) {
                for (int i = 0; i < keyword.length(); i++) {
                    final char c = keyword.charAt(i);
                    if (!CharUtil.isBlankChar(c)) {
                        // 如果是大小写字母，额外加上
                        if (Character.isUpperCase(c)) {
                            highlightKeywordList.add(String.valueOf(Character.toLowerCase(c)));
                        }
                        if (Character.isLowerCase(c)) {
                            highlightKeywordList.add(String.valueOf(Character.toUpperCase(c)));
                        }
                        highlightKeywordList.add(String.valueOf(c));
                    }
                }
            }

        });
        highlightKeywordList.forEach(
                keyword -> replacementInfo(stringBuilder, keyword, "⌬", "⦰")
        );
        String resultString = stringBuilder.toString();
        resultString = resultString.replace("⌬", before);
        resultString = resultString.replace("⦰", rear);
        return resultString;
    }

    public static String replacementInfoForKeywordList(final StringBuilder stringBuilder, final Set<String> highlightKeywordList, final String before, final String rear) {
        String string = stringBuilder.toString();
        if (CharSequenceUtil.isBlank(stringBuilder) || CollUtil.isEmpty(highlightKeywordList)) {
            return string;
        }
        highlightKeywordList.forEach(
                keyword -> replacementInfo(stringBuilder, keyword, "⌬", "⦰")
        );
        String resultString = stringBuilder.toString();
        resultString = resultString.replace("⌬", before);
        resultString = resultString.replace("⦰", rear);
        return resultString;
    }


    public static void main(String[] args) {
        System.out.println(replacementInfoForKeywordList(new StringBuilder("1月销量点评：主动去库存，1月销量承压"),
                Arrays.asList("$1"), LABEL_BEFORE, LABEL_REAR));
    }

    /**
     * 获取字段在索引中的类型
     *
     * @param index
     * @param field
     * @return
     */
    public static ActualFieldAndType getFieldTypeByMapping(final String index, final String field) {
        final ActualFieldAndType actualFieldAndType = new ActualFieldAndType();
        actualFieldAndType.setField(field);
        final Map<String, TypeMapping>  mappings = ElasticsearchHelper.getMappings();
        final TypeMapping typeMapping = mappings.get(index);

        if (typeMapping == null || typeMapping.properties() == null) {
            actualFieldAndType.setType(EMPTY_STRING);
            return actualFieldAndType;
        }

        Map<String, Property> indexMapping = typeMapping.properties();
        // 直接使用 properties，key 是字段名，value 是 Property
        final String outerField;
        String innerField = null;
        if (field.contains(".")) {
            final String[] split = field.split("\\.");
            outerField = split[0];
            innerField = split[1];
        } else {
            outerField = field;
        }
        actualFieldAndType.setOuterField(outerField);
        actualFieldAndType.setInnerField(innerField);

        Property outerProperty = indexMapping.get(outerField);
        if (outerProperty == null) {
            log.error("此字段" + outerField + "在这个索引" + index + "中不存在！");
            actualFieldAndType.setType(EMPTY_STRING);
            return actualFieldAndType;
        }

        String type = outerProperty._kind().jsonValue(); // PropertyKind.text/keyword/nested等

        actualFieldAndType.setType(type);

        if (innerField == null) {
            return actualFieldAndType;
        }
        // 多字段情况
        if (outerProperty._kind() == Property.Kind.Text) {
            TextProperty textProp = outerProperty.text();
            if (textProp.fields() != null && textProp.fields().containsKey(innerField)) {
                Property inner = textProp.fields().get(innerField);
                actualFieldAndType.setType(inner._kind().jsonValue());
            }
        } else if (outerProperty._kind() == Property.Kind.Keyword) {
            KeywordProperty keywordProp = outerProperty.keyword();
            if (keywordProp.fields() != null && keywordProp.fields().containsKey(innerField)) {
                Property inner = keywordProp.fields().get(innerField);
                actualFieldAndType.setType(inner._kind().jsonValue());
            }
        } else if (outerProperty._kind() == Property.Kind.Object || outerProperty._kind() == Property.Kind.Nested) {
            Map<String, Property> subProps = outerProperty._kind() == Property.Kind.Object
                    ? outerProperty.object().properties()
                    : outerProperty.nested().properties();
            if (subProps != null && subProps.containsKey(innerField)) {
                Property inner = subProps.get(innerField);
                actualFieldAndType.setType(inner._kind().jsonValue());
                if (outerProperty._kind() == Property.Kind.Nested) {
                    actualFieldAndType.setInnerType(inner._kind().jsonValue());
                }
            }
        }

        return actualFieldAndType;
    }

    /**
     * 在某字符前后添加字段
     *
     * @param string：原字符串
     * @param keyword：字符
     * @param before：在字符前需要插入的字段
     * @param rear：在字符后需要插入的字段
     * @return
     */
    public static String replacementInfo(final String string, final String keyword, final String before, final String rear) {
        if (StrUtil.isBlank(string) || StrUtil.isBlank(keyword)) {
            return string;
        }
        final StringBuilder stringBuilder = new StringBuilder(string);
        //字符第一次出现的位置
        int index = stringBuilder.indexOf(keyword);
        while (index != -1) {
            stringBuilder.insert(index, before);
            stringBuilder.insert(index + before.length() + keyword.length(), rear);
            //下一次出现的位置，
            index = stringBuilder.indexOf(keyword, index + before.length() + keyword.length() + rear.length() - 1);
        }
        return stringBuilder.toString();
    }
}
