package com.quantchi.company.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@ApiModel(value = "分支机构")
public class CompanyBranchVO {

    @ApiModelProperty("分支机构企业ID")
    private String branchId;

    @ApiModelProperty("分支企业名称")
    private String branchName;

    @ApiModelProperty("负责人")
    private String director;

    @ApiModelProperty("成立时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate establishDate;

    @ApiModelProperty("所属地区")
    private String belongArea;

    @ApiModelProperty("企业状态")
    private String status;

}
