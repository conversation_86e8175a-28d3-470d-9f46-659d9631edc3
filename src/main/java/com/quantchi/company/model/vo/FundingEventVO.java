package com.quantchi.company.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class FundingEventVO {

    @ApiModelProperty("资金青睐度")
    private String fundingFavorability;

    @ApiModelProperty("融资总次数")
    private int totalFundingRounds;

    @ApiModelProperty("融资总金额")
    private NumberUnitVO totalFundingAmount;

    @ApiModelProperty("最新融资轮次")
    private String latestFundingRound;

    @ApiModelProperty("最新估值")
    private String latestValuation;

    @ApiModelProperty("最新融资日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date latestFundingDate;

    @ApiModelProperty("融资历史")
    private List<FundingHistory> fundingHistory;

    @Data
    public static class FundingHistory {

        @ApiModelProperty("轮次")
        private String round;

        @ApiModelProperty("融资额")
        private String amount;

        @ApiModelProperty("投资机构")
        private List<String> investors;

        @ApiModelProperty("时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date date;
    }

}

