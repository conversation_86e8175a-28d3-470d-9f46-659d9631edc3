package com.quantchi.contract.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Data
@ApiModel("租金明细列表展示")
public class LeaseRentListVO extends LeaseContractBaseVO {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("承租方")
    private String lessee;

    @ApiModelProperty("租赁时间")
    private String leasePeriod;

    @ApiModelProperty("租金标准")
    private String rentStandard;

    @ApiModelProperty("租金总额")
    private BigDecimal rentTotal;

    @ApiModelProperty("缴纳状态 0未缴纳 1已缴纳 2部分缴纳")
    private Integer payStatus;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("季度")
    private Integer quarter;

    @ApiModelProperty("折扣系数")
    private BigDecimal discount;

    @ApiModelProperty("减免金额")
    private BigDecimal reduction;

    // 用于排序的合同创建时间，不输出到接口
    @JsonIgnore
    private java.util.Date contractCreateTime;

    // 用于内部聚合和状态判断，不输出到接口
    @JsonIgnore
    private BigDecimal receivableAmount;

    @JsonIgnore
    private BigDecimal receivedAmount;

    @JsonIgnore
    private BigDecimal unreceivedAmount;
}
