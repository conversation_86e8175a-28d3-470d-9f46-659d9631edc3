package com.quantchi.application.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EquityStructureBO {

    /** 股东名称 */
    @ApiModelProperty("股东名称")
    private String shareholderName;

    /** 股权占比（%） */
    @ApiModelProperty("股权占比（%）")
    private String equityRatio;

    /** 出资金额 */
    @ApiModelProperty("出资金额")
    private String capitalContribution;

    /** 出资形式 */
    @ApiModelProperty("出资形式")
    private String contributionType;
}