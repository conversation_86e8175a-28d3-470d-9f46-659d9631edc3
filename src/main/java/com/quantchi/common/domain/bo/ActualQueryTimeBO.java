package com.quantchi.common.domain.bo;

/**
 * <AUTHOR>
 * @date 2023/2/10 09:53
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("实际查询的时间")
@Data
public class ActualQueryTimeBO {

    @ApiModelProperty("起始时间，格式为yyyy-MM-dd HH:MM:ss")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("传入时间的原日期格式")
    private String originDatePattern;

    @ApiModelProperty("两个时间中存在的日期列表")
    private List<String> intervalDateList;
}
