package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "Company对象", description = "")
@TableName("company")
public class Company implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("企业ID")
    private String id;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("企业英文名称")
    private String nameEn;

    @ApiModelProperty("企业历史名称，多个用;隔开")
    private String usedName;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("法人")
    private String legalPerson;

    @ApiModelProperty("logo处理后OSS链接地址")
    private String logo;

    @ApiModelProperty("原始logo链接")
    private String logoSource;

    @ApiModelProperty("员工数量")
    private String employeeSize;

    @ApiModelProperty("参保人数")
    private Integer insuredNumber;

    @ApiModelProperty("联系电话")
    private String tel;

    @ApiModelProperty("电子邮件")
    private String email;

    @ApiModelProperty("网址")
    private String website;

    @ApiModelProperty("企业地址")
    private String address;

    @ApiModelProperty("企业建立时间")
    private LocalDate establishDate;

    @ApiModelProperty("所属国家")
    private String nation;

    @ApiModelProperty("所属省份")
    private String province;

    @ApiModelProperty("所属城市")
    private String city;

    @ApiModelProperty("所属区域")
    private String area;

    @ApiModelProperty("所属乡镇")
    private String town;

    @ApiModelProperty("国家代码")
    private String nationCode;

    @ApiModelProperty("省份代码")
    private String provinceCode;

    @ApiModelProperty("城市代码")
    private String cityCode;

    @ApiModelProperty("区域代码")
    private String areaCode;

    @ApiModelProperty("经度")
    private String lng;

    @ApiModelProperty("纬度")
    private String lat;

    @ApiModelProperty("核准日期")
    private Date checkDate;

    @ApiModelProperty("企业状态")
    private String status;

    @ApiModelProperty("实缴资本描述")
    private String payCapi;

    @ApiModelProperty("实缴资本数值")
    private BigDecimal payCapiValue;

    @ApiModelProperty("实缴资本币种单位")
    private String payCapiUnit;

    @ApiModelProperty("实缴资本数值（以人民币计算）")
    private BigDecimal payCapiValueCal;

    @ApiModelProperty("注册资本描述")
    private String registCapi;

    @ApiModelProperty("注册资本数值")
    private BigDecimal registCapiValue;

    @ApiModelProperty("注册资本币种单位")
    private String registCapiUnit;

    @ApiModelProperty("注册资本数值（以人民币计算）")
    private BigDecimal registCapiValueCal;

    @ApiModelProperty("组织机构代码")
    private String orgNo;

    @ApiModelProperty("公司类型")
    private String companyType;

    @ApiModelProperty("企业规模")
    private String companyScale;

    @ApiModelProperty("营业期限")
    private String businessTerm;

    @ApiModelProperty("纳税人识别号")
    private String taxpayerNo;

    @ApiModelProperty("国民经济行业分类-门类")
    @TableField("nation_industry_1")
    private String nationIndustry1;

    @ApiModelProperty("国民经济行业分类-大类")
    @TableField("nation_industry_2")
    private String nationIndustry2;

    @ApiModelProperty("国民经济行业分类-中类")
    @TableField("nation_industry_3")
    private String nationIndustry3;

    @ApiModelProperty("国民经济行业分类-小类")
    @TableField("nation_industry_4")
    private String nationIndustry4;

    @ApiModelProperty("国民经济行业分类代码")
    private String nationIndustryCode;

    @ApiModelProperty("登记机关")
    private String belongOrg;

    @ApiModelProperty("工商注册号")
    private String registrationId;

    @ApiModelProperty("经营范围")
    private String businessScope;

    @ApiModelProperty("企业简介")
    private String description;

    @ApiModelProperty("数据是否有效")
    private Integer isValid;

    @ApiModelProperty("数据创建时间")
    private Date createTime;

    @ApiModelProperty("数据修改时间")
    private Date updateTime;

    @ApiModelProperty("有效手机号")
    private String validPhone;

    @ApiModelProperty("通讯地址")
    private String contactAddress;

    @ApiModelProperty("传真")
    private String fax;

    @ApiModelProperty("邮政编码")
    private String postalCode;

    @TableField(exist = false)
    private String registration;

    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String NAME_EN = "name_en";

    public static final String USED_NAME = "used_name";

    public static final String CREDIT_CODE = "credit_code";

    public static final String LEGAL_PERSON = "legal_person";

    public static final String LOGO = "logo";

    public static final String LOGO_SOURCE = "logo_source";

    public static final String EMPLOYEE_SIZE = "employee_size";

    public static final String INSURED_NUMBER = "insured_number";

    public static final String TEL = "tel";

    public static final String EMAIL = "email";

    public static final String WEBSITE = "website";

    public static final String ADDRESS = "address";

    public static final String ESTABLISH_DATE = "establish_date";

    public static final String NATION = "nation";

    public static final String PROVINCE = "province";

    public static final String CITY = "city";

    public static final String AREA = "area";

    public static final String TOWN = "town";

    public static final String NATION_CODE = "nation_code";

    public static final String PROVINCE_CODE = "province_code";

    public static final String CITY_CODE = "city_code";

    public static final String AREA_CODE = "area_code";

    public static final String LNG = "lng";

    public static final String LAT = "lat";

    public static final String CHECK_DATE = "check_date";

    public static final String STATUS = "status";

    public static final String PAY_CAPI = "pay_capi";

    public static final String PAY_CAPI_VALUE = "pay_capi_value";

    public static final String PAY_CAPI_UNIT = "pay_capi_unit";

    public static final String PAY_CAPI_VALUE_CAL = "pay_capi_value_cal";

    public static final String REGIST_CAPI = "regist_capi";

    public static final String REGIST_CAPI_VALUE = "regist_capi_value";

    public static final String REGIST_CAPI_UNIT = "regist_capi_unit";

    public static final String REGIST_CAPI_VALUE_CAL = "regist_capi_value_cal";

    public static final String ORG_NO = "org_no";

    public static final String COMPANY_TYPE = "company_type";

    public static final String COMPANY_SCALE = "company_scale";

    public static final String BUSINESS_TERM = "business_term";

    public static final String TAXPAYER_NO = "taxpayer_no";

    public static final String NATION_INDUSTRY_1 = "nation_industry_1";

    public static final String NATION_INDUSTRY_2 = "nation_industry_2";

    public static final String NATION_INDUSTRY_3 = "nation_industry_3";

    public static final String NATION_INDUSTRY_4 = "nation_industry_4";

    public static final String NATION_INDUSTRY_CODE = "nation_industry_code";

    public static final String BELONG_ORG = "belong_org";

    public static final String REGISTRATION_ID = "registration_id";

    public static final String BUSINESS_SCOPE = "business_scope";

    public static final String DESCRIPTION = "description";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
