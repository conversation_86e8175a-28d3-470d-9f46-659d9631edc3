package com.quantchi.common.domain;

import com.quantchi.application.model.entity.CompanyType;
import com.quantchi.application.model.entity.IndustryChain;
import com.quantchi.application.model.entity.MoveOutReason;
import com.quantchi.application.model.vo.MoveInOptionalListVO;
import com.quantchi.contract.model.entity.*;
import com.quantchi.contract.model.vo.OptionalListVO;
import com.quantchi.sys.model.entity.SysOrg;
import com.quantchi.zone.model.entity.Park;
import com.quantchi.zone.model.entity.ZoneFunction;
import io.swagger.models.auth.In;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class InMemoryCache {

    @Getter
    private static List<SysOrg> sysOrgList;
    @Getter
    private static List<Park> parkingList;
    @Getter
    private static List<ZoneFunction> zoneFunctions;
    @Getter
    private static List<LeaseFunction> leaseFunctions;
    @Getter
    private static List<UtilitiesDepositStandard> utilitiesDepositStandardList;
    @Getter
    private static List<PowerConsumptionStandard> powerConsumptionStandardList;
    @Getter
    private static List<FirstRentPayTime> firstRentPayTimeList;
    @Getter
    private static List<DecorateTimeLimit> decorateTimeLimitList;
    @Getter
    private static List<IndustryChain> industryChainList;
    @Getter
    private static List<CompanyType> companyTypeList;
    @Getter
    private static List<MoveOutReason> moveOutReasonList;

    public static void setData(List<SysOrg> sysOrgList, List<Park> parkingList, List<ZoneFunction> zoneFunctions,
                               List<LeaseFunction> leaseFunctions, List<UtilitiesDepositStandard> utilitiesDepositStandardList,
                               List<PowerConsumptionStandard> powerConsumptionStandardList, List<FirstRentPayTime> firstRentPayTimeList,
                               List<DecorateTimeLimit> decorateTimeLimitList, List<IndustryChain> industryChainList, List<CompanyType> companyTypeList,
                               List<MoveOutReason> moveOutReasonList) {
        InMemoryCache.sysOrgList = sysOrgList;
        InMemoryCache.parkingList = parkingList;
        InMemoryCache.zoneFunctions = zoneFunctions;
        InMemoryCache.leaseFunctions = leaseFunctions;
        InMemoryCache.utilitiesDepositStandardList = utilitiesDepositStandardList;
        InMemoryCache.powerConsumptionStandardList = powerConsumptionStandardList;
        InMemoryCache.firstRentPayTimeList = firstRentPayTimeList;
        InMemoryCache.decorateTimeLimitList = decorateTimeLimitList;
        InMemoryCache.industryChainList = industryChainList;
        InMemoryCache.companyTypeList = companyTypeList;
        InMemoryCache.moveOutReasonList = moveOutReasonList;
    }

    public static Map<Long, String> getSysOrgMap() {
        return sysOrgList.stream().collect(Collectors.toMap(SysOrg::getId, SysOrg::getOrgName));
    }

    public static Map<String, String> getParkingMap() {
        return parkingList.stream().collect(Collectors.toMap(Park::getId, Park::getParkName));
    }

    public static Map<Integer, String> getZoneFunctionMap() {
        return zoneFunctions.stream().collect(Collectors.toMap(ZoneFunction::getId, ZoneFunction::getFunction));
    }

    public static Map<Integer, String> getLeaseFunctionMap() {
        return leaseFunctions.stream().collect(Collectors.toMap(LeaseFunction::getId, LeaseFunction::getFunction));
    }

    public static Map<Integer, String> getUtilitiesDepositStandardMap() {
        return utilitiesDepositStandardList.stream().collect(Collectors.toMap(UtilitiesDepositStandard::getId, UtilitiesDepositStandard::getStandard));
    }

    public static Map<Integer, String> getPowerConsumptionStandardMap() {
        return powerConsumptionStandardList.stream().collect(Collectors.toMap(PowerConsumptionStandard::getId, PowerConsumptionStandard::getStandard));
    }

    public static Map<Integer, String> getFirstRentPayTimeMap() {
        return firstRentPayTimeList.stream().collect(Collectors.toMap(FirstRentPayTime::getId, FirstRentPayTime::getPayTime));
    }

    public static Map<Integer, String> getDecorateTimeLimitMap() {
        return decorateTimeLimitList.stream().collect(Collectors.toMap(DecorateTimeLimit::getId, DecorateTimeLimit::getTimeLimit));
    }

    public static Map<String, String> getIndustryChainMap() {
        return industryChainList.stream().collect(Collectors.toMap(IndustryChain::getId, IndustryChain::getName));
    }

    public static Map<Integer, String> getCompanyTypeMap() {
        return companyTypeList.stream().collect(Collectors.toMap(CompanyType::getId, CompanyType::getName));
    }

    public static Map<Integer, String> getMoveOutReasonMap() {
        return moveOutReasonList.stream().collect(Collectors.toMap(MoveOutReason::getId, MoveOutReason::getMoveOutReason));
    }

    public static OptionalListVO getOptionalListVO() {
        OptionalListVO optionalListVO = new OptionalListVO();
        optionalListVO.setZoneFunctions(zoneFunctions);
        optionalListVO.setLeaseFunctions(leaseFunctions);
        optionalListVO.setUtilitiesDepositStandardList(utilitiesDepositStandardList);
        optionalListVO.setPowerConsumptionStandardList(powerConsumptionStandardList);
        optionalListVO.setFirstRentPayTimeList(firstRentPayTimeList);
        optionalListVO.setDecorateTimeLimitList(decorateTimeLimitList);
        return optionalListVO;
    }

    public static MoveInOptionalListVO getMoveInOptionalListVO() {
        MoveInOptionalListVO moveInOptionalListVO = new MoveInOptionalListVO();
        moveInOptionalListVO.setIndustryChainList(industryChainList);
        moveInOptionalListVO.setCompanyTypeList(companyTypeList);
        return moveInOptionalListVO;
    }

}
