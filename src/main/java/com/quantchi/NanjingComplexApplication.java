package com.quantchi;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Slf4j
//@EnableAsync
@EnableScheduling
@EnableSwagger2
@EnableCaching
@ConfigurationPropertiesScan
@SpringBootApplication
@MapperScan("com.quantchi.**.mapper")
public class NanjingComplexApplication {

    public static void main(String[] args) {
        SpringApplication.run(NanjingComplexApplication.class, args);
    }

}
