package com.quantchi.common.constant;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/9/22 11:06
 */
@ApiModel("筛选项查询类型")
@AllArgsConstructor
@Getter
public class SearchTypeConstants {


    /**
     * 正常值搜索
     */
    public static final int USUAL_QUERY = 0;

    /**
     * 日期范围搜索，单位月
     */
    public static final int DATE_MONTH = 1;

    /**
     * 金额范围搜索，单位万元
     */
    public static final int AMOUNT_YUAN = 2;

    /**
     * 日期范围搜索，单位天
     */
    public static final int DATE_DAY = 3;

    /**
     * 级联搜索
     */
    public static final int CASCADE = 4;

    /**
     * 一个筛选项对应多个范围值
     */
    public static final int ONE_TO_MANY = 5;

    /**
     * 范围搜索
     */
    public static final int RANGE_QUERY = 6;

}
