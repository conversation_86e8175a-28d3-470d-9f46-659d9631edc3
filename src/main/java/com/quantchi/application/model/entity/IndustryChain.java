package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 产业链代码表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("industry_chain")
@ApiModel(value = "IndustryChain对象", description = "产业链代码表")
public class IndustryChain implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产业链id")
    private String id;

    @ApiModelProperty("产业链名称")
    private String name;

    @ApiModelProperty("产业链英文名称")
    private String nameEn;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("是否完善，1为完善，0为不完善")
    private Boolean isComplete;

    @ApiModelProperty("有效标记，1为有效，0为无效")
    private Boolean isValid;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("父链名称")
    private String parentName;

    @ApiModelProperty("父链英文名称")
    private String parentNameEn;

    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String NAME_EN = "name_en";

    public static final String SORT = "sort";

    public static final String IS_COMPLETE = "is_complete";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String PARENT_NAME = "parent_name";

    public static final String PARENT_NAME_EN = "parent_name_en";
}
