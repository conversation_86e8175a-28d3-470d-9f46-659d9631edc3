package com.quantchi.common.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2023/10/11 14:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("带纠错结果的分页结果类")
@NoArgsConstructor
public class ErrorCorrectionPageResult extends EsPageResult {

    @ApiModelProperty("原本查询的关键词")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String originalKeyword;

    @ApiModelProperty("实际查询的关键词")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String actualKeyword;

    public ErrorCorrectionPageResult(final EsPageResult esPageResult) {
        BeanUtils.copyProperties(esPageResult, this);
    }

}
