package com.quantchi.application.controller;

import com.quantchi.application.model.bo.AuditProcessBO;
import com.quantchi.application.model.bo.AuditQueryBO;
import com.quantchi.application.model.bo.InitiateTransferBO;
import com.quantchi.application.model.vo.AuditOptionalListVO;
import com.quantchi.application.model.vo.AuditPageVO;
import com.quantchi.application.model.vo.MoveInOptionalListVO;
import com.quantchi.application.service.IApplicationService;
import com.quantchi.application.service.IAuditService;
import com.quantchi.common.domain.CustomIndexNavSetting;
import com.quantchi.common.domain.InMemoryCache;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.sys.service.impl.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/audit")
@Slf4j
@Api(tags = "审批管理接口")
@RequiredArgsConstructor
@Validated
public class AuditController {

    private final IApplicationService applicationService;
    private final IAuditService auditService;

    @GetMapping("/optionalList")
    @ApiOperation("下拉框选项")
    public ResultInfo<List<CustomIndexNavSetting>> optionalList() {

        return ResultConvert.success(auditService.optionalList());

    }

    @PostMapping("/pageAuditRecords")
    @ApiOperation("分页查询审批列表")
    public ResultInfo<AuditPageVO> pageAuditRecords(@RequestBody @Valid AuditQueryBO queryBO) {
        try {
            return ResultConvert.success(auditService.pageAuditRecords(queryBO));
        } catch (Exception e) {
            log.error("分页查询审批列表异常", e);
            return ResultConvert.error("分页查询审批列表异常: " + e.getMessage());
        }
    }
    
    @PostMapping("/processAudit")
    @ApiOperation("审批处理")
    public ResultInfo<String> processAudit(@RequestBody @Valid AuditProcessBO auditProcessBO) {
        try {
            boolean success = auditService.processAudit(auditProcessBO);
            if (success) {
                return ResultConvert.success("审批处理成功");
            } else {
                return ResultConvert.error("审批处理失败，申请可能已处理或不存在");
            }
        } catch (Exception e) {
            log.error("审批处理异常", e);
            return ResultConvert.error("审批处理异常: " + e.getMessage());
        }
    }

    @PostMapping("/exportAuditRecords")
    @ApiOperation("导出审批记录")
    public void exportAuditRecords(@RequestBody AuditQueryBO queryBO, HttpServletResponse response) {
        try {
            auditService.exportAuditRecords(queryBO, response);
        } catch (Exception e) {
            log.error("导出审批记录异常", e);
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    @PostMapping("/initiateTransfer")
    @ApiOperation("发起流转")
    public ResultInfo<String> initiateTransfer(@RequestBody @Valid InitiateTransferBO initiateTransferBO) {
        try {
            boolean success = auditService.initiateTransfer(initiateTransferBO);
            if (success) {
                return ResultConvert.success("流转成功");
            } else {
                return ResultConvert.error("流转失败，申请可能不存在或状态异常");
            }
        } catch (Exception e) {
            log.error("发起流转异常", e);
            return ResultConvert.error("发起流转异常: " + e.getMessage());
        }
    }
}
