package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 迁出原因
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("move_out_reason")
@ApiModel(value = "MoveOutReason对象", description = "迁出原因")
public class MoveOutReason implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty("迁出原因")
    private String moveOutReason;

    public static final String ID = "id";

    public static final String MOVE_OUT_REASON = "move_out_reason";
}
