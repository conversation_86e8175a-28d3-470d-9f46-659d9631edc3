package com.quantchi.application.service;

import com.quantchi.application.model.entity.Application;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.application.model.bo.ApplicationQueryBO;
import com.quantchi.application.model.vo.ApplicationPageVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 申请 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IApplicationService extends IService<Application> {
    
    /**
     * 分页查询办事记录
     *
     * @param queryBO 查询参数
     * @return 分页结果
     */
    ApplicationPageVO pageApplicationRecords(ApplicationQueryBO queryBO);
    
    /**
     * 撤销办事申请
     *
     * @param applicationId 申请ID
     * @return 是否撤销成功
     */
    boolean cancelApplication(String applicationId);

    /**
     * 下载装修承诺书
     *
     * @param response HTTP响应
     * @throws Exception 下载异常
     */
    void downloadDecorationCommitment(HttpServletResponse response) throws Exception;
}
