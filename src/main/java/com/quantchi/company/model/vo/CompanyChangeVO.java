package com.quantchi.company.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@ApiModel(value = "企业变更记录")
public class CompanyChangeVO {

    @ApiModelProperty("变更日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date changeDate;

    @ApiModelProperty("变更项目，如章程备案、股权变更、地址变更")
    private String changeProject;

    @ApiModelProperty("变更前")
    private String changeBefore;

    @ApiModelProperty("变更后")
    private String changeAfter;

}
