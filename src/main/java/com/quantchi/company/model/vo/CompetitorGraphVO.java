package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/27 下午4:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompetitorGraphVO {

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "x坐标值")
    private String xCoordinateValue;

    @ApiModelProperty(value = "y坐标值")
    private String yCoordinateValue;

}
