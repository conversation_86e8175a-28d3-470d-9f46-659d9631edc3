package com.quantchi.application.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 入驻申请业务对象
 */
@Data
@ApiModel(value = "ApplicationMoveInBO", description = "入驻申请业务对象")
public class ApplicationMoveInBO extends ApplicationMoveInBaseBO{

    /**
     * 是否有经营异常（0否，1是）
     */
    @ApiModelProperty("是否有经营异常（0否，1是）")
    private Byte isAbnormalOperation;

    /**
     * 是否科技型中小企业（0否，1是）
     */
    @ApiModelProperty("是否科技型中小企业（0否，1是）")
    private Byte isTechEnterprise;

    /**
     * 是否高新产品（0否，1是）
     */
    @ApiModelProperty("是否高新产品（0否，1是）")
    private Byte isHighTechProduct;

    /**
     * 是否高新企业（0否，1是）
     */
    @ApiModelProperty("是否高新企业（0否，1是）")
    private Byte isHighTechEnterprise;

    /**
     * 上年度末注册资本
     */
    @ApiModelProperty("上年度末注册资本")
    private String registeredCapitalLastYear;

    /**
     * 上年度末从业人员
     */
    @ApiModelProperty("上年度末从业人员")
    private String employeesLastYear;

    /**
     * 上年度末知识产权
     */
    @ApiModelProperty("上年度末知识产权")
    private String intellectualPropertyLastYear;

    /**
     * 上年度实际总收入
     */
    @ApiModelProperty("上年度实际总收入")
    private String totalRevenueLastYear;

    /**
     * 上年度实际净利润
     */
    @ApiModelProperty("上年度实际净利润")
    private String netProfitLastYear;

    /**
     * 上年度实际纳税额
     */
    @ApiModelProperty("上年度实际纳税额")
    private String taxPaidLastYear;


}


