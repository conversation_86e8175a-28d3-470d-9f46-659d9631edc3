package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_finance")
@ApiModel(value = "CompanyFinance对象", description = "")
public class CompanyFinance implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("关联企业库id")
    private String cid;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("季度")
    private Integer quarter;

    @ApiModelProperty("利润总额万元")
    private BigDecimal totalProfit;

    @ApiModelProperty("利润总额同比")
    private BigDecimal totalProfitYoy;

    @ApiModelProperty("毛利率(%)")
    private BigDecimal grossMargin;

    @ApiModelProperty("毛利率同比")
    private BigDecimal grossMarginYoy;

    @ApiModelProperty("净利率(%)")
    private BigDecimal netMargin;

    @ApiModelProperty("净利率同比")
    private BigDecimal netMarginYoy;

    @ApiModelProperty("员工人数")
    private Integer person;

    @ApiModelProperty("人均利润产值：利润总额/员工人数")
    private BigDecimal laborProductivity;

    @ApiModelProperty("研发人员人数")
    private Integer devPerson;

    @ApiModelProperty("研发支出合计万元")
    private BigDecimal devExpense;

    @ApiModelProperty("研发支出合计同比")
    private BigDecimal devExpenseYoy;

    @ApiModelProperty("研发支出资本化万元")
    private BigDecimal devCapital;

    @ApiModelProperty("研发支出资本化率")
    private BigDecimal devCapRate;

    @ApiModelProperty("资产总额万元")
    private BigDecimal totalAssets;

    @ApiModelProperty("资产总额同比")
    private BigDecimal totalAssetsYoy;

    @ApiModelProperty("固定资产万元")
    private BigDecimal fixedAssets;

    @ApiModelProperty("固定资产同比")
    private BigDecimal fixedAssetsYoy;

    @ApiModelProperty("负债总额万元")
    private BigDecimal totalLiabilty;

    @ApiModelProperty("负债总额同比")
    private BigDecimal totalLiabiltyYoy;

    @ApiModelProperty("资产负债率(%)")
    private BigDecimal assetLiabilty;

    @ApiModelProperty("资产负债率同比")
    private BigDecimal assetLiabiltyYoy;

    @ApiModelProperty("净利润万元")
    private BigDecimal netProfit;

    @ApiModelProperty("净利润同比")
    private BigDecimal netProfitYoy;

    @ApiModelProperty("归属于母公司的净利润万元")
    private BigDecimal netProfitParent;

    @ApiModelProperty("归属于母公司的净利润同比")
    private BigDecimal netProfitParentYoy;

    @ApiModelProperty("应付账款万元")
    private BigDecimal acountsPayable;

    @ApiModelProperty("应收账款万元")
    private BigDecimal accountsReceivable;

    @ApiModelProperty("净资产收益率ROE(%)")
    private BigDecimal roe;

    @ApiModelProperty("净资产收益率ROE同比")
    private BigDecimal roeYoy;

    @ApiModelProperty("营业总收入万元")
    private BigDecimal grossIncome;

    @ApiModelProperty("营业总收入同比")
    private BigDecimal grossIncomeYoy;

    @ApiModelProperty("主营营业收入万元")
    private BigDecimal mainIncome;

    @ApiModelProperty("主营营业收入同比")
    private BigDecimal mainIncomeYoy;

    @ApiModelProperty("所得税")
    private BigDecimal taxScale;

    @ApiModelProperty("所得税同比")
    private BigDecimal taxScaleYoy;

    @ApiModelProperty("融资规模 元")
    private BigDecimal financeScale;

    @ApiModelProperty("融资规模同比")
    private BigDecimal financeScaleYoy;

    @ApiModelProperty("前一大股东占比")
    private BigDecimal firstShareholder;

    @ApiModelProperty("前一大股东占比环比")
    private BigDecimal firstShareholderYoy;

    @ApiModelProperty("前五大股东占比")
    private BigDecimal fiveShareholder;

    @ApiModelProperty("前五大股东占比环比")
    private BigDecimal fiveShareholderYoy;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String CID = "cid";

    public static final String NAME = "name";

    public static final String YEAR = "year";

    public static final String QUARTER = "quarter";

    public static final String TOTAL_PROFIT = "total_profit";

    public static final String TOTAL_PROFIT_YOY = "total_profit_yoy";

    public static final String GROSS_MARGIN = "gross_margin";

    public static final String GROSS_MARGIN_YOY = "gross_margin_yoy";

    public static final String NET_MARGIN = "net_margin";

    public static final String NET_MARGIN_YOY = "net_margin_yoy";

    public static final String PERSON = "person";

    public static final String LABOR_PRODUCTIVITY = "labor_productivity";

    public static final String DEV_PERSON = "dev_person";

    public static final String DEV_EXPENSE = "dev_expense";

    public static final String DEV_EXPENSE_YOY = "dev_expense_yoy";

    public static final String DEV_CAPITAL = "dev_capital";

    public static final String DEV_CAP_RATE = "dev_cap_rate";

    public static final String TOTAL_ASSETS = "total_assets";

    public static final String TOTAL_ASSETS_YOY = "total_assets_yoy";

    public static final String FIXED_ASSETS = "fixed_assets";

    public static final String FIXED_ASSETS_YOY = "fixed_assets_yoy";

    public static final String TOTAL_LIABILTY = "total_liabilty";

    public static final String TOTAL_LIABILTY_YOY = "total_liabilty_yoy";

    public static final String ASSET_LIABILTY = "asset_liabilty";

    public static final String ASSET_LIABILTY_YOY = "asset_liabilty_yoy";

    public static final String NET_PROFIT = "net_profit";

    public static final String NET_PROFIT_YOY = "net_profit_yoy";

    public static final String NET_PROFIT_PARENT = "net_profit_parent";

    public static final String NET_PROFIT_PARENT_YOY = "net_profit_parent_yoy";

    public static final String ACOUNTS_PAYABLE = "acounts_payable";

    public static final String ACCOUNTS_RECEIVABLE = "accounts_receivable";

    public static final String ROE = "roe";

    public static final String ROE_YOY = "roe_yoy";

    public static final String GROSS_INCOME = "gross_income";

    public static final String GROSS_INCOME_YOY = "gross_income_yoy";

    public static final String MAIN_INCOME = "main_income";

    public static final String MAIN_INCOME_YOY = "main_income_yoy";

    public static final String TAX_SCALE = "tax_scale";

    public static final String TAX_SCALE_YOY = "tax_scale_yoy";

    public static final String FINANCE_SCALE = "finance_scale";

    public static final String FINANCE_SCALE_YOY = "finance_scale_yoy";

    public static final String FIRST_SHAREHOLDER = "first_shareholder";

    public static final String FIRST_SHAREHOLDER_YOY = "first_shareholder_yoy";

    public static final String FIVE_SHAREHOLDER = "five_shareholder";

    public static final String FIVE_SHAREHOLDER_YOY = "five_shareholder_yoy";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
