package com.quantchi.contract.service.impl;

import com.quantchi.contract.model.entity.DecorateTimeLimit;
import com.quantchi.contract.mapper.DecorateTimeLimitMapper;
import com.quantchi.contract.service.IDecorateTimeLimitService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 装修改造期限制 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class DecorateTimeLimitServiceImpl extends ServiceImpl<DecorateTimeLimitMapper, DecorateTimeLimit> implements IDecorateTimeLimitService {

}
