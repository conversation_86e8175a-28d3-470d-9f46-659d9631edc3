package com.quantchi.application.model.bo;

import com.quantchi.common.domain.bo.PageBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 申请查询参数对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ApplicationQueryBO对象", description = "办事记录查询参数")
public class ApplicationQueryBO extends PageBO {
    
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private Integer applyType;
    
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private Integer status;
}
