package com.quantchi.company.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quantchi.common.constant.Constants;
import com.quantchi.common.domain.CustomIndexNavSetting;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.enums.EsIndexEnum;
import com.quantchi.common.model.EsPageResult;
import com.quantchi.common.model.MultidimensionalQuery;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.company.mapper.CompanyInvestMapper;
import com.quantchi.company.mapper.DmDivisionMapper;
import com.quantchi.application.model.bo.AddContactBO;
import com.quantchi.application.model.bo.ContactListQueryBO;
import com.quantchi.application.model.bo.EditContactBO;
import com.quantchi.application.model.vo.CompanyContactVO;
import com.quantchi.application.model.vo.ContactListPageVO;
import com.quantchi.application.service.IApplicationContactService;
import com.quantchi.company.model.entity.*;
import com.quantchi.company.model.vo.*;
import com.quantchi.company.service.*;
import com.quantchi.sys.config.annotation.Log;
import com.quantchi.sys.model.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.quantchi.company.constants.Constant.LIBRARY_INTERFACE_NOTE;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Slf4j
@RestController
@RequestMapping("/company")
@Api(tags = "企业基本信息接口")
@RequiredArgsConstructor
public class CompanyPortraitController {

    private final ICompanyService companyService;

    private final ICompanyLeaderService companyLeaderService;

    private final ICompanyShareholderService companyShareholderService;

    private final ICompanyBranchService companyBranchService;

    private final ICompanyChangeService companyChangeService;

    private final IApplicationContactService applicationContactService;

    private final DmDivisionMapper dmDivisionMapper;

    private final CompanyInvestMapper companyInvestMapper;

    @GetMapping("/getNavSetting")
    @ApiOperation(value = "筛选条目")
    public ResultInfo<List<CustomIndexNavSetting>> getNavSetting() {

        return ResultConvert.success(companyService.getNavSetting());

    }

    @PostMapping("/filtering")
    @ApiOperation(value = "列表查询", notes = LIBRARY_INTERFACE_NOTE)
    @Log(title = "企业管理-查询企业", businessType = BusinessType.QUERY)
    public ResultInfo<EsPageResult> queryByTermsAndKey(
            @NonNull @RequestBody final MultidimensionalQuery query) {
        String index = query.getIndex();
        final String type = query.getType();
        if (StrUtil.isNotBlank(type)) {
            index = EsIndexEnum.getEsIndexByType(type);
            query.setIndex(index);
        }
        // 获取对应的服务类
        return ResultConvert.success(companyService.getLibraryList(query));
    }

    
    @GetMapping("/baseInfo")
    @ApiOperation("企业基础工商信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<CompanyVO> baseInfo(@RequestParam final String id) {
        return ResultConvert.success(companyService.baseInfo(id));
    }

    @GetMapping("/shareholderInfo")
    @ApiOperation("股东信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public ResultInfo<PageInfo<CompanyShareholderVO>> shareholderInfo(
            @RequestParam final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyShareholder> list = companyShareholderService.list(Wrappers.<CompanyShareholder>lambdaQuery()
                .eq(CompanyShareholder::getCid, id)
                .eq(CompanyShareholder::getIsValid, 1)
                .orderByDesc(CompanyShareholder::getStockPercent));
        final PageInfo<CompanyShareholder> pageInfo = new PageInfo<>(list);
        final PageInfo<CompanyShareholderVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyShareholderVO> voList = new ArrayList<>(list.size());
        // 检查企业是否存在
        final Set<String> existCompanyIdList;
        final List<String> companyIdList = list.stream().map(CompanyShareholder::getShareholderId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(companyIdList)) {
            final List<Company> companies = companyService.listByIds(companyIdList);
            existCompanyIdList = companies.stream().map(Company::getId).collect(Collectors.toSet());
        } else {
            existCompanyIdList = Collections.emptySet();
        }

        list.forEach(item -> {
            final CompanyShareholderVO vo = new CompanyShareholderVO();
            vo.setShareholderName(item.getShareholderName());
            if (existCompanyIdList.contains(item.getShareholderId())) {
                vo.setShareholderId(item.getShareholderId());
            }
            vo.setShareholderType(item.getShareholderType());
            final BigDecimal stockPercent = item.getStockPercent();
            if (stockPercent != null) {
                vo.setStockPercent(stockPercent.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
            }
            final BigDecimal subscribedAmount = item.getSubscribedAmount();
            if (subscribedAmount != null) {
                vo.setSubscribedAmount(subscribedAmount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toPlainString());
            }
            vo.setSubscribedDate(item.getSubscribedDate());
            final BigDecimal paidAmount = item.getPaidAmount();
            if (paidAmount != null) {
                vo.setPaidAmount(paidAmount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toPlainString());
            }
            vo.setPaidDate(item.getPaidDate());
            voList.add(vo);
        });
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }


    @GetMapping("/keyPersonnel")
    @ApiOperation("主要人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public ResultInfo<PageInfo<CompanyLeaderVO>> keyPersonnel(
            @RequestParam final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyLeader> companyLeaderList = companyLeaderService.list(Wrappers.<CompanyLeader>lambdaQuery()
                .eq(CompanyLeader::getCid, id)
                .eq(CompanyLeader::getIsValid, 1)
                .orderByAsc(CompanyLeader::getLzLevel)
                .orderByAsc(CompanyLeader::getLevel));
        final PageInfo<CompanyLeader> pageInfo = new PageInfo<>(companyLeaderList);
        final PageInfo<CompanyLeaderVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyLeaderVO> voList = new ArrayList<>(companyLeaderList.size());
        companyLeaderList.forEach(companyLeader -> {
            final CompanyLeaderVO vo = new CompanyLeaderVO();
            vo.setName(companyLeader.getName());
            vo.setPosition(companyLeader.getPosition());
            vo.setDescription(companyLeader.getDescription());
            voList.add(vo);
        });
        // 根据cid和人员信息去股东信息表获取持股比例
        final List<String> nameList = voList.stream().map(CompanyLeaderVO::getName).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(nameList)) {
            final List<CompanyShareholder> list = companyShareholderService.list(Wrappers.<CompanyShareholder>lambdaQuery()
                    .eq(CompanyShareholder::getCid, id)
                    .eq(CompanyShareholder::getIsValid, 1)
                    .in(CompanyShareholder::getShareholderName, nameList));
            final Map<String, BigDecimal> shareholderPercentMap = list.stream()
                    .filter(item -> item.getStockPercent() != null).collect(Collectors.toMap(CompanyShareholder::getShareholderName, CompanyShareholder::getStockPercent, (a, b) -> a));
            voList.forEach(vo -> {
                final BigDecimal shareholderPercent = shareholderPercentMap.get(vo.getName());
                if (shareholderPercent != null) {
                    vo.setShareholdingRatio(shareholderPercent.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
                }
            });
        }
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/investment")
    @ApiOperation("对外投资企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "Integer", dataTypeClass = Integer.class)
    })
    public ResultInfo<PageInfo<CompanyInvestVO>> investment(
            @RequestParam final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        //投资事件 不去重
        List<CompanyInvest> list = companyInvestMapper.getDirectInvestByCid(id, null, null, 0);
        final Set<String> provinceCodeList = list.stream().map(CompanyInvest::getInvestProvinceCode).filter(Objects::nonNull).collect(Collectors.toSet());
        final Set<String> cityCodeList = list.stream().map(CompanyInvest::getInvestCityCode).filter(Objects::nonNull).collect(Collectors.toSet());
        final Set<String> areaCodeList = list.stream().map(CompanyInvest::getInvestAreaCode).filter(Objects::nonNull).collect(Collectors.toSet());
        final Collection<String> union = CollUtil.union(provinceCodeList, cityCodeList, areaCodeList);
        final Map<String, String> codeMap;
        if (CollUtil.isNotEmpty(union)) {
            final List<DmDivision> dmDivisions = dmDivisionMapper.selectList(Wrappers.<DmDivision>lambdaQuery()
                    .in(DmDivision::getCode, union));
            codeMap = dmDivisions.stream().collect(Collectors.toMap(DmDivision::getCode, DmDivision::getName));
        } else {
            codeMap = new HashMap<>();
        }
        final PageInfo<CompanyInvestVO> resultPageInfo = new PageInfo<>();
        resultPageInfo.setTotal(list.size());
        list = list.stream()
                .sorted(Comparator.comparing(CompanyInvest::getInvestedEstablishedDate, Comparator.nullsLast(Comparator.naturalOrder()))
                        .reversed())
                .skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        final List<CompanyInvestVO> voList = new ArrayList<>(list.size());
        Set<String> existInvestedIdList = list.stream().map(CompanyInvest::getInvestedId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(existInvestedIdList)) {
            final List<Company> companyList = companyService.list(Wrappers.<Company>lambdaQuery()
                    .select(Company::getId)
                    .in(Company::getId, existInvestedIdList));
            existInvestedIdList = companyList.stream().map(Company::getId).collect(Collectors.toSet());
        }
        for (final CompanyInvest item : list) {
            final CompanyInvestVO vo = new CompanyInvestVO();
            if (existInvestedIdList.contains(item.getInvestedId())) {
                vo.setInvestedId(item.getInvestedId());
            }
            vo.setInvestedName(item.getInvestedName());
            vo.setInvestedStatus(item.getInvestedStatus());
            vo.setInvestedEstablishedDate(item.getInvestedEstablishedDate());
            final BigDecimal investRatio = item.getInvestRatio();
            if (investRatio != null) {
                // 保留两位小数，然后加上‘%’
                vo.setInvestRatio(investRatio.setScale(2, RoundingMode.HALF_UP).toPlainString() + "%");
            }
            vo.setInvestAmount(item.getInvestAmount());
            // 所属地区，使用省市代码获取省市区名称，然后拼接出来
            vo.setBelongArea(Constants.getBelongArea(codeMap.getOrDefault(item.getInvestProvinceCode(), ""),
                    codeMap.getOrDefault(item.getInvestCityCode(), ""),
                    codeMap.getOrDefault(item.getInvestAreaCode(), "")));
            vo.setInvestedIndustry(item.getInvestedIndustry());
            vo.setInvestType(item.getInvestType());
            voList.add(vo);
        }
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/branch")
    @ApiOperation("分支机构")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public ResultInfo<PageInfo<CompanyBranchVO>> branch(
            @RequestParam final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyBranch> list = companyBranchService.list(Wrappers.<CompanyBranch>lambdaQuery()
                .eq(CompanyBranch::getIsValid, 1)
                .eq(CompanyBranch::getCid, id));
        final PageInfo<CompanyBranch> pageInfo = new PageInfo<>(list);
        final PageInfo<CompanyBranchVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyBranchVO> voList = new ArrayList<>(list.size());
        final List<String> branchIdList = new ArrayList<>(list.size());
        list.forEach(item -> {
            final CompanyBranchVO vo = new CompanyBranchVO();
            final String branchId = item.getBranchId();
            vo.setBranchId(branchId);
            vo.setBranchName(item.getBranchName());
            if (branchId != null) {
                branchIdList.add(branchId);
            }
            voList.add(vo);
        });
        // 从企业主表里获取企业信息
        if (!branchIdList.isEmpty()) {
            final List<Company> branchComanyList = companyService.list(Wrappers.<Company>lambdaQuery()
                    .in(Company::getId, branchIdList));
            if (!branchComanyList.isEmpty()) {
                final Map<String, Company> collect = branchComanyList.stream().collect(Collectors.toMap(Company::getId, Function.identity()));
                voList.forEach(vo -> {
                    final Company company = collect.get(vo.getBranchId());
                    if (company != null) {
                        vo.setStatus(company.getStatus());
                        vo.setBelongArea(Constants.getBelongArea(company.getProvince(), company.getCity(), company.getArea()));
                        vo.setEstablishDate(company.getEstablishDate());
                        vo.setDirector(company.getLegalPerson());
                    } else {
                        vo.setBranchId(null);
                    }
                });
            } else {
                voList.forEach(vo -> vo.setBranchId(null));
            }
        }
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/change")
    @ApiOperation("变更记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = false, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public ResultInfo<PageInfo<CompanyChangeVO>> change(
            @RequestParam final String id,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CompanyChange> list = companyChangeService.list(Wrappers.<CompanyChange>lambdaQuery()
                .eq(CompanyChange::getCid, id)
                .orderByDesc(CompanyChange::getChangeDate));
        final PageInfo<CompanyChange> pageInfo = new PageInfo<>(list);
        final PageInfo<CompanyChangeVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        final List<CompanyChangeVO> voList = new ArrayList<>(list.size());
        list.forEach(item -> {
            final CompanyChangeVO vo = new CompanyChangeVO();
            vo.setChangeDate(item.getChangeDate());
            vo.setChangeProject(item.getChangeProject());
            vo.setChangeBefore(item.getChangeBefore());
            vo.setChangeAfter(item.getChangeAfter());
            voList.add(vo);
        });
        resultPageInfo.setList(voList);
        return ResultConvert.success(resultPageInfo);
    }

    @GetMapping("/searchByName")
    @ApiOperation("按企业名称模糊搜索")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "关键词", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<List<CompanyItemVO>> searchByName(@RequestParam final String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return ResultConvert.success(Collections.emptyList());
        }
        return ResultConvert.success(companyService.searchByName(keyword));
    }

    @GetMapping("/companyInfo")
    @ApiOperation("企业关键信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业ID", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<CompanyInfoVO> companyInfo(@RequestParam String companyId) {
        CompanyInfoVO info = companyService.getCompanyInfo(companyId);
        return ResultConvert.success(info);
    }

    @PostMapping("/contactList")
    @ApiOperation("企业联系人分页列表")
    public ResultInfo<ContactListPageVO> getCompanyContactList(@RequestBody @Valid ContactListQueryBO queryBO) {
        ContactListPageVO pageVO = applicationContactService.pageContactsByCondition(queryBO);
        return ResultConvert.success(pageVO);
    }

    @PutMapping("/editContact")
    @ApiOperation("编辑企业联系人信息")
    @Log(title = "企业管理-编辑联系人", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> editContact(@Valid @RequestBody EditContactBO editContactBO) {
        boolean result = applicationContactService.editContact(editContactBO);
        return ResultConvert.success(result);
    }

    @PostMapping("/addContact")
    @ApiOperation("新增企业联系人")
    @Log(title = "企业管理-新增联系人", businessType = BusinessType.INSERT)
    public ResultInfo<Boolean> addContact(@Valid @RequestBody AddContactBO addContactBO) {
        boolean result = applicationContactService.addContact(addContactBO);
        return ResultConvert.success(result);
    }

    @DeleteMapping("/deleteContact")
    @ApiOperation("删除企业联系人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contactId", value = "联系人ID", required = true, dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "企业管理-删除联系人", businessType = BusinessType.DELETE)
    public ResultInfo<Boolean> deleteContact(@RequestParam String contactId) {
        boolean result = applicationContactService.deleteContact(contactId);
        return ResultConvert.success(result);
    }

    @PutMapping("/updateRegistration")
    @ApiOperation("修改企业注册方式")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业ID", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "registration", value = "注册方式（实体注册/虚拟注册）", required = true, dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "企业管理-修改注册方式", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> updateCompanyRegistration(
            @RequestParam String companyId,
            @RequestParam String registration) {
        boolean result = companyService.updateCompanyRegistration(companyId, registration);
        return ResultConvert.success(result);
    }

//    @PostMapping("/addCompany")
//    @ApiOperation("新增企业")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "companyName", value = "企业名称", required = true, dataType = "String", dataTypeClass = String.class)
//    })
//    public ResultInfo<String> addCompany(@RequestParam final String companyName) {
//        return ResultConvert.success(companyService.addCompany(companyName));
//    }

}
