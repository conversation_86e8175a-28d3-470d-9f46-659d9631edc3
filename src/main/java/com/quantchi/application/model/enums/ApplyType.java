package com.quantchi.application.model.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum ApplyType {
    MOVE_IN(1, "入驻申请"),
    MOVE_OUT(2, "迁出申请"),
    DECORATE(3, "装修申请"),
    ;

    private final Integer code;
    private final String info;

    ApplyType(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static String getInfoByCode(Integer code) {
        for (ApplyType status : ApplyType.values()) {
            if (status.getCode().equals(code)) {
                return status.getInfo();
            }
        }
        return "";
    }
}
