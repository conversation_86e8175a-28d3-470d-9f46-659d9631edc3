package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class InvestmentExpansionVO {

    @ApiModelProperty("投资活跃度")
    private String investmentActivity;

    @ApiModelProperty("对外直接投资企业数量")
    private Long totalInvestedCompanies;

    @ApiModelProperty("对外直接投资企业总数")
    private Long investedDirectlyCompanyNum;

    @ApiModelProperty("对外间接投资企业总数")
    private Long investedIndirectlyCompanyNum;

    @ApiModelProperty("分支机构数量")
    private Long branchCount;

    @ApiModelProperty("对外投资总金额")
    private NumberUnitVO totalInvestmentAmount;

    @ApiModelProperty("直接控制企业数量")
    private int controlledCompanyNum;

    @ApiModelProperty("间接控制企业数量")
    private int indirectControlCompanyNum;

    @ApiModelProperty("投资偏好城市")
    private String preferredCity;

    @ApiModelProperty("投资偏好产业")
    private String preferredIndustry;

    @ApiModelProperty("投资历史")
    private List<InvestmentPeriod> investmentHistory;

    @Data
    public static class InvestmentPeriod {

        @ApiModelProperty("时间")
        private String date;

        @ApiModelProperty("投资记录")
        private List<InvestmentHistory> investmentRecords;
    }

    @Data
    public static class InvestmentHistory {

        @ApiModelProperty("公司id")
        private String companyId;

        @ApiModelProperty("公司名称")
        private String companyName;

        @ApiModelProperty("投资比例")
        private String investmentRatio = "0";

        @ApiModelProperty("国标行业")
        private String nationalIndustry;

        @ApiModelProperty("主营产品")
        private List<String> mainProductList;
    }

}

