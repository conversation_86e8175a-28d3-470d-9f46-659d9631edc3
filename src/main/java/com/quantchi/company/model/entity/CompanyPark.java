package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_park")
@ApiModel(value = "CompanyPark对象", description = "")
public class CompanyPark implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("企业地址")
    private String registAddress;

    @ApiModelProperty("企业地址")
    private LocalDate registDate;

    @ApiModelProperty("产业类别")
    private String industryType;

    @ApiModelProperty("企业标签")
    private String tag;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String NAME = "name";

    public static final String CREDIT_CODE = "credit_code";

    public static final String REGIST_ADDRESS = "regist_address";

    public static final String REGIST_DATE = "regist_date";

    public static final String INDUSTRY_TYPE = "industry_type";

    public static final String TAG = "tag";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
