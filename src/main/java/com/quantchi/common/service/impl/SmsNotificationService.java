package com.quantchi.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.quantchi.common.utils.RedisUtils;
import com.quantchi.sys.service.impl.CaptchaSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 短信通知服务
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SmsNotificationService {

    private final CaptchaSendService captchaSendService;

    private final ThreadPoolTaskExecutor docExecutor;
    /**
     * 短信模板ID - 登录验证码模板
     */
    private static final String TEMPLATE_ID = "SMS_488635181";

    /**
     * 默认的手机号列表缓存键
     */
    private static final String PHONE_LIST_CACHE_KEY = "sms:notification:phone:list";

    /**
     * 短信发送记录缓存键前缀
     */
    private static final String SMS_SEND_RECORD_PREFIX = "sms:notification:record:";

    /**
     * 默认手机号列表（可以通过配置或数据库动态管理）
     */
    private static final List<String> DEFAULT_PHONE_LIST = Arrays.asList(
        "17816855273",
        "17603474358",
        "13270226772",
        "18058768173",
        "17858862568",
        "17364525653",
        "13165966913",
        "15857199219",
        "15695298023"
        // 在这里添加更多手机号
    );

    /**
     * 获取需要发送短信的手机号列表
     * 优先从缓存获取，如果缓存为空则使用默认列表
     * 
     * @return 手机号列表
     */
    public List<String> getPhoneList() {
        // 尝试从缓存获取手机号列表
        List<String> phoneList = RedisUtils.getCacheObject(PHONE_LIST_CACHE_KEY);
        
        if (CollUtil.isEmpty(phoneList)) {
            // 如果缓存为空，使用默认列表
            phoneList = new ArrayList<>(DEFAULT_PHONE_LIST);
            log.info("使用默认手机号列表，共{}个号码", phoneList.size());
        } else {
            log.info("从缓存获取手机号列表，共{}个号码", phoneList.size());
        }
        
        return phoneList;
    }

    /**
     * 设置手机号列表到缓存
     * 
     * @param phoneList 手机号列表
     */
    public void setPhoneList(List<String> phoneList) {
        if (CollUtil.isEmpty(phoneList)) {
            RedisUtils.deleteObject(PHONE_LIST_CACHE_KEY);
            log.info("清空手机号列表缓存");
        } else {
            // 设置缓存，永不过期（或设置较长的过期时间）
            RedisUtils.setCacheObject(PHONE_LIST_CACHE_KEY, phoneList);
            log.info("设置手机号列表到缓存，共{}个号码", phoneList.size());
        }
    }

    /**
     * 批量发送短信通知
     * 
     * @param phoneList 手机号列表
     * @param message 短信内容（验证码）
     * @return 发送结果统计
     */
    public SmsNotificationResult sendBatchNotification(List<String> phoneList, String message) {
        if (CollUtil.isEmpty(phoneList)) {
            log.warn("手机号列表为空，跳过发送");
            return new SmsNotificationResult(0, 0, 0);
        }

        if (StrUtil.isBlank(message)) {
            // 如果没有指定消息内容，生成随机验证码
            message = String.valueOf(RandomUtil.randomLong(100000, 999999));
        }

        log.info("开始批量发送短信通知，目标手机号数量: {}, 消息内容: {}", phoneList.size(), message);

        final String finalMessage = message;
        final String sendTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        // 使用异步方式发送短信，提高性能
        List<CompletableFuture<Boolean>> futures = new ArrayList<>();
        
        for (String phone : phoneList) {
            if (StrUtil.isBlank(phone)) {
                continue;
            }
            
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 发送短信
                    Boolean result = captchaSendService.sendCaptchaByAliyun(
                        phone, 
                        finalMessage, 
                        "3", // TTL 3分钟
                        TEMPLATE_ID
                    );
                    
                    // 记录发送结果
                    recordSendResult(phone, finalMessage, result, sendTime);
                    
                    if (result) {
                        log.info("短信发送成功: 手机号={}, 内容={}", phone, finalMessage);
                    } else {
                        log.error("短信发送失败: 手机号={}, 内容={}", phone, finalMessage);
                    }
                    
                    return result;
                } catch (Exception e) {
                    log.error("短信发送异常: 手机号={}, 内容={}", phone, finalMessage, e);
                    recordSendResult(phone, finalMessage, false, sendTime);
                    return false;
                }
            }, docExecutor);
            
            futures.add(future);
        }

        // 等待所有发送任务完成并统计结果
        int totalCount = futures.size();
        int successCount = 0;
        int failureCount = 0;

        for (CompletableFuture<Boolean> future : futures) {
            try {
                Boolean result = future.get();
                if (result) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("获取短信发送结果异常", e);
                failureCount++;
            }
        }

        SmsNotificationResult result = new SmsNotificationResult(totalCount, successCount, failureCount);
        log.info("批量短信发送完成: {}", result);
        
        return result;
    }

    /**
     * 发送定时通知短信（每天9点执行）
     * 
     * @return 发送结果
     */
    public SmsNotificationResult sendScheduledNotification() {
        log.info("执行定时短信通知任务");
        
        List<String> phoneList = getPhoneList();
        
        // 生成当天的验证码（可以根据业务需求调整）
        String message = String.valueOf(RandomUtil.randomLong(100000, 999999));
        
        return sendBatchNotification(phoneList, message);
    }

    /**
     * 手动发送通知短信
     * 
     * @param phoneList 手机号列表（可选，为空时使用默认列表）
     * @param message 短信内容（可选，为空时生成随机验证码）
     * @return 发送结果
     */
    public SmsNotificationResult sendManualNotification(List<String> phoneList, String message) {
        log.info("执行手动短信通知任务");
        
        if (CollUtil.isEmpty(phoneList)) {
            phoneList = getPhoneList();
        }
        
        return sendBatchNotification(phoneList, message);
    }

    /**
     * 记录短信发送结果
     * 
     * @param phone 手机号
     * @param message 消息内容
     * @param success 是否成功
     * @param sendTime 发送时间
     */
    private void recordSendResult(String phone, String message, Boolean success, String sendTime) {
        try {
            String recordKey = SMS_SEND_RECORD_PREFIX + phone + ":" + System.currentTimeMillis();
            String recordValue = String.format("time=%s,message=%s,success=%s", sendTime, message, success);
            
            // 记录保存7天
            RedisUtils.setCacheObject(recordKey, recordValue, Duration.ofDays(7));
        } catch (Exception e) {
            log.error("记录短信发送结果失败", e);
        }
    }

    /**
     * 短信发送结果统计
     */
    public static class SmsNotificationResult {
        private final int totalCount;
        private final int successCount;
        private final int failureCount;

        public SmsNotificationResult(int totalCount, int successCount, int failureCount) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public double getSuccessRate() {
            return totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
        }

        @Override
        public String toString() {
            return String.format("总数=%d, 成功=%d, 失败=%d, 成功率=%.2f%%", 
                totalCount, successCount, failureCount, getSuccessRate());
        }
    }
}
