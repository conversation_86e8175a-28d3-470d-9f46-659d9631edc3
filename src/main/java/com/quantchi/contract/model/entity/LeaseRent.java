package com.quantchi.contract.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("lease_rent")
@ApiModel("租金明细")
public class LeaseRent {

    @TableId
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("合同id")
    private String contractId;

    @ApiModelProperty("企业ID")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("季度")
    private Integer quarter;

    @ApiModelProperty("折扣系数")
    private BigDecimal discount;

    @ApiModelProperty("减免金额")
    private BigDecimal reduction;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("编辑后的应收金额")
    private BigDecimal receivableAmountEdit;

    @ApiModelProperty("已收金额")
    private BigDecimal receivedAmount;

    @ApiModelProperty("未收金额")
    private BigDecimal unreceivedAmount;

    @ApiModelProperty("对赌金额")
    private BigDecimal betAmount;
}
