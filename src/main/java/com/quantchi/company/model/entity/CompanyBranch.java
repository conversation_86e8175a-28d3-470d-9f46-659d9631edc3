package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_branch")
@ApiModel(value = "CompanyBranch对象", description = "")
public class CompanyBranch implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录主键")
    private String id;

    @ApiModelProperty("母公司ID")
    private String cid;

    @ApiModelProperty("母公司名称")
    private String companyName;

    @ApiModelProperty("分支机构企业ID")
    private String branchId;

    @ApiModelProperty("分支企业名称")
    private String branchName;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String COMPANY_NAME = "company_name";

    public static final String BRANCH_ID = "branch_id";

    public static final String BRANCH_NAME = "branch_name";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
