package com.quantchi.common.model;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 多维度查询条件封装对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("多维度查询条件封装对象")
public class MultidimensionalQuery extends FullSearchQuery {

    @ApiModelProperty("索引名称")
    private String index;

    @ApiModelProperty("索引类型")
    private String type;

    @ApiModelProperty("筛选条件对")
    private Map<String, List<String>> termQueries;

    @ApiModelProperty("排序方式，多个排序方式中间用;分隔，如'date:desc;year:asc'")
    private String sort;

    @ApiModelProperty("所需字段")
    private List<String> requiredList;

    @ApiModelProperty("预先筛选条件")
    private BoolQuery.Builder boolQuery;

    @ApiModelProperty("是否需要高亮")
    private Boolean isHighlight = true;

}
