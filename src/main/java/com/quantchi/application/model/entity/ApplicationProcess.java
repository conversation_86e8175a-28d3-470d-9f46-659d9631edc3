package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 审批流程
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_process")
@ApiModel(value = "ApplicationProcess对象", description = "审批流程")
public class ApplicationProcess implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private String id;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 处理人 */
    @ApiModelProperty("处理人：发起人/审批人")
    private String processor;

    /**
     * 流转给
     */
    @ApiModelProperty("流转给")
    private String transferTo;

    /** 审批流程 */
    @ApiModelProperty("审批流程")
    private String process;

    /** 审批时间 */
    @ApiModelProperty("审批时间")
    private LocalDate processTime;

    /** 审批状态：1-已同意；0-已驳回 */
    @ApiModelProperty("审批状态：1-已同意；0-已驳回")
    private Integer processStatus;

    /** 审批意见 */
    @ApiModelProperty("审批意见")
    private String processAdvice;

    /** 上传文件名称 */
    @ApiModelProperty("上传文件名称")
    private String fileName;

    /** 上传文件路径 */
    @ApiModelProperty("上传文件路径")
    private String filePath;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createUser;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private String updateUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String APPROVER = "approver";

    public static final String APPROVAL_PROCESS = "approval_process";

    public static final String APPROVAL_TIME = "approval_time";

    public static final String APPROVAL_STATUS = "approval_status";

    public static final String ADVICE = "advice";

    public static final String FILE_NAME = "file_name";

    public static final String FILE_PATH = "file_path";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CREATE_USER = "create_user";

    public static final String UPDATE_USER = "update_user";

    public static final String START_APPLY = "发起审批";

    public static final String PROCESSOR_COMMENTS = "承办人意见";

    public static final String PROCESSOR_CHANGE = "发起流转";

}
