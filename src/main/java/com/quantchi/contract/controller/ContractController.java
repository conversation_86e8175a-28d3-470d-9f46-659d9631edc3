package com.quantchi.contract.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quantchi.common.domain.InMemoryCache;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.contract.model.bo.LeaseContractBO;
import com.quantchi.contract.model.vo.LeaseContractListVO;
import com.quantchi.contract.model.vo.LeaseContractPageQuery;
import com.quantchi.contract.model.vo.LeaseContractSimpleVO;
import com.quantchi.contract.model.vo.LeaseContractVO;
import com.quantchi.contract.model.vo.OptionalListVO;
import com.quantchi.contract.service.ILeaseContractService;
import com.quantchi.sys.config.annotation.Log;
import com.quantchi.sys.model.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

@RestController
@Api(tags = "合同相关接口")
@RequestMapping("/contract")
@Slf4j
@RequiredArgsConstructor
public class ContractController {

    @Autowired
    private ILeaseContractService leaseContractService;

    @GetMapping("/optionalList")
    @ApiOperation("下拉框选项")
    public ResultInfo<OptionalListVO> optionalList() {

        return ResultConvert.success(InMemoryCache.getOptionalListVO());

    }

    @PostMapping("/saveLeaseContract")
    @ApiOperation("新增或编辑租赁合同")
    public ResultInfo<String> saveLeaseContract(@RequestBody LeaseContractBO bo) {
        try{
            String id = leaseContractService.saveOrUpdateLeaseContract(bo);
            return ResultConvert.success(id);
        } catch (Exception e) {
            return ResultConvert.error("新增或编辑租赁合同失败! " + e.getMessage());
        }
    }

    @GetMapping("/leaseContractDetail")
    @ApiOperation("查询合同详情")
    public ResultInfo<LeaseContractVO> leaseContractDetail(@RequestParam String id) {
        LeaseContractVO vo = leaseContractService.getLeaseContractDetail(id);
        return ResultConvert.success(vo);
    }

    @PostMapping("/leaseContractPage")
    @ApiOperation("分页查询合同列表")
    public ResultInfo<IPage<LeaseContractListVO>> pageLeaseContractList(@RequestBody LeaseContractPageQuery query) {
        IPage<LeaseContractListVO> result = leaseContractService.pageLeaseContractList(query);
        return ResultConvert.success(result);
    }

    @GetMapping("/deleteLeaseContract")
    @ApiOperation("删除合同")
    public ResultInfo<Boolean> deleteLeaseContract(@RequestParam String id) {
        boolean result = leaseContractService.deleteLeaseContractById(id);
        return ResultConvert.success(result);
    }

    @GetMapping("/generateLeaseContractNo")
    @ApiOperation("生成合同编号")
    public ResultInfo<String> generateLeaseContractId() {
        return ResultConvert.success(UUID.randomUUID().toString());
    }

    @PostMapping("/uploadContractScanCopy")
    @ApiOperation("上传合同扫描件")
    public ResultInfo<String> uploadContractScanCopy(@RequestBody MultipartFile file,
                                                    @RequestParam("contractId") String contractId) {
        String path = leaseContractService.uploadContractScanCopy(file, contractId);
        return ResultConvert.success(path);
    }

    @PostMapping("/uploadInvestAgreement")
    @ApiOperation("上传投资协议文件")
    public ResultInfo<String> uploadInvestAgreement(@RequestBody MultipartFile file,
                                                   @RequestParam("contractId") String contractId) {
        String path = leaseContractService.uploadInvestAgreement(file, contractId);
        return ResultConvert.success(path);
    }

    @ApiOperation(value = "文件下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    @GetMapping("/download")
    @Log(title = "文件管理-下载", businessType = BusinessType.EXPORT)
    public void download(@RequestParam @NotBlank String fileId, boolean inline, HttpServletResponse response) {
        leaseContractService.download(fileId, inline, response);
    }

    @GetMapping("/listBylesseeId")
    @ApiOperation("根据lesseeId查询合同id和contractNo")
    public ResultInfo<List<LeaseContractSimpleVO>> getContractByLesseeId(@RequestParam String lesseeId) {
        List<LeaseContractSimpleVO> result = leaseContractService.getContractIdAndNoByLesseeId(lesseeId);
        return ResultConvert.success(result);
    }

    @GetMapping("/downloadLeaseContract")
    @ApiOperation("下载租赁合同")
    public void downloadLeaseContract(@RequestParam String contractId, HttpServletResponse response) {
        try {
            leaseContractService.downloadLeaseContract(contractId, response);
        } catch (Exception e) {
            log.error("下载租赁合同异常", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入响应异常", ex);
            }
        }
    }

}
