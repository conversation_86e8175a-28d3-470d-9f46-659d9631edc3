package com.quantchi.application.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 新增企业联系人请求参数
 */
@Data
@ApiModel("新增企业联系人请求参数")
public class AddContactBO {

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String contactInfo;

    @ApiModelProperty(value = "企业ID", required = true)
    @NotBlank(message = "企业ID不能为空")
    private String companyId;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "在职状态", notes = "0-离职，1-在职，默认为1")
    private Integer jobStatus = 1;

    @ApiModelProperty("备注")
    private String remark;
}
