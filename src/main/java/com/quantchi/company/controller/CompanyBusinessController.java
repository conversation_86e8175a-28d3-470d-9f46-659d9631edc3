package com.quantchi.company.controller;

import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.company.model.vo.FinancialOverviewVO;
import com.quantchi.company.model.vo.BusinessStatisticsVO;
import com.quantchi.company.service.impl.CompanyBusinessServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/business")
@Api(tags = "企业经营分析接口")
@RequiredArgsConstructor
public class CompanyBusinessController {

    private final CompanyBusinessServiceImpl businessService;

    @GetMapping("/financialOverview")
    @ApiOperation("财务总览")
    public ResultInfo<FinancialOverviewVO> financialOverview(@RequestParam final String companyId) {
        return ResultConvert.success(businessService.financialOverview(companyId));
    }

    @GetMapping("/financialDetail")
    @ApiOperation("财务明细")
    public ResultInfo<List<FinancialOverviewVO>> financialDetail(@RequestParam final String companyId, @RequestParam final String year) {
        return ResultConvert.success(businessService.financialDetail(companyId, year));
    }

    @GetMapping("/statisticsByYearAndQuarter")
    @ApiOperation("年度与季度统计")
    public ResultInfo<BusinessStatisticsVO> statisticsByYearAndQuarter(@RequestParam String companyId) {
        return ResultConvert.success(businessService.statisticsByYearAndQuarter(companyId));
    }

}
