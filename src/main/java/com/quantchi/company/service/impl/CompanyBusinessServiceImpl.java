package com.quantchi.company.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.quantchi.company.mapper.CompanyFinanceMapper;
import com.quantchi.company.model.entity.CompanyFinance;
import com.quantchi.company.model.vo.BusinessStatisticsVO;
import com.quantchi.company.model.vo.FinancialOverviewVO;
import com.quantchi.company.service.ICompanyBusinessService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Year;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业财务相关业务实现类
 */
@RequiredArgsConstructor
@Service
public class CompanyBusinessServiceImpl implements ICompanyBusinessService {

    private final CompanyFinanceMapper companyFinanceMapper;

    @Override
    public FinancialOverviewVO financialOverview(String companyId) {
        int currentYear = Year.now().getValue();
        int startYear = currentYear - 2;
        // 查询近三年数据
        List<CompanyFinance> list = companyFinanceMapper.selectList(
                new LambdaQueryWrapper<CompanyFinance>()
                        .eq(CompanyFinance::getCid, companyId)
                        .ge(CompanyFinance::getYear, startYear)
        );
        if (list.isEmpty()) {
            return null;
        }
        // 按年份分组
        Map<Integer, List<CompanyFinance>> yearMap = list.stream().collect(Collectors.groupingBy(CompanyFinance::getYear));
        // 最新年份
        int maxYear = yearMap.keySet().stream().max(Integer::compareTo).orElse(currentYear);
        // 最新年份与上一年数据
        List<CompanyFinance> thisYearList = yearMap.getOrDefault(maxYear, Collections.emptyList());
        List<CompanyFinance> lastYearList = yearMap.getOrDefault(maxYear-1, Collections.emptyList());
        // 统计最新年度营业收入与纳税
        double revenue = thisYearList.stream().map(cf -> cf.getGrossIncome() == null ? 0.0 : cf.getGrossIncome().doubleValue()).reduce(0.0, Double::sum);
        double tax = thisYearList.stream().map(cf -> cf.getTaxScale() == null ? 0.0 : cf.getTaxScale().doubleValue()).reduce(0.0, Double::sum);
        // 统计上一年度营业收入与纳税
        double revenueLast = lastYearList.stream().map(cf -> cf.getGrossIncome() == null ? 0.0 : cf.getGrossIncome().doubleValue()).reduce(0.0, Double::sum);
        double taxLast = lastYearList.stream().map(cf -> cf.getTaxScale() == null ? 0.0 : cf.getTaxScale().doubleValue()).reduce(0.0, Double::sum);
        // 计算同比增长（如无上一年数据则为--）
        String revenueTrend = revenueLast == 0 ? "--" : String.format("%.2f%%", (revenue-revenueLast)/revenueLast*100);
        String taxTrend = taxLast == 0 ? "--" : String.format("%.2f%%", (tax-taxLast)/taxLast*100);
        FinancialOverviewVO vo = new FinancialOverviewVO();
        vo.setYear(String.valueOf(maxYear));
        vo.setRevenue(String.format("%.2f", revenue));
        vo.setRevenueTrend(revenueTrend);
        vo.setTax(String.format("%.2f", tax));
        vo.setTaxTrend(taxTrend);
        return vo;
    }

    @Override
    public List<FinancialOverviewVO> financialDetail(String companyId, String year) {
        int yearInt;
        try {
            yearInt = Integer.parseInt(year);
        } catch (Exception e) {
            return Collections.emptyList();
        }
        // 查询该企业指定年份的所有财务数据
        List<CompanyFinance> list = companyFinanceMapper.selectList(
                new LambdaQueryWrapper<CompanyFinance>()
                        .eq(CompanyFinance::getCid, companyId)
                        .eq(CompanyFinance::getYear, yearInt)
        );
        // 按季度分组
        Map<Integer, List<CompanyFinance>> quarterMap = list.stream()
                .filter(cf -> cf.getQuarter() != null)
                .collect(Collectors.groupingBy(CompanyFinance::getQuarter));
        List<FinancialOverviewVO> result = new ArrayList<>();
        
        for (Integer q : quarterMap.keySet()) {
            List<CompanyFinance> qList = quarterMap.get(q);
            double revenue = qList.stream().map(cf -> cf.getGrossIncome() == null ? 0.0 : cf.getGrossIncome().doubleValue()).reduce(0.0, Double::sum);
            double tax = qList.stream().map(cf -> cf.getTaxScale() == null ? 0.0 : cf.getTaxScale().doubleValue()).reduce(0.0, Double::sum);
            // 查询上一年同季度数据
            List<CompanyFinance> lastYearQuarterList = companyFinanceMapper.selectList(
                new LambdaQueryWrapper<CompanyFinance>()
                    .eq(CompanyFinance::getCid, companyId)
                    .eq(CompanyFinance::getYear, yearInt - 1)
                    .eq(CompanyFinance::getQuarter, q)
            );
            double revenueLast = lastYearQuarterList.stream().map(cf -> cf.getGrossIncome() == null ? 0.0 : cf.getGrossIncome().doubleValue()).reduce(0.0, Double::sum);
            double taxLast = lastYearQuarterList.stream().map(cf -> cf.getTaxScale() == null ? 0.0 : cf.getTaxScale().doubleValue()).reduce(0.0, Double::sum);
            String revenueTrend = revenueLast == 0 ? "--" : String.format("%.2f%%", (revenue-revenueLast)/revenueLast*100);
            String taxTrend = taxLast == 0 ? "--" : String.format("%.2f%%", (tax-taxLast)/taxLast*100);
            
            FinancialOverviewVO vo = new FinancialOverviewVO();
            vo.setQuarter(" Q" + q);
            vo.setYear(String.valueOf(yearInt));
            vo.setRevenue(String.format("%.2f", revenue));
            vo.setRevenueTrend(revenueTrend);
            vo.setTax(String.format("%.2f", tax));
            vo.setTaxTrend(taxTrend);
            result.add(vo);
        }
        // 按季度排序
        result.sort(Comparator.comparing(FinancialOverviewVO::getQuarter));
        return result;
    }

    @Override
    public BusinessStatisticsVO statisticsByYearAndQuarter(String companyId) {
        int currentYear = Year.now().getValue();
        int startYear = currentYear - 2;
        // 查询近三年数据
        List<CompanyFinance> financeList = companyFinanceMapper.selectList(
                new LambdaQueryWrapper<CompanyFinance>()
                        .eq(CompanyFinance::getCid, companyId)
                        .ge(CompanyFinance::getYear, startYear)
        );
        // 年度统计分组
        Map<Integer, List<CompanyFinance>> annualGroup = financeList.stream()
                .collect(Collectors.groupingBy(CompanyFinance::getYear));
        List<BusinessStatisticsVO.AnnualStat> annualVOList = new ArrayList<>();
        for (Integer year : annualGroup.keySet()) {
            List<CompanyFinance> yearList = annualGroup.get(year);
            double revenue = yearList.stream().map(cf -> cf.getGrossIncome() == null ? 0.0 : cf.getGrossIncome().doubleValue()).reduce(0.0, Double::sum);
            double tax = yearList.stream().map(cf -> cf.getTaxScale() == null ? 0.0 : cf.getTaxScale().doubleValue()).reduce(0.0, Double::sum);
            BusinessStatisticsVO.AnnualStat stat = new BusinessStatisticsVO.AnnualStat();
            stat.setYear(year);
            stat.setRevenue(revenue);
            stat.setTax(tax);
            annualVOList.add(stat);
        }
        annualVOList.sort(Comparator.comparing(BusinessStatisticsVO.AnnualStat::getYear).reversed());
        // 季度统计分组
        Map<String, List<CompanyFinance>> quarterGroup = financeList.stream()
                .filter(cf -> cf.getQuarter() != null)
                .collect(Collectors.groupingBy(cf -> cf.getYear() + "_" + cf.getQuarter()));
        List<BusinessStatisticsVO.QuarterStat> quarterVOList = new ArrayList<>();
        for (String key : quarterGroup.keySet()) {
            List<CompanyFinance> qList = quarterGroup.get(key);
            String[] parts = key.split("_");
            Integer year = Integer.valueOf(parts[0]);
            Integer quarter = Integer.valueOf(parts[1]);
            double revenue = qList.stream().map(cf -> cf.getGrossIncome() == null ? 0.0 : cf.getGrossIncome().doubleValue()).reduce(0.0, Double::sum);
            double tax = qList.stream().map(cf -> cf.getTaxScale() == null ? 0.0 : cf.getTaxScale().doubleValue()).reduce(0.0, Double::sum);
            BusinessStatisticsVO.QuarterStat stat = new BusinessStatisticsVO.QuarterStat();
            stat.setYear(year);
            stat.setQuarter(quarter);
            stat.setRevenue(revenue);
            stat.setTax(tax);
            quarterVOList.add(stat);
        }
        quarterVOList.sort(Comparator.comparing(BusinessStatisticsVO.QuarterStat::getYear).reversed()
                .thenComparing(BusinessStatisticsVO.QuarterStat::getQuarter).reversed());
        BusinessStatisticsVO vo = new BusinessStatisticsVO();
        vo.setAnnual(annualVOList);
        vo.setQuarter(quarterVOList);
        return vo;
    }
}
