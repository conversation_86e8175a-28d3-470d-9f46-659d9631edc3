package com.quantchi.server.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel(description = "园区动态")
public class ParkDynamicsVO {
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "文章名称")
    private String articleName;


    @ApiModelProperty(value = "缩略图")
    private String image;

    @ApiModelProperty(value = "链接地址")
    private String url;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String publishTime;
}
