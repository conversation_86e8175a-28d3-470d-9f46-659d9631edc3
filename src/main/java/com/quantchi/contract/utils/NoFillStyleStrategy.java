package com.quantchi.contract.utils;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

/**
 * 移除表头灰色背景的策略
 */
public class NoFillStyleStrategy implements SheetWriteHandler {

    private final int quarterCount;

    public NoFillStyleStrategy(int quarterCount) {
        this.quarterCount = quarterCount;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // Sheet创建前执行
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        
        // 设置列宽
        setColumnWidths(sheet);
        
        // 等待表头创建完成后再设置样式
        // 这里我们需要在数据写入后处理
    }

    private void setColumnWidths(Sheet sheet) {
        // 设置列宽（单位：字符宽度 * 256）
        sheet.setColumnWidth(0, 20 * 256); // 合同编号
        sheet.setColumnWidth(1, 25 * 256); // 承租方
        sheet.setColumnWidth(2, 30 * 256); // 房号/楼栋/园区
        sheet.setColumnWidth(3, 30 * 256); // 租金标准
        
        // 动态季度列
        int startCol = 4;
        for (int i = 0; i < quarterCount; i++) {
            int baseCol = startCol + i * 5;
            sheet.setColumnWidth(baseCol, 12 * 256);     // 折扣系数
            sheet.setColumnWidth(baseCol + 1, 20 * 256); // 减免金额
            sheet.setColumnWidth(baseCol + 2, 20 * 256); // 应收金额
            sheet.setColumnWidth(baseCol + 3, 20 * 256); // 已收金额
            sheet.setColumnWidth(baseCol + 4, 20 * 256); // 未收金额
        }
    }
}
