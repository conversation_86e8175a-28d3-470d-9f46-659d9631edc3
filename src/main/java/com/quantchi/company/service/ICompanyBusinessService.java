package com.quantchi.company.service;

import com.quantchi.company.model.vo.BusinessStatisticsVO;
import com.quantchi.company.model.vo.FinancialOverviewVO;

import java.util.List;

public interface ICompanyBusinessService {

    /**
     * 获取企业财务概况
     */
    FinancialOverviewVO financialOverview(String companyId);

    /**
     * 获取企业财务明细
     */
    List<FinancialOverviewVO> financialDetail(String companyId, String year);

    /**
     * 按年度、季度分别展示营业收入总额和纳税收入总额，展示最近三年的数据
     */
    BusinessStatisticsVO statisticsByYearAndQuarter(String companyId);

}
