package com.quantchi.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quantchi.server.model.entity.RecentDeclaration;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.server.model.vo.ParkDynamicsVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface IRecentDeclarationService extends IService<RecentDeclaration> {

    /**
     * 分页查询近期申报
     *
     * @param pageNum 页码
     * @param pageSize 每页条数
     * @return 分页结果
     */
    IPage<RecentDeclaration> page(int pageNum, int pageSize);

    /**
     * 分页查询园区动态
     */
    IPage<ParkDynamicsVO> parkDynamics(int pageNum, int pageSize);

}
