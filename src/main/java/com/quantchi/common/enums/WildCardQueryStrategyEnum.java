package com.quantchi.common.enums;

public enum WildCardQueryStrategyEnum {

    LEFT_LINK_ALL_QUERY(1,"*keyword","左全模糊查询"),
    RIGHT_LINK_ALL_QUERY(2,"keyword*","右全模糊查询"),
    ALL_LINK_ALL_QUERY(3,"*keyword*","全模糊查询"),
    LEFT_LINK_ONE_QUERY(4,"?keyword","左单字符模糊查询"),
    RIGHT_LINK_ONE_QUERY(5,"keyword?","右单字符全模糊查询"),
    ALL_LINK_ONE_QUERY(6,"?keyword?","全单字符模糊查询"),
    RIGHT_LINK_ALL_QUERY_WITH_ONE(7,"keyword?*","右边至少一个单词全模糊查询"),

    //TODO 按需补充

    ;

    private final Integer type;

    private final String strategy;

    private final String desc;

    public Integer getType() {
        return type;
    }

    public String getStrategy() {
        return strategy;
    }

    public String getDesc() {
        return desc;
    }

    WildCardQueryStrategyEnum(final Integer type, final String strategy, final String desc) {
        this.type = type;
        this.strategy = strategy;
        this.desc = desc;
    }

    public static String getStrategyKeyword(final String keyword, final Integer type){
        for (final WildCardQueryStrategyEnum wild : WildCardQueryStrategyEnum.values()){
            if (wild.getType().equals(type)){
                return wild.getStrategy().replace("keyword", keyword);
            }
        }
        return keyword;
    }
}
