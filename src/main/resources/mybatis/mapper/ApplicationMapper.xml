<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.application.mapper.ApplicationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.quantchi.application.model.entity.Application">
        <id column="id" property="id" />
        <result column="applicant_id" property="applicantId" />
        <result column="apply_code" property="applyCode" />
        <result column="apply_type" property="applyType" />
        <result column="apply_time" property="applyTime" />
        <result column="finish_time" property="finishTime" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 审批记录结果映射 -->
    <resultMap id="AuditRecordResultMap" type="com.quantchi.application.model.vo.AuditRecordVO">
        <id column="application_id" property="applicationId" />
        <result column="applicant_name" property="applicantName" />
        <result column="apply_type" property="applyType" />
        <result column="apply_time" property="applyTime" />
        <result column="finish_time" property="finishTime" />
        <result column="status" property="status" />
        <result column="applicant_id" property="applicantId" />
    </resultMap>

    <!-- 分页查询审批列表，联表查询申请单位 -->
    <select id="pageAuditRecordsWithJoin" resultMap="AuditRecordResultMap">
        <choose>
            <!-- 根据applyTypes参数的值构建查询 -->
            <when test="applyTypes != null and applyTypes.size() > 0">
                SELECT 
                    application_id, 
                    applicant_name,
                    applicant_id,
                    apply_type, 
                    apply_time, 
                    finish_time, 
                    status 
                FROM (
                    <!-- 多个申请类型的情况，使用UNION ALL -->
                    <if test="applyTypes.contains(1)">
                        SELECT
                            a.id AS application_id,
                            a.applicant_id,
                            c.name AS applicant_name,
                            a.apply_type,
                            a.apply_time,
                            a.finish_time,
                            a.status
                        FROM
                            application a
                        LEFT JOIN application_move_in mi ON a.id = mi.application_id
                        LEFT JOIN company c ON mi.company_id = c.id
                        WHERE a.apply_type = 1
                        <if test="statuses != null and statuses.size() > 0">
                            AND a.status IN
                            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                                #{status}
                            </foreach>
                        </if>
                        <if test="keyword != null and keyword != ''">
                            AND c.name LIKE CONCAT('%', #{keyword}, '%')
                        </if>
                        <if test="approver != null">
                            AND a.approver = #{approver}
                        </if>
                    </if>
                    
                    <!-- 动态添加UNION ALL关键字 -->
                    <if test="applyTypes.contains(1) and applyTypes.contains(2)">
                        UNION ALL
                    </if>
                    
                    <if test="applyTypes.contains(2)">
                        SELECT
                            a.id AS application_id,
                            a.applicant_id,
                            c.name AS applicant_name,
                            a.apply_type,
                            a.apply_time,
                            a.finish_time,
                            a.status
                        FROM
                            application a
                        LEFT JOIN application_move_out mo ON a.id = mo.application_id
                        LEFT JOIN company c ON mo.company_id = c.id
                        WHERE a.apply_type = 2
                        <if test="statuses != null and statuses.size() > 0">
                            AND a.status IN
                            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                                #{status}
                            </foreach>
                        </if>
                        <if test="keyword != null and keyword != ''">
                            AND c.name LIKE CONCAT('%', #{keyword}, '%')
                        </if>
                        <if test="approver != null">
                            AND a.approver = #{approver}
                        </if>
                    </if>
                    
                    <!-- 动态添加UNION ALL关键字 -->
                    <if test="(applyTypes.contains(1) or applyTypes.contains(2)) and applyTypes.contains(3)">
                        UNION ALL
                    </if>
                    
                    <if test="applyTypes.contains(3)">
                        SELECT
                            a.id AS application_id,
                            a.applicant_id,
                            c.name AS applicant_name,
                            a.apply_type,
                            a.apply_time,
                            a.finish_time,
                            a.status
                        FROM
                            application a
                        LEFT JOIN application_decoration d ON a.id = d.application_id
                        LEFT JOIN company c ON d.company_id = c.id
                        WHERE a.apply_type = 3
                        <if test="statuses != null and statuses.size() > 0">
                            AND a.status IN
                            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                                #{status}
                            </foreach>
                        </if>
                        <if test="keyword != null and keyword != ''">
                            AND c.name LIKE CONCAT('%', #{keyword}, '%')
                        </if>
                        <if test="approver != null">
                            AND a.approver = #{approver}
                        </if>
                    </if>
                ) result
                ORDER BY
                    apply_time DESC
            </when>
            
            <!-- 未指定申请类型的情况，查询所有类型 -->
            <otherwise>
                SELECT
                    a.id AS application_id,
                    a.applicant_id,
                    IFNULL(c.name, '') AS applicant_name,
                    a.apply_type,
                    a.apply_time,
                    a.finish_time,
                    a.status
                FROM
                    application a
                LEFT JOIN (
                    SELECT mi.application_id, c.name, a.apply_type
                    FROM application_move_in mi
                    JOIN application a ON mi.application_id = a.id AND a.apply_type = 1
                    LEFT JOIN company c ON mi.company_id = c.id
                    
                    UNION ALL
                    
                    SELECT mo.application_id, c.name, a.apply_type
                    FROM application_move_out mo
                    JOIN application a ON mo.application_id = a.id AND a.apply_type = 2
                    LEFT JOIN company c ON mo.company_id = c.id
                    
                    UNION ALL
                    
                    SELECT d.application_id, c.name, a.apply_type
                    FROM application_decoration d
                    JOIN application a ON d.application_id = a.id AND a.apply_type = 3
                    LEFT JOIN company c ON d.company_id = c.id
                ) c ON a.id = c.application_id
                WHERE 1=1
                <if test="statuses != null and statuses.size() > 0">
                    AND a.status IN
                    <foreach collection="statuses" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
                <if test="keyword != null and keyword != ''">
                    AND c.name LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="approver != null">
                    AND a.approver = #{approver}
                </if>
                ORDER BY
                    a.apply_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询审批记录列表（不分页，用于导出） -->
    <select id="listAuditRecordsWithJoin" resultMap="AuditRecordResultMap">
        <choose>
            <!-- 根据applyTypes参数的值构建查询 -->
            <when test="applyTypes != null and applyTypes.size() > 0">
                SELECT
                    application_id,
                    applicant_name,
                    applicant_id,
                    apply_type,
                    apply_time,
                    finish_time,
                    status
                FROM (
                    <!-- 多个申请类型的情况，使用UNION ALL -->
                    <if test="applyTypes.contains(1)">
                        SELECT
                            a.id AS application_id,
                            a.applicant_id,
                            c.name AS applicant_name,
                            a.apply_type,
                            a.apply_time,
                            a.finish_time,
                            a.status
                        FROM
                            application a
                        LEFT JOIN application_move_in mi ON a.id = mi.application_id
                        LEFT JOIN company c ON mi.company_id = c.id
                        WHERE a.apply_type = 1
                        <if test="statuses != null and statuses.size() > 0">
                            AND a.status IN
                            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                                #{status}
                            </foreach>
                        </if>
                        <if test="keyword != null and keyword != ''">
                            AND c.name LIKE CONCAT('%', #{keyword}, '%')
                        </if>
                        <if test="approver != null">
                            AND a.approver = #{approver}
                        </if>
                    </if>

                    <!-- 动态添加UNION ALL关键字 -->
                    <if test="applyTypes.contains(1) and applyTypes.contains(2)">
                        UNION ALL
                    </if>

                    <if test="applyTypes.contains(2)">
                        SELECT
                            a.id AS application_id,
                            a.applicant_id,
                            c.name AS applicant_name,
                            a.apply_type,
                            a.apply_time,
                            a.finish_time,
                            a.status
                        FROM
                            application a
                        LEFT JOIN application_move_out mo ON a.id = mo.application_id
                        LEFT JOIN company c ON mo.company_id = c.id
                        WHERE a.apply_type = 2
                        <if test="statuses != null and statuses.size() > 0">
                            AND a.status IN
                            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                                #{status}
                            </foreach>
                        </if>
                        <if test="keyword != null and keyword != ''">
                            AND c.name LIKE CONCAT('%', #{keyword}, '%')
                        </if>
                        <if test="approver != null">
                            AND a.approver = #{approver}
                        </if>
                    </if>

                    <!-- 动态添加UNION ALL关键字 -->
                    <if test="(applyTypes.contains(1) or applyTypes.contains(2)) and applyTypes.contains(3)">
                        UNION ALL
                    </if>

                    <if test="applyTypes.contains(3)">
                        SELECT
                            a.id AS application_id,
                            a.applicant_id,
                            c.name AS applicant_name,
                            a.apply_type,
                            a.apply_time,
                            a.finish_time,
                            a.status
                        FROM
                            application a
                        LEFT JOIN application_decoration d ON a.id = d.application_id
                        LEFT JOIN company c ON d.company_id = c.id
                        WHERE a.apply_type = 3
                        <if test="statuses != null and statuses.size() > 0">
                            AND a.status IN
                            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                                #{status}
                            </foreach>
                        </if>
                        <if test="keyword != null and keyword != ''">
                            AND c.name LIKE CONCAT('%', #{keyword}, '%')
                        </if>
                        <if test="approver != null">
                            AND a.approver = #{approver}
                        </if>
                    </if>
                ) result
                ORDER BY
                    apply_time DESC
            </when>

            <!-- 未指定申请类型的情况，查询所有类型 -->
            <otherwise>
                SELECT
                    a.id AS application_id,
                    a.applicant_id,
                    IFNULL(c.name, '') AS applicant_name,
                    a.apply_type,
                    a.apply_time,
                    a.finish_time,
                    a.status
                FROM
                    application a
                LEFT JOIN (
                    SELECT mi.application_id, c.name, a.apply_type
                    FROM application_move_in mi
                    JOIN application a ON mi.application_id = a.id AND a.apply_type = 1
                    LEFT JOIN company c ON mi.company_id = c.id

                    UNION ALL

                    SELECT mo.application_id, c.name, a.apply_type
                    FROM application_move_out mo
                    JOIN application a ON mo.application_id = a.id AND a.apply_type = 2
                    LEFT JOIN company c ON mo.company_id = c.id

                    UNION ALL

                    SELECT d.application_id, c.name, a.apply_type
                    FROM application_decoration d
                    JOIN application a ON d.application_id = a.id AND a.apply_type = 3
                    LEFT JOIN company c ON d.company_id = c.id
                ) c ON a.id = c.application_id
                WHERE 1=1
                <if test="statuses != null and statuses.size() > 0">
                    AND a.status IN
                    <foreach collection="statuses" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
                <if test="keyword != null and keyword != ''">
                    AND c.name LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="approver != null">
                    AND a.approver = #{approver}
                </if>
                ORDER BY
                    a.apply_time DESC
            </otherwise>
        </choose>
    </select>
</mapper>
