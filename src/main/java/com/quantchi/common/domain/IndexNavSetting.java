package com.quantchi.common.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 索引导航设置
 */
@Data
public class IndexNavSetting {

    @ApiModelProperty("对应字段")
    protected String field;

    @ApiModelProperty("对应字段的关键词查询字段")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected String fieldKeyword;

    /**
     * 导航标签
     */
    @ApiModelProperty("导航标签")
    protected String fieldName;

    /**
     * 导航值
     */
    @ApiModelProperty("导航值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> scope;


    /**
     * 范围（只支持数字类型，默认为双精度）
     */
    @ApiModelProperty("范围（只支持数字类型，默认为双精度）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> range;

    /**
     * 字段值别名，需要与scope一一对应
     */
    @ApiModelProperty("字段值别名，需要与scope一一对应")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> alias;

    @ApiModelProperty("匹配类型，精确或模糊")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> matchTypes;
}
