package com.quantchi.application.model.enums;

import com.quantchi.application.model.entity.ApplicationFile;

/**
 * 文件类型枚举
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
public enum FileTypeEnum {
    
    DECORATE_APPLY(ApplicationFile.DECORATE_APPLY, "01_装修申请说明"),
    BUSINESS_LICENSE(ApplicationFile.BUSINESS_LICENSE, "02_营业执照"),
    LEASE_CONTRACT(ApplicationFile.LEASE_CONTRACT, "03_有效期内租赁合同"),
    DECORATE_UNDERTAKING(ApplicationFile.DECORATE_UNDERTAKING, "04_装修承诺书"),
    DESIGN_DRAWING(ApplicationFile.DESIGN_DRAWING, "05_设计院盖章的设计图纸"),
    DECORATE_COMPANY(ApplicationFile.DECORATE_COMPANY, "06_装修公司营业执照及资质证明等"),
    AUDIT(ApplicationFile.AUDIT, "审批材料"),
    OTHER(0, "07_其他文件");

    private final Integer code;
    private final String folderName;

    FileTypeEnum(final Integer code, final String folderName) {
        this.code = code;
        this.folderName = folderName;
    }

    public Integer getCode() {
        return code;
    }

    public String getFolderName() {
        return folderName;
    }

    /**
     * 根据文件类型代码获取文件夹名称
     *
     * @param code 文件类型代码
     * @return 文件夹名称
     */
    public static String getFolderNameByCode(final Integer code) {
        for (FileTypeEnum fileType : FileTypeEnum.values()) {
            if (fileType.getCode().equals(code)) {
                return fileType.getFolderName();
            }
        }
        return OTHER.getFolderName();
    }
}
