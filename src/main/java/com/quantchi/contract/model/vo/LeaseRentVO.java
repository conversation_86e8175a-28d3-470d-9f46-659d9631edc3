package com.quantchi.contract.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("租金明细展示")
public class LeaseRentVO {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("季度")
    private Integer quarter;

    @ApiModelProperty("折扣系数")
    private BigDecimal discount;

    @ApiModelProperty("减免金额")
    private BigDecimal reduction;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("已收金额")
    private BigDecimal receivedAmount;

    @ApiModelProperty("未收金额")
    private BigDecimal unreceivedAmount;

    @ApiModelProperty("对赌金额")
    private BigDecimal betAmount;
}
