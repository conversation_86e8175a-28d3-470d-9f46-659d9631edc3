package com.quantchi.application.model.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum ApplicationStatus {
    PROCESS_GRANTED(1, "审批通过"),
    PROCESSING(2, "处理中"),
    PROCESS_REFUSE(3, "已驳回"),
    PROCESS_UNDO(4, "已撤销"),
    ;

    private final Integer code;
    private final String info;

    ApplicationStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static String getInfoByCode(Integer code) {
        for (ApplicationStatus status : ApplicationStatus.values()) {
            if (status.getCode().equals(code)) {
                return status.getInfo();
            }
        }
        return "";
    }
}
