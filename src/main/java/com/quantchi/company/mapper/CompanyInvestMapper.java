package com.quantchi.company.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quantchi.company.model.bo.CompanyFundIndustryInvestBO;
import com.quantchi.company.model.entity.CompanyInvest;
import com.quantchi.company.model.vo.NameCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface CompanyInvestMapper extends BaseMapper<CompanyInvest> {

    /**
     * 根据企业id获取所有（直接、间接）投资记录  type 根据被投资企业0.不去重 1.去重
     *
     * @param id
     * @param provinceId
     * @param cityId
     * @param type
     * @return
     */
    List<CompanyInvest> getAllInvestByCid(@Param("id") String id, @Param("provinceId") String provinceId,
                                          @Param("cityId") String cityId, @Param("type") Integer type);


    /**
     * 根据企业id获取所有直接投资记录（0.不去重  1.根据被投资企业去重）
     *
     * @param id
     * @param provinceId
     * @param cityId
     * @param type
     * @return
     */
    List<CompanyInvest> getDirectInvestByCid(@Param("id") String id,
                                             @Param("provinceId") String provinceId,
                                             @Param("cityId") String cityId,
                                             @Param("type") Integer type);

    /**
     * 根据企业id获取所有（直接、间接）投资记录及行业分类信息 type 根据被投资企业0.不去重 1.去重
     *
     * @param id
     * @return
     */
    List<CompanyFundIndustryInvestBO> getAllInvestForIndustryByCid(@Param("id") String id,
                                                                   @Param("type") Integer type);

    /**
     * 根据企业id获取企业大类
     *
     * @param id
     * @return
     */
    String getBigCategoryByCid(@Param("id") String id);

    /**
     * 根据id列表获取企业注册资本
     *
     * @param idList
     * @return
     */
    List<Map<String, Object>> getRegistCapiValueListByCidList(@Param("idList") List<String> idList);

    /**
     * 根据城市进行分组
     *
     * @param companyId
     * @return
     */
    List<NameCountVO> getMaxCityCodeRecord(@Param("companyId") String companyId);

    /**
     * 根据行业进行分组
     *
     * @param companyId
     * @return
     */
    List<NameCountVO> getMaxIndustryRecord(@Param("companyId") String companyId);

}
