package com.quantchi.overview.service;

import com.quantchi.overview.model.bo.ExpiringContractQueryBO;
import com.quantchi.overview.model.bo.NewCompanyQueryBO;
import com.quantchi.overview.model.vo.ExpiringContractPageVO;
import com.quantchi.overview.model.vo.MoveInTrendVO;
import com.quantchi.overview.model.vo.NewCompanyPageVO;
import com.quantchi.overview.model.vo.OverviewVO;
import java.util.List;

/**
 * 园区总览 服务接口
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface IOverviewService {
    
    /**
     * 获取园区概况数据
     *
     * @param parkId 园区ID，可为空
     * @return 园区概况数据
     */
    OverviewVO getOverviewData(String parkId);
    
    /**
     * 分页查询新入驻企业列表
     *
     * @param queryBO 查询参数
     * @return 分页结果
     */
    NewCompanyPageVO pageNewCompanies(NewCompanyQueryBO queryBO);
    
    /**
     * 分页查询合同即将到期企业列表
     *
     * @param queryBO 查询参数
     * @return 分页结果
     */
    ExpiringContractPageVO pageExpiringContracts(ExpiringContractQueryBO queryBO);
    
    /**
     * 查询当前年份企业入驻增长趋势
     *
     * @param parkId 园区ID，可为空
     * @return 每月入驻企业数量列表
     */
    List<MoveInTrendVO> queryMoveInTrend(String parkId);
}
