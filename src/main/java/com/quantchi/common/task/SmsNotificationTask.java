package com.quantchi.common.task;

import com.quantchi.common.service.impl.SmsNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 短信通知定时任务
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "sms.notification.enabled", havingValue = "true", matchIfMissing = true)
@org.springframework.context.annotation.Profile("testk8s")
public class SmsNotificationTask {

    @Autowired
    private SmsNotificationService smsNotificationService;

    /**
     * Bean初始化后执行，记录定时任务启动信息
     */
    @PostConstruct
    public void init() {
        log.info("=== 短信通知定时任务已启动 ===");
        log.info("当前环境: testk8s");
        log.info("定时任务配置: 每天上午9点执行");
        log.info("模板ID: SMS_488635181");
        log.info("================================");
    }

    /**
     * 每天上午9点执行短信通知任务
     * cron表达式: 秒 分 时 日 月 周
     * 0 0 9 * * ? 表示每天上午9点0分0秒执行
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendDailyNotification() {
        log.info("开始执行每日短信通知定时任务");
        
        try {
            SmsNotificationService.SmsNotificationResult result = smsNotificationService.sendScheduledNotification();
            
            if (result.getSuccessCount() > 0) {
                log.info("每日短信通知任务执行成功: {}", result);
            } else {
                log.warn("每日短信通知任务执行完成，但没有成功发送任何短信: {}", result);
            }
            
        } catch (Exception e) {
            log.error("每日短信通知任务执行失败", e);
        }
    }


}
