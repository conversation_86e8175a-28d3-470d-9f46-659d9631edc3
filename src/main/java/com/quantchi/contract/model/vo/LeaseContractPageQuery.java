package com.quantchi.contract.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("合同分页查询参数")
public class LeaseContractPageQuery {
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页数量")
    private Integer pageSize = 10;

    @ApiModelProperty("关键字（合同编号/承租方名称）")
    private String keyword;

    @ApiModelProperty("园区ID")
    private String parkId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("租赁开始时间-起")
    private LocalDate leaseStartTimeBegin;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("租赁开始时间-止")
    private LocalDate leaseStartTimeEnd;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间-起")
    private LocalDate createTimeBegin;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间-止")
    private LocalDate createTimeEnd;
}
