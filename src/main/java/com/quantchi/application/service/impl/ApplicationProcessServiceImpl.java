package com.quantchi.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.quantchi.application.model.entity.ApplicationFile;
import com.quantchi.application.model.entity.ApplicationProcess;
import com.quantchi.application.mapper.ApplicationProcessMapper;
import com.quantchi.application.model.enums.ProcessStatus;
import com.quantchi.application.model.vo.ProcessVO;
import com.quantchi.application.service.IApplicationProcessService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.common.model.FileInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.quantchi.common.model.FileInfo.getFileInfos;

/**
 * <p>
 * 审批流程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
public class ApplicationProcessServiceImpl extends ServiceImpl<ApplicationProcessMapper, ApplicationProcess> implements IApplicationProcessService {

    @Value("${self.address}")
    private String downloadAddress;

    @Override
    public List<ProcessVO> getProcessVOList(String applicationId, List<ApplicationFile> fileList) {
        List<ProcessVO> processVOList = new ArrayList<>();
        List<ApplicationProcess> processList = this.list(
                new LambdaQueryWrapper<ApplicationProcess>()
                        .eq(ApplicationProcess::getApplicationId, applicationId)
                        .orderByAsc(ApplicationProcess::getProcessTime)
        );

        if (CollectionUtils.isNotEmpty(processList)) {
            processVOList = processList.stream().map(process -> {
                ProcessVO processVO = new ProcessVO();
                BeanUtils.copyProperties(process, processVO);
                // 设置审批状态说明
                if (process.getProcessStatus() != null) {
                    processVO.setProcessStatusCN(ProcessStatus.getInfoByCode(process.getProcessStatus()));
                }
                List<FileInfo> fileInfos = getFileInfos(process.getId(), fileList, ApplicationFile.AUDIT, downloadAddress);
                if (CollectionUtils.isNotEmpty(fileInfos)) {
                    processVO.setAuditFiles(fileInfos);
                }
                return processVO;
            }).collect(Collectors.toList());
        }
        return processVOList;
    }
}
