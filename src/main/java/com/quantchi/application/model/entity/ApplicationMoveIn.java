package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 申请基本内容表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_move_in")
@ApiModel(value = "ApplicationMoveIn对象", description = "申请基本内容表")
public class ApplicationMoveIn implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("入驻类型: 0-新注册; 1-迁址")
    private Integer moveInType;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 申请ID
     */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 公司ID */
    @ApiModelProperty("公司ID")
    private String companyId;

    /**
     * 实施项目名称
     */
    @ApiModelProperty("实施项目名称")
    private String projectName;

    /**
     * 产业领域ID
     */
    @ApiModelProperty("产业领域ID")
    private String industryFieldId;

    /**
     * 所属类型ID
     */
    @ApiModelProperty("所属类型ID")
    private Integer typeId;

    /**
     * 成立时间
     */
    @ApiModelProperty("成立时间")
    private LocalDate establishmentDate;

    /**
     * 是否实际入驻
     */
    @ApiModelProperty("是否实际入驻: 0-否, 1-是")
    private Byte registration;

    /**
     * 目前地点
     */
    @ApiModelProperty("实际办公地址")
    private String actualLocation;

    /**
     * 项目来源
     */
    @ApiModelProperty("项目来源")
    private String projectSource;


    /**
     * 是否有经营异常（0否，1是）
     */
    @ApiModelProperty("是否有经营异常（0否，1是）")
    private Byte isAbnormalOperation;

    /**
     * 是否科技型中小企业（0否，1是）
     */
    @ApiModelProperty("是否科技型中小企业（0否，1是）")
    private Byte isTechEnterprise;

    /**
     * 是否高新产品（0否，1是）
     */
    @ApiModelProperty("是否高新产品（0否，1是）")
    private Byte isHighTechProduct;

    /**
     * 是否高新企业（0否，1是）
     */
    @ApiModelProperty("是否高新企业（0否，1是）")
    private Byte isHighTechEnterprise;

    /**
     * 上年度末注册资本
     */
    @ApiModelProperty("上年度末注册资本")
    private String registeredCapitalLastYear;

    /**
     * 上年度末从业人员
     */
    @ApiModelProperty("上年度末从业人员")
    private String employeesLastYear;

    /**
     * 上年度末知识产权
     */
    @ApiModelProperty("上年度末知识产权")
    private String intellectualPropertyLastYear;

    /**
     * 上年度实际总收入
     */
    @ApiModelProperty("上年度实际总收入")
    private String totalRevenueLastYear;

    /**
     * 上年度实际净利润
     */
    @ApiModelProperty("上年度实际净利润")
    private String netProfitLastYear;

    /**
     * 上年度实际纳税额
     */
    @ApiModelProperty("上年度实际纳税额")
    private String taxPaidLastYear;

    /**
     * 项目内容
     */
    @ApiModelProperty("项目内容")
    private String projectContent;

    /**
     * 第一年计划投资
     */
    @ApiModelProperty("第一年计划投资")
    private String investmentPlanFirstYear;

    /**
     * 前三年计划投资
     */
    @ApiModelProperty("前三年计划投资")
    private String investmentPlanThreeYears;

    /**
     * 第一年员工人数
     */
    @ApiModelProperty("第一年员工人数")
    private String employeesFirstYear;

    /**
     * 前三年员工人数
     */
    @ApiModelProperty("前三年员工人数")
    private String employeesThreeYears;

    /**
     * 第一年场地需求
     */
    @ApiModelProperty("第一年场地需求")
    private String siteDemandFirstYear;

    /**
     * 前三年场地需求
     */
    @ApiModelProperty("前三年场地需求")
    private String siteDemandThreeYears;

    /**
     * 第一年预计产值
     */
    @ApiModelProperty("第一年预计产值")
    private String outputValueFirstYear;

    /**
     * 前三年预计产值
     */
    @ApiModelProperty("前三年预计产值")
    private String outputValueThreeYears;

    /**
     * 团队
     */
    @ApiModelProperty("团队")
    private String team;

    /**
     * 技术
     */
    @ApiModelProperty("技术")
    private String technology;

    /**
     * 产品
     */
    @ApiModelProperty("产品")
    private String product;

    /**
     * 市场
     */
    @ApiModelProperty("市场")
    private String market;

    /**
     * 融资
     */
    @ApiModelProperty("融资")
    private String financing;

    /**
     * 荣誉
     */
    @ApiModelProperty("荣誉")
    private String honors;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String ESTABLISHMENT_DATE = "establishment_date";

    public static final String CURRENT_LOCATION = "current_location";

    public static final String IS_HIGH_TECH_PRODUCT = "is_high_tech_product";

    public static final String IS_HIGH_TECH_ENTERPRISE = "is_high_tech_enterprise";

    public static final String REGISTERED_CAPITAL_LAST_YEAR = "registered_capital_last_year";

    public static final String EMPLOYEES_LAST_YEAR = "employees_last_year";

    public static final String INTELLECTUAL_PROPERTY_LAST_YEAR = "intellectual_property_last_year";

    public static final String TOTAL_REVENUE_LAST_YEAR = "total_revenue_last_year";

    public static final String NET_PROFIT_LAST_YEAR = "net_profit_last_year";

    public static final String TAX_PAID_LAST_YEAR = "tax_paid_last_year";

    public static final String INVESTMENT_PLAN_FIRST_YEAR = "investment_plan_first_year";

    public static final String INVESTMENT_PLAN_THREE_YEARS = "investment_plan_three_years";

    public static final String EMPLOYEES_FIRST_YEAR = "employees_first_year";

    public static final String EMPLOYEES_THREE_YEARS = "employees_three_years";

    public static final String SITE_DEMAND_FIRST_YEAR = "site_demand_first_year";

    public static final String SITE_DEMAND_THREE_YEARS = "site_demand_three_years";

    public static final String OUTPUT_VALUE_FIRST_YEAR = "output_value_first_year";

    public static final String OUTPUT_VALUE_THREE_YEARS = "output_value_three_years";

    public static final String PROJECT_CONTENT = "project_content";

    public static final String TEAM = "team";

    public static final String TECHNOLOGY = "technology";

    public static final String PRODUCT = "product";

    public static final String MARKET = "market";

    public static final String FINANCING = "financing";

    public static final String HONOR = "honor";

    public static final String APPLICANT_NAME = "applicant_name";

    public static final String PROJECT_NAME = "project_name";

    public static final String INDUSTRY_FIELD_ID = "industry_field_id";

    public static final String TYPE_ID = "type_id";

    public static final String CREATE_TIME = "create_time";

    public static final String CREATE_USER = "create_user";
}
