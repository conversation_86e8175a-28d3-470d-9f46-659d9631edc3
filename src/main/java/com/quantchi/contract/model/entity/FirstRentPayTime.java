package com.quantchi.contract.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 首期租金支付时间
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("first_rent_pay_time")
@ApiModel(value = "FirstRentPayTime对象", description = "首期租金支付时间")
public class FirstRentPayTime implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("首期租金支付时间")
    private String payTime;

    public static final String ID = "id";

    public static final String PAY_TIME = "pay_time";
}
