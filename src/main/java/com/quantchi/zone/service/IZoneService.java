package com.quantchi.zone.service;

import com.quantchi.zone.model.bo.ZoneBO;
import com.quantchi.zone.model.entity.Zone;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.zone.model.vo.*;
import org.apache.commons.math3.analysis.function.Floor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 空间实体 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
public interface IZoneService extends IService<Zone> {

    /**
     * 空间列表
     */
    List<ZoneListVO> zoneList(String parkId, String buildingId);

    /**
     * 创建空间
     */
    void editZone(ZoneBO zoneBO);

    /**
     * 逻辑删除空间（isValid=0）
     */
    void deleteZone(String zoneId);

    /**
     * 查询空间详情
     */
    ZoneDetailVO zoneDetail(String zoneId);

    /**
     * 查询空间简单列表（仅id和name）
     */
    List<FloorVO> zoneSimpleList(String parkId, String buildId, String contractId);

    List<ZoneSimpleVO> zoneSimpleListAll();
}
