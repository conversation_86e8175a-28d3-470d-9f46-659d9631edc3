package com.quantchi.common.domain;

import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 索引导航设置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class CustomIndexNavSetting extends IndexNavSetting {

    @ApiModelProperty("筛选项自定义类型，0无自定义类型/1日期自定义，传入开始和结束日期/2金额自定义，传入下限和上限，单位万/3级联搜索")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer customType;

    @ApiModelProperty("筛选项查询类型，详见SearchTypeConstants")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer searchType;

    @ApiModelProperty("节点等级")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer level;

    @ApiModelProperty(value = "是否禁止显示")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean disabled = false;

    private String type;

    @ApiModelProperty("级联选择")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CustomIndexNavSetting> children;

    /**
     * 从产业领域树中构建筛选项
     *
     * @param source
     */
    public CustomIndexNavSetting(final Map<String, Object> source) {
        this.field = String.valueOf(source.get("id"));
        this.fieldName = String.valueOf(source.get("name"));
    }

    public CustomIndexNavSetting(final String field) {
        this.field = field;
        this.fieldName = field;
    }

    // 将 List<Tree<String>> 转换为 List<CustomIndexNavSetting>
    public static List<CustomIndexNavSetting> convertTreeList(final List<Tree<String>> treeList) {
        final List<CustomIndexNavSetting> result = new ArrayList<>();

        for (final Tree<String> tree : treeList) {
            final CustomIndexNavSetting setting = convertTreeToSetting(tree);
            result.add(setting);
        }
        return result;
    }

    // 将单个 Tree<String> 转换为 CustomIndexNavSetting
    private static CustomIndexNavSetting convertTreeToSetting(final Tree<String> tree) {
        final CustomIndexNavSetting setting = new CustomIndexNavSetting();

        setting.setField(tree.getId());  // 将 Tree<String> 的 id 转换为 CustomIndexNavSetting 的 field
        setting.setFieldName((String) tree.getName());  // 将 name 转换为 fieldName
        setting.setType(String.valueOf(tree.get("type")));
//        setting.setLevel((Integer) tree.getWeight());  // 设置当前节点的等级

        // 如果当前节点有子节点，递归转换
        if (tree.getChildren() != null && !tree.getChildren().isEmpty()) {
            final List<CustomIndexNavSetting> childSettings = convertTreeList(tree.getChildren());
            setting.setChildren(childSettings);
        }

        return setting;
    }
}
