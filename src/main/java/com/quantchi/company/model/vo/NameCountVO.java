package com.quantchi.company.model.vo;

import cn.hutool.core.collection.CollUtil;
import com.quantchi.company.model.bo.IndustryAnalysisCompanyFinancingBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 报告实体
 *
 * <AUTHOR>
 * @date 2022/02/17
 */
@Data
@NoArgsConstructor
public class NameCountVO implements Serializable {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("数量")
    private Long count = 0L;

    public NameCountVO(final String name, final Long count) {
        this.name = name;
        if (count != null) {
            this.count = count;
        } else {
            this.count = 0L;
        }
    }

    public NameCountVO(final String name, final Integer count) {
        this.name = name;
        if (count != null) {
            this.count = Long.valueOf(count);
        } else {
            this.count = 0L;
        }
    }

    public NameCountVO(final IndustryAnalysisCompanyFinancingBO bo) {
        this.name = bo.getFinancingRound();
        this.count = Long.valueOf(bo.getCount());
    }

    public static List<String> getMaxCountNames(final List<NameCountVO> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyList();
        }
        // 找到最大数量值
        final Optional<Long> maxCountOptional = records.stream()
                .map(NameCountVO::getCount)
                .max(Long::compare);

        if (!maxCountOptional.isPresent()) {
            return Collections.emptyList(); // 如果列表为空，返回空列表
        }

        final long maxCount = maxCountOptional.get();

        // 获取所有具有最大数量的名称
        return records.stream()
                .filter(record -> record.getCount() == maxCount)
                .map(NameCountVO::getName)
                .collect(Collectors.toList());
    }

}
