package com.quantchi.application.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 企业联系人信息VO
 */
@Data
@ApiModel("企业联系人信息")
public class CompanyContactVO {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("联系方式")
    private String contactInfo;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("在职状态：0-离职，1-在职")
    private Integer jobStatus;

    @ApiModelProperty("备注")
    private String remark;


}
