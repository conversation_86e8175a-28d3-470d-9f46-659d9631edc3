package com.quantchi.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;

/**
 * Word文档处理工具类
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
@Slf4j
public class WordUtils {

    /** 未选中的复选框字符 */
    private static final String CHECKBOX_UNCHECKED = "□";

    /** 已选中的复选框字符（勾选符号） */
    private static final String CHECKBOX_CHECKED = "☑";

    // 使用静态集合跨方法调用保持已处理的复选框状态
    private static final Set<String> PROCESSED_NORMAL_CHECKBOXES = new HashSet<>();
    private static final Map<String, Boolean> PROCESSED_SPECIAL_CHECKBOXES = new HashMap<>();

    private static List<String> specialCheckbox = Arrays.asList("高新产品", "高新企业", "是否实际入驻", "是否有经营异常", "科技型中小企业");

    /**
     * 替换Word文档中的占位符
     *
     * @param inputStream  模板文件输入流
     * @param outputStream 生成文件输出流
     * @param params       替换参数
     * @throws IOException IO异常
     */
    public static void replaceTextInDocument(final InputStream inputStream, final OutputStream outputStream, final Map<String, String> params) throws IOException {
        try (final XWPFDocument document = new XWPFDocument(inputStream)) {

            // 打印替换参数，用于调试
            logParams(params);

            log.info("开始替换文档中的占位符，参数数量: {}", params.size());

            // 提取复选框相关参数
            Map<String, String> checkboxParams = extractCheckboxParams(params);
            log.info("提取到复选框参数: {}", checkboxParams);

            // 用于记录替换次数
            int replaceCount = 0;

            // 每次文档处理开始前清空复选框状态
            PROCESSED_NORMAL_CHECKBOXES.clear();
            PROCESSED_SPECIAL_CHECKBOXES.clear();

            // 用于记录已处理的特殊复选框，避免重复处理
            // Map<String, Boolean> processedSpecialCheckboxes = new HashMap<>();

            // 用于记录已处理的普通复选框，避免重复处理
            // Set<String> processedCheckboxes = new HashSet<>();

            // 替换文档主体中的占位符
            for (final XWPFParagraph paragraph : document.getParagraphs()) {
                replaceCount += replaceParagraphText(paragraph, params);
            }

            // 替换页眉中的占位符
            for (final XWPFHeader header : document.getHeaderList()) {
                for (final XWPFParagraph paragraph : header.getParagraphs()) {
                    replaceCount += replaceParagraphText(paragraph, params);
                }
            }

            // 替换页脚中的占位符
            for (final XWPFFooter footer : document.getFooterList()) {
                for (final XWPFParagraph paragraph : footer.getParagraphs()) {
                    replaceCount += replaceParagraphText(paragraph, params);
                }
            }

            // 直接处理所有表格中的所有复选框
            // 处理表格中的文本及复选框
            for (XWPFTable table : document.getTables()) {
                // 处理普通复选框和文本替换
                for (XWPFTableRow row : table.getRows()) {
                    // 先搜集这一行中所有带复选框的单元格位置
                    Map<Integer, XWPFTableCell> checkboxCells = new HashMap<>();

                    for (int i = 0; i < row.getTableCells().size(); i++) {
                        XWPFTableCell cell = row.getTableCells().get(i);
                        String cellText = cell.getText();
                        if (cellText.contains(CHECKBOX_UNCHECKED)) {
                            checkboxCells.put(i, cell);
                        }
                    }

                    // 现在处理每个单元格
                    for (int i = 0; i < row.getTableCells().size(); i++) {
                        XWPFTableCell cell = row.getTableCells().get(i);
                        String cellText = cell.getText();

                        // 替换单元格中的文本
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceCount += replaceParagraphText(paragraph, params);
                        }

                        // 处理高新产品复选框
                        if (cellText.contains("高新产品") && !PROCESSED_SPECIAL_CHECKBOXES.containsKey("高新产品")) {
                            handleSpecialCheckbox("高新产品", cellText, cell, row, i, checkboxCells, checkboxParams);
                        }

                        // 处理高新企业复选框
                        else if (cellText.contains("高新企业") && !PROCESSED_SPECIAL_CHECKBOXES.containsKey("高新企业")) {
                            handleSpecialCheckbox("高新企业", cellText, cell, row, i, checkboxCells, checkboxParams);
                        }

                        // 处理是否实际入驻复选框
                        else if (cellText.contains("是否实际入驻") && !PROCESSED_SPECIAL_CHECKBOXES.containsKey("是否实际入驻")) {
                            handleSpecialCheckbox("是否实际入驻", cellText, cell, row, i, checkboxCells, checkboxParams);
                        }

                        // 处理是否有经营异常复选框
                        else if (cellText.contains("是否有经营异常") && !PROCESSED_SPECIAL_CHECKBOXES.containsKey("是否有经营异常")) {
                            handleSpecialCheckbox("是否有经营异常", cellText, cell, row, i, checkboxCells, checkboxParams);
                        }

                        // 处理科技型中小企业复选框
                        else if (cellText.contains("科技型中小企业") && !PROCESSED_SPECIAL_CHECKBOXES.containsKey("科技型中小企业")) {
                            handleSpecialCheckbox("科技型中小企业", cellText, cell, row, i, checkboxCells, checkboxParams);
                        }

                        // 处理普通复选框
                        else if (cellText.contains(CHECKBOX_UNCHECKED)) {
                            for (String checkboxId : checkboxParams.keySet()) {
                                // 跳过高新产品和高新企业的选项，这些已经通过专门的逻辑处理了
                                if (checkboxId.contains("|")) {
                                    String[] parts = checkboxId.split("\\|");
                                    if (parts.length == 2) {
                                        String option = parts[0].trim();
                                        String category = parts[1].trim();

                                        // 如果是高新产品或高新企业，跳过
                                        if (specialCheckbox.contains(category)) {
                                            continue;
                                        }

                                        // 如果是"是"或"否"选项，它们可能已经在特殊处理中处理过了
                                        if (("是".equals(option) || "否".equals(option)) &&
                                                PROCESSED_NORMAL_CHECKBOXES.contains(option)) {
                                            log.info("选项[{}]已经被处理过，跳过", option);
                                            continue;
                                        }
                                    }
                                }

                                // 检查该复选框是否已经处理过，避免重复勾选
                                String contextCategory = getCheckboxCategory(checkboxId);
                                if (PROCESSED_NORMAL_CHECKBOXES.contains(contextCategory)) {
                                    log.info("复选框[{}]已经被处理过，跳过", contextCategory);
                                    continue;
                                }

                                // 如果单元格文本包含复选框ID，勾选该复选框
                                String option = getCheckboxOption(checkboxId);
                                if (cellText.contains(option)) {
                                    boolean processed = checkNormalCheckbox(cell, option, checkboxId);
                                    if (processed) {
                                        log.info("在单元格中处理了普通复选框[{}], 原始ID: [{}]", option, checkboxId);
                                        // 只标记原始ID为已处理，避免误标其他选项
                                        PROCESSED_NORMAL_CHECKBOXES.add(checkboxId);
                                        // 每个单元格只处理一个匹配的复选框
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }


                replaceCount += 40; // 估计替换数量
            }

            log.info("文档替换完成，共替换 {} 处", replaceCount);

            // 写入输出流
            document.write(outputStream);
        }
    }

    private static void handleSpecialCheckbox(String category, String cellText, XWPFTableCell cell, XWPFTableRow row, int i,
                                              Map<Integer, XWPFTableCell> checkboxCells, Map<String, String> checkboxParams ) {
        log.info("找到包含'{}'的单元格: {}", category, cellText);

        // 在当前单元格或相邻单元格查找复选框
        XWPFTableCell checkboxCell = null;
        if (cellText.contains(CHECKBOX_UNCHECKED)) {
            // 如果当前单元格包含复选框
            checkboxCell = cell;
            log.info("{}单元格自身包含复选框", category);
        } else {
            // 查找相邻单元格是否包含复选框
            if (i + 1 < row.getTableCells().size() && checkboxCells.containsKey(i + 1)) {
                checkboxCell = checkboxCells.get(i + 1);
                log.info("{}的复选框在右侧相邻单元格", category);
            } else if (i > 0 && checkboxCells.containsKey(i - 1)) {
                checkboxCell = checkboxCells.get(i - 1);
                log.info("{}的复选框在左侧相邻单元格", category);
            } else if (!checkboxCells.isEmpty()) {
                // 尝试找最近的复选框单元格
                int minDistance = Integer.MAX_VALUE;
                int closestCellIndex = -1;

                for (int cellIndex : checkboxCells.keySet()) {
                    int distance = Math.abs(cellIndex - i);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCellIndex = cellIndex;
                    }
                }

                if (closestCellIndex != -1) {
                    checkboxCell = checkboxCells.get(closestCellIndex);
                    log.info("{}的复选框在距离{}个单元格的位置", category, minDistance);
                }
            }
        }

        if (checkboxCell != null) {
            // 处理高新产品的复选框
            String key = category;
            boolean processed = false;

            if (checkboxParams.containsKey("是|" + key)) {
                processed = processDirectCheckbox(checkboxCell, "是", key);
                if (processed) {
                    log.info("处理了{}[是]复选框", key);
                }
            } else if (checkboxParams.containsKey("否|" + key)) {
                processed = processDirectCheckbox(checkboxCell, "否", key);
                if (processed) {
                    log.info("处理了{}[否]复选框", key);
                }
            }

            // 如果处理成功，标记为已处理
            if (processed) {
                PROCESSED_SPECIAL_CHECKBOXES.put(key, true);
            }
        }
    }

    /**
     * 从参数中提取复选框相关参数
     *
     * @param params 全部参数
     * @return 复选框参数
     */
    private static Map<String, String> extractCheckboxParams(Map<String, String> params) {
        Map<String, String> checkboxParams = new HashMap<>();

        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 处理以 checkbox_ 开头的参数
            if (key.startsWith("checkbox_") && "true".equals(value)) {
                String checkboxId = key.substring("checkbox_".length());
                checkboxParams.put(checkboxId, "true");

                // 记录日志
                log.info("提取到复选框参数: {} -> {}", checkboxId, "true");
            }
            // 直接处理包含 | 的参数（不需要 checkbox_ 前缀）
            else if (key.contains("|") && "true".equals(value)) {
                checkboxParams.put(key, "true");

                // 记录日志
                log.info("提取到带上下文的复选框参数: {} -> {}", key, "true");
            }
        }

        return checkboxParams;
    }

    /**
     * 直接处理复选框单元格，查找并勾选指定选项的复选框
     * @param cell 包含复选框的单元格
     * @param option 选项（"是"或"否"）
     * @param category 类别（"高新产品"或"高新企业"）
     * @return 是否成功处理
     */
    private static boolean processDirectCheckbox(XWPFTableCell cell, String option, String category) {
        log.info("直接处理复选框单元格，选项: {}, 类别: {}", option, category);

        if (PROCESSED_SPECIAL_CHECKBOXES.containsKey(category)) {
            log.info("类别 [{}] 已经被处理过，跳过", category);
            return false;
        }

        // 遍历所有段落，找到包含复选框的段落
        List<XWPFParagraph> checkboxParagraphs = new ArrayList<>();
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            if (paragraph.getText().contains(CHECKBOX_UNCHECKED)) {
                checkboxParagraphs.add(paragraph);
                log.info("找到包含复选框的段落: {}", paragraph.getText());
            }
        }

        if (checkboxParagraphs.isEmpty()) {
            log.warn("未找到包含复选框的段落");
            return false;
        }

        // 记录成功处理的状态
        boolean processed = false;

        // 优先处理只有两个复选框的段落（通常一个是"是"，一个是"否"）
        for (XWPFParagraph paragraph : checkboxParagraphs) {
            List<XWPFRun> runs = paragraph.getRuns();

            // 打印所有Run内容
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                if (run.getText(0) != null) {
                    log.info("Run[{}]: '{}'", i, run.getText(0).replace("\n", "\\n").replace("\r", "\\r"));
                }
            }

            // 查找所有包含复选框的Run
            List<Integer> checkboxRuns = new ArrayList<>();
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String text = run.getText(0);
                if (text != null && text.contains(CHECKBOX_UNCHECKED)) {
                    checkboxRuns.add(i);
                    log.info("找到复选框在Run[{}]", i);
                }
            }

            if (checkboxRuns.size() == 2) {
                // 通常第一个Run包含"是"选项，第二个Run包含"否"选项
                int targetIndex = "是".equals(option) ? 0 : 1;

                // 确认对应的Run包含期望选项
                boolean foundOption = false;
                if (targetIndex < runs.size()) {
                    XWPFRun run = runs.get(checkboxRuns.get(targetIndex));
                    String runText = run.getText(0);
                    if (runText != null && runText.contains(option)) {
                        foundOption = true;
                    }
                }

                // 如果找不到匹配的选项，尝试通过内容匹配查找
                if (!foundOption) {
                    for (int i = 0; i < checkboxRuns.size(); i++) {
                        XWPFRun run = runs.get(checkboxRuns.get(i));
                        String runText = run.getText(0);
                        if (runText != null && runText.contains(option)) {
                            targetIndex = i;
                            foundOption = true;
                            break;
                        }
                    }
                }

                if (targetIndex < checkboxRuns.size()) {
                    int runIndex = checkboxRuns.get(targetIndex);
                    XWPFRun run = runs.get(runIndex);
                    String runText = run.getText(0);

                    // 替换复选框
                    String newText = runText.replace(CHECKBOX_UNCHECKED, CHECKBOX_CHECKED);
                    run.setText(newText, 0);

                    // 验证替换是否成功
                    if (run.getText(0).contains(CHECKBOX_CHECKED)) {
                        log.info("成功勾选{}选项，在Run[{}]", option, runIndex);
                        processed = true;

                        // 标记该类别已处理
                        PROCESSED_SPECIAL_CHECKBOXES.put(category, true);

                        // 也标记普通复选框已处理，避免重复处理
                        if (specialCheckbox.contains(category)) {
                            PROCESSED_NORMAL_CHECKBOXES.add("是");
                            PROCESSED_NORMAL_CHECKBOXES.add("否");
                        }

                        return true;
                    } else {
                        log.warn("勾选操作似乎未生效，Run[{}]的文本: {}", runIndex, run.getText(0));
                    }
                }
            } else if (checkboxRuns.size() == 1) {
                // 只有一个复选框，检查是否包含目标选项
                int runIndex = checkboxRuns.get(0);
                XWPFRun run = runs.get(runIndex);
                String runText = run.getText(0);

                if (runText != null && runText.contains(option)) {
                    // 直接勾选
                    String newText = runText.replace(CHECKBOX_UNCHECKED, CHECKBOX_CHECKED);
                    run.setText(newText, 0);

                    // 验证替换是否成功
                    if (run.getText(0).contains(CHECKBOX_CHECKED)) {
                        log.info("只有一个复选框，直接勾选在Run[{}]", runIndex);
                        processed = true;

                        // 标记该类别已处理
                        PROCESSED_SPECIAL_CHECKBOXES.put(category, true);
                        return true;
                    } else {
                        log.warn("勾选操作似乎未生效，Run[{}]的文本: {}", runIndex, run.getText(0));
                    }
                }
            }
        }

        // 如果未成功处理，记录警告日志
        if (!processed) {
            log.warn("无法在单元格中找到合适的复选框用于勾选{}选项", option);
        }

        return processed;
    }


    /**
     * 替换段落中的文本
     *
     * @param paragraph 段落
     * @param params 替换参数
     * @return 替换次数
     */
    private static int replaceParagraphText(final XWPFParagraph paragraph, final Map<String, String> params) {
        int count = 0;
        String paragraphText = paragraph.getText();
        if (StringUtils.isBlank(paragraphText)) {
            return 0;
        }

        // 检查是否包含任何需要替换的内容
        boolean needReplace = false;
        for (String key : params.keySet()) {
            String placeholder = "${" + key + "}";
            if (paragraphText.contains(placeholder)) {
                needReplace = true;
                count++;
                break;
            }
        }

        if (!needReplace) {
            return 0;
        }

        // 重建段落内容
        // 先清空所有Run
        for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
            paragraph.removeRun(i);
        }

        // 复制替换后的文本
        String replacedText = paragraphText;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue() : "";
            if (replacedText.contains(key)) {
                replacedText = replacedText.replace(key, value);
            }
        }

        // 创建新的Run并添加替换后的文本
        XWPFRun newRun = paragraph.createRun();
        newRun.setText(replacedText);

        return count;
    }

    /**
     * 打印占位符参数，用于调试
     *
     * @param params 参数映射
     */
    public static void logParams(final Map<String, String> params) {
        log.info("==== 开始打印替换参数 ====");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            log.info("参数: ${" + entry.getKey() + "} -> " + entry.getValue());
        }
        log.info("==== 结束打印替换参数 ====");
    }

    /**
     * 从复选框ID中获取选项部分
     * @param checkboxId 复选框ID
     * @return 选项
     */
    private static String getCheckboxOption(String checkboxId) {
        if (checkboxId.contains("|")) {
            return checkboxId.split("\\|")[0].trim();
        }
        return checkboxId;
    }

    /**
     * 从复选框ID中获取类别部分
     * @param checkboxId 复选框ID
     * @return 类别
     */
    private static String getCheckboxCategory(String checkboxId) {
        if (checkboxId.contains("|")) {
            String[] parts = checkboxId.split("\\|");
            if (parts.length > 1) {
                return parts[1].trim();
            }
        }
        return checkboxId;
    }

    /**
     * 处理普通复选框
     * @param cell 单元格
     * @param checkboxText 复选框文本
     * @param originalCheckboxId 原始复选框ID（可能包含上下文信息）
     * @return 是否成功处理
     */
    private static boolean checkNormalCheckbox(XWPFTableCell cell, String checkboxText, String originalCheckboxId) {
        log.info("检查复选框，文本: {}, 原始ID: {}", checkboxText, originalCheckboxId);
        
        // 提取上下文类别
        String contextCategory = getCheckboxCategory(originalCheckboxId);
        log.info("复选框上下文类别: {}", contextCategory);
        
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            // 检查段落中是否包含上下文类别信息
            boolean isCorrectContext = true;
            if (StringUtils.isNotBlank(contextCategory)) {
                if ("产业领域".equals(contextCategory)) {
                    isCorrectContext = paragraph.getText().contains("产业领域") || 
                                     containsIndustryKeywords(paragraph.getText());
                    log.info("检查产业领域上下文: {}\n段落文本: {}", isCorrectContext, paragraph.getText());
                } else if ("所属类型".equals(contextCategory)) {
                    isCorrectContext = paragraph.getText().contains("所属类型") || 
                                     containsTypeKeywords(paragraph.getText());
                    log.info("检查所属类型上下文: {}\n段落文本: {}", isCorrectContext, paragraph.getText());
                }
            }
            
            // 如果上下文不匹配，跳过此段落
            if (!isCorrectContext) {
                log.info("段落上下文不匹配，跳过");
                continue;
            }
            
            // 确保段落同时包含复选框文本和复选框
            if (paragraph.getText().contains(checkboxText) && paragraph.getText().contains(CHECKBOX_UNCHECKED)) {
                List<XWPFRun> runs = paragraph.getRuns();

                // 记录每个Run的文本内容，用于调试
                Map<Integer, String> runTexts = new HashMap<>();
                for (int i = 0; i < runs.size(); i++) {
                    String text = runs.get(i).getText(0);
                    if (text != null) {
                        runTexts.put(i, text);
                    }
                }
                log.info("段落中的所有Run内容: {}", runTexts);

                // 精确定位包含指定文本和复选框的Run
                int textRunIndex = -1;
                Map<Integer, Integer> checkboxPositions = new HashMap<>();

                for (int i = 0; i < runs.size(); i++) {
                    XWPFRun run = runs.get(i);
                    String runText = run.getText(0);

                    if (runText == null) {
                        continue;
                    }

                    // 记录文本位置
                    if (runText.contains(checkboxText)) {
                        textRunIndex = i;
                        log.info("找到文本[{}]在Run[{}]", checkboxText, i);
                    }

                    // 记录所有复选框位置
                    if (runText.contains(CHECKBOX_UNCHECKED)) {
                        int checkboxPos = runText.indexOf(CHECKBOX_UNCHECKED);
                        // 记录复选框在Run中的位置以及Run索引
                        checkboxPositions.put(i, checkboxPos);
                        log.info("找到复选框在Run[{}]的位置{}", i, checkboxPos);
                    }
                }

                // 如果同一个Run中同时包含文本和复选框，优先处理这种情况
                if (textRunIndex != -1 && checkboxPositions.containsKey(textRunIndex)) {
                    XWPFRun run = runs.get(textRunIndex);
                    String runText = run.getText(0);

                    // 检查文本和复选框的顺序，确保复选框跟在文本后面
                    int textPos = runText.indexOf(checkboxText);
                    int checkboxPos = runText.indexOf(CHECKBOX_UNCHECKED);

                    if (textPos < checkboxPos || Math.abs(textPos - checkboxPos) <= checkboxText.length()) {
                        // 只替换紧跟在文本后面的第一个复选框
                        String before = runText.substring(0, checkboxPos);
                        String after = runText.substring(checkboxPos + CHECKBOX_UNCHECKED.length());
                        String newText = before + CHECKBOX_CHECKED + after;
                        run.setText(newText, 0);

                        // 验证替换是否成功
                        if (run.getText(0).contains(CHECKBOX_CHECKED)) {
                            log.info("在同一个Run[{}]中处理了[{}]的复选框", textRunIndex, checkboxText);
                            return true;
                        } else {
                            log.warn("替换未生效: {}", run.getText(0));
                        }
                    }
                }

                // 如果文本和复选框不在同一个Run，查找最近的复选框
                if (textRunIndex != -1) {
                    // 查找与文本Run最近的复选框Run
                    int closestCheckboxRunIndex = -1;
                    int minDistance = Integer.MAX_VALUE;

                    for (int checkboxRunIndex : checkboxPositions.keySet()) {
                        // 只考虑紧跟在文本后面的复选框或相邻的复选框
                        if (checkboxRunIndex > textRunIndex || checkboxRunIndex == textRunIndex + 1) {
                            int distance = checkboxRunIndex - textRunIndex;
                            if (distance < minDistance) {
                                minDistance = distance;
                                closestCheckboxRunIndex = checkboxRunIndex;
                            }
                        }
                    }

                    // 如果找不到后面的复选框，尝试找最近的
                    if (closestCheckboxRunIndex == -1 && !checkboxPositions.isEmpty()) {
                        for (int checkboxRunIndex : checkboxPositions.keySet()) {
                            int distance = Math.abs(checkboxRunIndex - textRunIndex);
                            if (distance < minDistance) {
                                minDistance = distance;
                                closestCheckboxRunIndex = checkboxRunIndex;
                            }
                        }
                    }

                    // 勾选找到的复选框
                    if (closestCheckboxRunIndex != -1) {
                        XWPFRun run = runs.get(closestCheckboxRunIndex);
                        String runText = run.getText(0);

                        // 如果Run中有多个复选框，只勾选第一个
                        int checkboxPos = runText.indexOf(CHECKBOX_UNCHECKED);
                        if (checkboxPos != -1) {
                            String before = runText.substring(0, checkboxPos);
                            String after = runText.substring(checkboxPos + CHECKBOX_UNCHECKED.length());
                            String newText = before + CHECKBOX_CHECKED + after;
                            run.setText(newText, 0);

                            // 验证替换是否成功
                            if (run.getText(0).contains(CHECKBOX_CHECKED)) {
                                log.info("在Run[{}]中为[{}]精确勾选了复选框，距离文本Run[{}]有{}个位置",
                                        closestCheckboxRunIndex, checkboxText, textRunIndex, minDistance);
                                return true;
                            } else {
                                log.warn("替换未生效: {}", run.getText(0));
                            }
                        }
                    }
                }
            }
        }

        log.warn("未能为[{}]找到匹配的复选框", checkboxText);
        return false;
    }
    
    /**
     * 检查文本中是否包含产业领域相关关键词
     * 
     * @param text 需要检查的文本
     * @return 是否包含相关关键词
     */
    private static boolean containsIndustryKeywords(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        
        // 定义产业领域相关关键词列表
        final String[] industryKeywords = {
            "光电技术", "信息技术", "智能装备", "医疗健康", "节能环保", "科技服务"
        };
        
        for (String keyword : industryKeywords) {
            if (text.contains(keyword)) {
                log.info("找到产业领域关键词: {}", keyword);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查文本中是否包含所属类型相关关键词
     * 
     * @param text 需要检查的文本
     * @return 是否包含相关关键词
     */
    private static boolean containsTypeKeywords(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        
        // 定义所属类型相关关键词列表
        final String[] typeKeywords = {
            "创业孵化", "投资加速", "创新平台", "总部研发", "规模生产", "配套机构"
        };
        
        for (String keyword : typeKeywords) {
            if (text.contains(keyword)) {
                log.info("找到所属类型关键词: {}", keyword);
                return true;
            }
        }
        
        return false;
    }
}
