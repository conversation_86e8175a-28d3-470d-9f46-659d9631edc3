package com.quantchi.common.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.quantchi.common.component.DataInitializerCompopnent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "数据操作接口")
@RequestMapping("/data")
@Slf4j
@RequiredArgsConstructor
public class DataController {

    private final DataInitializerCompopnent dataInitializerCompopnent;

    @GetMapping("/refreshInMemoryData")
    @ApiOperation(value = "刷新内存数据")
    @SaIgnore
    public String refreshInMemoryData() {
        dataInitializerCompopnent.init();
        return "刷新成功";
    }

}
