package com.quantchi.server.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 近期申报实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("recent_declaration")
@ApiModel(value = "RecentDeclaration对象", description = "近期申报信息")
public class RecentDeclaration implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "链接地址")
    private String url;
    
    @ApiModelProperty(value = "创建时间")
    @JsonIgnore
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "创建用户")
    @JsonIgnore
    private String createUser;
    
    @ApiModelProperty(value = "来源")
    private String source;
    
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate publishTime;
    
    @ApiModelProperty(value = "文章名称")
    private String articleName;

    public static final String ID = "id";

    public static final String URL = "url";
    
    public static final String CREATE_TIME = "create_time";
    
    public static final String CREATE_USER = "create_user";
    
    public static final String SOURCE = "source";
    
    public static final String PUBLISH_TIME = "publish_time";
    
    public static final String ARTICLE_NAME = "article_name";
}
