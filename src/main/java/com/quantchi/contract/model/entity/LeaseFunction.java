package com.quantchi.contract.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 租赁物功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("lease_function")
@ApiModel(value = "LeaseFunction对象", description = "租赁物功能")
public class LeaseFunction implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("租赁物功能")
    private String function;

    public static final String ID = "id";

    public static final String FUNCTION = "function";
}
