package com.quantchi.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quantchi.contract.model.bo.LeaseRentListQuery;
import com.quantchi.contract.model.entity.LeaseRent;
import com.quantchi.contract.model.vo.LeaseRentDetailVO;
import com.quantchi.contract.model.vo.LeaseRentListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Mapper
public interface LeaseRentMapper extends BaseMapper<LeaseRent> {
    // 如需自定义SQL可在此添加

    /**
     * 多条件租金明细查询（不分页，返回全部结果）
     */
    List<LeaseRentListVO> selectByCondition(@Param("query") LeaseRentListQuery query);

    /**
     * 专门用于导出的查询，返回每个租金记录
     */
    List<LeaseRentListVO> selectForExport(@Param("query") LeaseRentListQuery query);

    /**
     * 根据合同id查询租金明细及扩展信息
     */
    List<LeaseRentListVO> selectByContractIdForList(@Param("contractId") String contractId);

    /**
     * 查询合同扩展信息（园区、楼栋、房号、承租方、租赁起止时间）
     */
    LeaseRentDetailVO selectContractExtInfoById(@Param("contractId") String contractId);

    /**
     * 租金应收总额
     */
    BigDecimal sumReceivable();

    /**
     * 根据园区ID查询租金应收总额
     * 
     * @param parkId 园区ID
     * @return 应收租金总额
     */
    BigDecimal sumReceivableByPark(@Param("parkId") String parkId);

    /**
     * 租金已收总额
     */
    BigDecimal sumReceived();

    /**
     * 根据园区ID查询租金已收总额
     * 
     * @param parkId 园区ID
     * @return 已收租金总额
     */
    BigDecimal sumReceivedByPark(@Param("parkId") String parkId);

    /**
     * 租金未收总额
     */
    BigDecimal sumUnreceived();

    /**
     * 根据园区ID查询租金未收总额
     * 
     * @param parkId 园区ID
     * @return 未收租金总额
     */
    BigDecimal sumUnreceivedByPark(@Param("parkId") String parkId);

    /**
     * 未来30天内到期合同的应收租金
     */
    BigDecimal sumExpiring(@Param("endDate") LocalDate endDate);

    /**
     * 根据园区ID查询未来30天内到期合同的应收租金
     * 
     * @param endDate 结束日期
     * @param parkId 园区ID
     * @return 快到期的租金总额
     */
    BigDecimal sumExpiringByPark(@Param("endDate") LocalDate endDate, @Param("parkId") String parkId);
}
