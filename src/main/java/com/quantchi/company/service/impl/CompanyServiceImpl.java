package com.quantchi.company.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.FunctionScoreMode;
import co.elastic.clients.elasticsearch._types.query_dsl.Operator;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch.core.search.TrackHits;
import co.elastic.clients.json.JsonData;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.common.config.properties.KeywordSearchProperties;
import com.quantchi.common.constant.Constants;
import com.quantchi.common.constant.SearchTypeConstants;
import com.quantchi.common.domain.CustomIndexNavSetting;
import com.quantchi.common.domain.NavigationSettings;
import com.quantchi.common.enums.CustomTypeEnum;
import com.quantchi.common.enums.EsIndexEnum;
import com.quantchi.common.exception.BusinessException;
import com.quantchi.common.helper.ElasticsearchHelper;
import com.quantchi.common.model.*;
import com.quantchi.common.utils.AliyunUtils;
import com.quantchi.common.utils.DateHandlerUtil;
import com.quantchi.common.utils.ElasticsearchBuilder;
import com.quantchi.company.mapper.CompanyInvestMapper;
import com.quantchi.company.mapper.CompanyMapper;
import com.quantchi.company.mapper.CompanyParkMapper;
import com.quantchi.company.model.entity.*;
import com.quantchi.company.model.vo.*;
import com.quantchi.company.service.*;
import io.swagger.annotations.ApiModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, Company> implements ICompanyService {

    private final ICompanyTagService companyTagService;

    private final ICompanyParkService companyParkService;

    private final ElasticsearchHelper elasticsearchHelper;

    private final ICompanyStockService companyStockService;

    private final CompanyInvestMapper companyInvestMapper;

    private final IDmDivisionService dmDivisionService;

    private final ThreadPoolTaskExecutor docExecutor;

    private final NavigationSettings navigationSettings;

    private final KeywordSearchProperties keywordSearchProperties;

    private final AliyunUtils aliyunUtils;

    /**
     * 获取筛选项
     */

    @Override
    public List<CustomIndexNavSetting> getNavSetting() {
        final List<CustomIndexNavSetting> customIndexNavSettings = navigationSettings.getIndexNavSettingMap().get(EsIndexEnum.COMPANY.getEsIndex());
        if (CollUtil.isEmpty(customIndexNavSettings)) {
            return Collections.emptyList();
        }
        // 级联参数根据父级的scope参数构建children
        customIndexNavSettings.forEach(customIndex -> {
            final String[] type = {""};
            final List<CustomIndexNavSetting> children = customIndex.getChildren();
            if (children != null) {
                children.forEach(ch -> {
                    type[0] = ch.getType();
                    final List<CustomIndexNavSetting> settings = new LinkedList<>();
                    final List<String> scope = ch.getScope();
                    if (scope == null) {
                        return;
                    }
                    // 通过scope构建子级的list
                    scope.forEach(sc -> {
                        final CustomIndexNavSetting customIndexNavSetting = new CustomIndexNavSetting();
                        // 使用名称作为field
                        customIndexNavSetting.setField(sc);
                        customIndexNavSetting.setFieldName(sc);
                        settings.add(customIndexNavSetting);
                    });
                    ch.setChildren(settings);
                    ch.setField(ch.getFieldName());
                    ch.setScope(null);
                });
            }
            customIndex.setType(type[0]);
        });
        return customIndexNavSettings;
    }


    @Override
    public CompanyVO baseInfo(final String id) {
        final CompanyVO vo = new CompanyVO();
        final Company company = this.getById(id);
        if (company == null) {
            throw new BusinessException("企业不存在");
        }
        final Map<String, Object> source = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY.getEsIndex(), id, new String[]{"id", "registration", "park"}, new String[]{});
        final Long userId = StpUtil.getLoginIdAsLong();
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(5);
        futureList.add(CompletableFuture.runAsync(() -> {
            vo.setId(company.getId());
            vo.setName(company.getName());
            final String logo = company.getLogo();
            if (CharSequenceUtil.isNotBlank(logo)) {
                vo.setLogo("https:" + CharSequenceUtil.removePrefix((String) logo, "http:"));
            }
            vo.setLogoSource(company.getLogoSource());
            vo.setStatus(company.getStatus());
            vo.setCreditCode(company.getCreditCode());
            vo.setLegalPerson(company.getLegalPerson());
            vo.setEstablishDate(company.getEstablishDate());
            vo.setRegistCapi(company.getRegistCapi());
            vo.setCompanyScale(company.getCompanyScale());
            vo.setDescription(company.getDescription());
            // todo 拆分电话和邮箱
            vo.setTelList(Collections.singletonList(company.getTel()));
            vo.setEmailList(Collections.singletonList(company.getEmail()));
            vo.setWebsite(company.getWebsite());
            vo.setAddress(company.getAddress());
            vo.setPayCapi(company.getPayCapi());
            vo.setCompanyType(company.getCompanyType());
            vo.setRegistrationId(company.getRegistrationId());
            vo.setEmployeeSize(company.getEmployeeSize());
            vo.setInsuredNumber(company.getInsuredNumber());
            vo.setBusinessTerm(company.getBusinessTerm());
            vo.setBelongOrg(company.getBelongOrg());
            vo.setCheckDate(company.getCheckDate());
            vo.setBelongArea(Constants.getBelongArea(company.getProvince(), company.getCity(), company.getArea()));
            vo.setBusinessScope(company.getBusinessScope());
            vo.setUpdateTime(company.getUpdateTime());
            // 国民经济行业分类
            final ArrayList<String> nationIndustryList = new ArrayList<>(Arrays.asList(company.getNationIndustry1(), company.getNationIndustry2(), company.getNationIndustry3(), company.getNationIndustry4()));
            nationIndustryList.removeIf(CharSequenceUtil::isBlank);
            vo.setNationIndustryList(nationIndustryList);
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            //企业注册方式和企业所在园区
            final String registration = Optional.ofNullable(source.get("registration")).map(Object::toString).orElse(null);
            final String park = Optional.ofNullable(source.get("park")).map(Object::toString).orElse(null);
            vo.setRegistration(registration);
            vo.setPark(park);
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            // 企业标签
            List<TagVO> labels = new ArrayList<>();
            final List<CompanyTag> companyTagList = companyTagService.list(Wrappers.<CompanyTag>lambdaQuery()
                            .eq(CompanyTag::getCid, id))
                    .stream()
                    .collect(Collectors.groupingBy(CompanyTag::getTagName))
                    .values()
                    .stream()
                    .map(tags -> tags.stream()
                            .max(Comparator.comparing(
                                            (CompanyTag tag) -> tag.getValidEnd() != null ? 1 : 0)  // 非null优先
                                    .thenComparing(tag -> tag.getValidEnd() != null ? tag.getValidEnd() : LocalDate.MIN))) // 然后按日期降序
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(companyTagList)) {
                labels.addAll(companyTagList.stream().map(tag-> {
                    TagVO tagVO = new TagVO();
                    tagVO.setTagName(tag.getTagName());
                    tagVO.setTagValid(tag.getValidEnd() != null ? tag.getValidEnd().isAfter(LocalDate.now()) ? 1 : 0 : 1);
                    tagVO.setTagDuration(DateHandlerUtil.formatTagDuration(tag.getValidBegin(), tag.getValidEnd()));
                    return tagVO;
                }).collect(Collectors.toList()));
            }
            final List<CompanyPark> companyParkList = companyParkService.list(Wrappers.<CompanyPark>lambdaQuery()
                    .eq(CompanyPark::getCreditCode, company.getCreditCode()));
            if (CollUtil.isNotEmpty(companyParkList)) {
                labels.addAll(companyParkList.stream().map(tag -> {
                    TagVO tagVO = new TagVO();
                    tagVO.setTagName(tag.getTag());
                    tagVO.setTagValid(1);
                    return tagVO;
                }).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(labels)) {
                Set<TagVO> tagSet = labels.stream()
                        .collect(Collectors.groupingBy(TagVO::getTagName))
                        .values()
                        .stream()
                        .map(tagList -> tagList.stream()
                                .max(Comparator.comparing(tag -> tag.getTagValid() == 1 ? 1 : 0))
                                .orElse(null))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                vo.setTagList(tagSet);
                vo.setIsListed(!CollUtil.intersection(tagSet.stream().map(TagVO::getTagName).collect(Collectors.toSet()), Constants.COMPANY_LISTED_TAG)
                        .isEmpty());
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            // 股票信息
            final List<CompanyStock> companyStockList = companyStockService.list(Wrappers.<CompanyStock>lambdaQuery()
                    .eq(CompanyStock::getCid, id));
            if (CollUtil.isNotEmpty(companyStockList)) {
                vo.setStockCode(String.join(",", companyStockList.stream().map(CompanyStock::getStockCode)
                        .collect(Collectors.toSet())));
                vo.setShortName(String.join(",", companyStockList.stream().map(CompanyStock::getShortName)
                        .collect(Collectors.toSet())));
            }
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            //根据企业id获取直接投资记录（根据被投资企业去重）
            final List<CompanyInvest> companyInvestList = companyInvestMapper.getDirectInvestByCid(id, null, null, 1);
            if (CollUtil.isNotEmpty(companyInvestList)) {
                // 投资地区分布
                final Map<String, List<CompanyInvest>> collect = companyInvestList.stream()
                        .filter(companyInvest -> CharSequenceUtil.isNotBlank(companyInvest.getInvestProvinceCode())).collect(Collectors.groupingBy(CompanyInvest::getInvestProvinceCode));
                final Set<String> provinceCodeList = collect.keySet();
                if (CollUtil.isNotEmpty(provinceCodeList)) {
                    final List<DmDivision> dmDivisionList = dmDivisionService.list(Wrappers.<DmDivision>lambdaQuery()
                            .in(DmDivision::getCode, provinceCodeList));
                    final Map<String, String> codeNameMap = dmDivisionList.stream().collect(Collectors.toMap(DmDivision::getCode, DmDivision::getShortName));
                    final List<NameCountVO> investAreaList = new ArrayList<>(collect.size());
                    for (final Map.Entry<String, List<CompanyInvest>> entry : collect.entrySet()) {
                        final String key = entry.getKey();
                        final List<CompanyInvest> value = entry.getValue();
                        investAreaList.add(new NameCountVO(codeNameMap.get(key), (long) value.size()));
                    }
                    vo.setInvestAreaList(investAreaList.stream().sorted(Comparator.comparing(NameCountVO::getCount).reversed()).collect(Collectors.toList()));
                }
                // 被投资企业成立日期分布
                final Map<Date, List<CompanyInvest>> establishDateMap = companyInvestList.stream()
                        .filter(companyInvest -> companyInvest.getInvestedEstablishedDate() != null).collect(Collectors.groupingBy(CompanyInvest::getInvestedEstablishedDate));
                final List<NameCountVO> investEstablishDateList = new ArrayList<>(establishDateMap.size());
                final Map<String, Long> dateMap = new HashMap<>(establishDateMap.size());
                for (final Map.Entry<Date, List<CompanyInvest>> entry : establishDateMap.entrySet()) {
                    final Date key = entry.getKey();
                    final List<CompanyInvest> value = entry.getValue();
                    final String year = DateUtil.format(key, "yyyy");
                    if (dateMap.containsKey(year)) {
                        dateMap.put(year, dateMap.get(year) + value.size());
                    } else {
                        dateMap.put(year, (long) value.size());
                    }
                }
                // 只获取近十年的数据
                final List<String> years = DateHandlerUtil.getLatest10Year();
                years.forEach(year -> {
                    investEstablishDateList.add(new NameCountVO(year, dateMap.getOrDefault(year, 0L)));
                });
                vo.setInvestCompanyDateList(investEstablishDateList);

//                //判断是否用户关注
//                final List<String> followStatusList = selectFollowStatusById("企业",
//                        Collections.singletonList(vo.getId()), userId);
//                if (!CollectionUtils.isEmpty(followStatusList)) {
//                    if (followStatusList.contains(vo.getId())) {
//                        vo.setFollowStatus(1);
//                    } else {
//                        vo.setFollowStatus(0);
//                    }
//                } else {
//                    vo.setFollowStatus(0);
//                }
            }
        }, docExecutor));
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[5]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        return vo;
    }

    @Override
    public EsPageResult getLibraryList(@NonNull MultidimensionalQuery query) {
        String keyword = query.getKeyword();
        if (CharSequenceUtil.isNotBlank(keyword) && keyword.length() > 1024) {
            keyword = keyword.substring(0, 1024);
        }
        final Boolean isNeedCorrect = query.getIsNeedCorrect();
        final Boolean isHighlight = query.getIsHighlight();
        final Map<String, List<String>> termQueries = query.getTermQueries();
        String index = query.getIndex();
        if (StrUtil.isBlank(index)) {
            index = EsIndexEnum.getEsIndexByType(query.getType());
            query.setIndex(index);
        }
        if (StrUtil.isBlank(index)) {
            throw new BusinessException("找不到对应的索引！");
        }
        // 初始化查询参数
        final BoolQuery.Builder queryBoolQuery = query.getBoolQuery();
        final Highlight.Builder highlightBuilder = null;
        final BoolQuery.Builder boolQuery;
        if (queryBoolQuery != null) {
            boolQuery = queryBoolQuery;
        } else {
            boolQuery = new BoolQuery.Builder();
        }
        final List<KeywordSearchProperty> keywordSearchList = keywordSearchProperties.getKeywordSearchList(index);
        // 1.构建boolQuery
        // 1.1 关键词搜索
        final String actualKeyword = buildKeywordBoolQuery(index, keyword, boolQuery, highlightBuilder, keywordSearchList, isNeedCorrect);
        // 1.2 构建特殊业务处理的boolQuery
        specialBuildBoolQuery(boolQuery, termQueries);
        // 1.3.筛选项搜索
        buildTermBoolQuery(index, termQueries, boolQuery);
        // 2.构建searchSource
        // 2.1 针对某些库的特殊逻辑处理
//        query.setSort(specialSortForLibrarySearch(boolQuery, query.getSort(), keyword));
        // 2.2 构建searchSource
        final SearchRequest.Builder searchSource =
                buildSearchSource(boolQuery, highlightBuilder, query);
        searchSource.trackTotalHits(TrackHits.of(f -> f.enabled(true)));
        // 2.3 根据业务需求定制searchSource
//        specialSearchSourceForLibrarySearch(index, searchSource, termQueries, keyword, boolQuery);
        // 3.获取结果
        final SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSource, index, keyword);
        final EsPageResult pageResult = elasticsearchHelper
                .buildPageResultWithHighlight(searchResponse, keywordSearchList, keyword, isHighlight, index);
        // 4.特殊业务逻辑处理
        final EsPageResult esPageResult = specialDealWithLibraryPageResult(pageResult);
        // 5.判断是否需要纠错
        if (CharSequenceUtil.isNotBlank(actualKeyword)) {
            final ErrorCorrectionPageResult errorCorrectionPageResult = new ErrorCorrectionPageResult(esPageResult);
            errorCorrectionPageResult.setOriginalKeyword(keyword);
            errorCorrectionPageResult.setActualKeyword(actualKeyword);
            return errorCorrectionPageResult;
        }
        return esPageResult;
    }

    public void specialBuildBoolQuery(final BoolQuery.Builder boolQuery, final Map<String, List<String>> termQueries) {
        generateLabelsQuery(boolQuery, termQueries);
        boolQuery.filter(f -> f.exists(e -> e.field("name")));
    }

    /**
     * 生成标签查询条件
     */
    private void generateLabelsQuery(final BoolQuery.Builder boolQuery, final Map<String, List<String>> termQueries) {
        if (termQueries != null && termQueries.containsKey("labels")) {
            final List<String> labels = termQueries.get("labels");
            if (CollUtil.isNotEmpty(labels)) {
                if (labels.contains("上市企业")) {
                    labels.remove("上市企业");
                    labels.add("A股");
                    labels.add("B股");
                    labels.add("港股");
                    labels.add("中概股");
                }
                if (labels.contains("专精特新企业")) {
                    labels.add("专精特新小巨人企业");
                }
                List<FieldValue> fieldValues = labels.stream().map(FieldValue::of).collect(Collectors.toList());
                boolQuery.filter(q -> q.terms(t -> t.field("labels").terms(ts -> ts.value(fieldValues))));
            }
            termQueries.remove("labels");
        }
    }

    /**
     * 构建多维度筛选项的boolQuery
     */
    private void buildTermBoolQuery(final String index, final Map<String, List<String>> termQueries,
                                    final BoolQuery.Builder boolQuery) {
        final List<CustomIndexNavSetting> indexNavigationSettings =
                navigationSettings.getIndexNavSettingMap().get(index);
        if (termQueries != null && CollUtil.isNotEmpty(termQueries.keySet())) {
            for (final Map.Entry<String, List<String>> entry : termQueries.entrySet()) {
                final String field = entry.getKey();
                final List<String> value = entry.getValue();
                // 1.处理参数
                CustomIndexNavSetting useSetting = null;
                if (CollUtil.isNotEmpty(indexNavigationSettings)) {
                    final List<CustomIndexNavSetting> navSettings =
                            indexNavigationSettings.stream().filter(set -> set.getField().equals(field)).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(navSettings)) {
                        useSetting = navSettings.get(0);
                    }
                }
                if (useSetting == null) {
                    // 如果不存在配置，那么新建一个默认配置
                    useSetting = new CustomIndexNavSetting();
                    useSetting.setCustomType(CustomTypeEnum.NORMAL.getType());
                    useSetting.setField(field);
                    useSetting.setSearchType(SearchTypeConstants.USUAL_QUERY);
                }
                buildBoolQueryForNavigationField(index, field, value, useSetting, boolQuery, Operator.And);
            }
        }
    }

    protected void specialSearchSourceForLibrarySearch(final String esIndex, final SearchRequest.Builder searchSource,
                                                       final Map<String, List<String>> termQueries, final String keyword, final BoolQuery.Builder boolQuery) {
        BoolQuery.Builder newBoolQuery = new BoolQuery.Builder();
        newBoolQuery.must(boolQuery.build().must());
        // 使用 Elasticsearch 8 API 创建 function_score 查询
        Query functionScoreQuery = Query.of(q -> q
                .functionScore(fs -> fs
                        .query(newBoolQuery.build()._toQuery())
                        .functions(f -> f
                                .filter(Query.of(filter -> filter.term(t -> t.field("types.name").value("注销"))))
                                .weight(0.1)
                        )
                        .scoreMode(FunctionScoreMode.Multiply)
                )
        );

        searchSource.query(functionScoreQuery);
    }

    public String specialSortForLibrarySearch(final BoolQuery.Builder boolQuery, final String sort, final String keyword) {
        // 未做搜索的时候企业库增加经营评价降序排列
        if (StrUtil.isEmpty(keyword)) {
            if (StrUtil.isBlank(sort)) {
                return "business_score:desc";
            }
        }
        // 对企业库实施特殊的排序逻辑
        if (StrUtil.isNotBlank(keyword)) {
            // 构建复合排序策略
            StringBuilder customSort = new StringBuilder();

            // 首先按相关性排序（关键词匹配度）
//            customSort.append(ElasticsearchBuilder.ES_SCORE).append(":desc");

//            // 然后按企业状态排序（在业/存续企业优先，注销企业排在后面）
//            customSort.append(",status_sort:desc");
//
//            // 按技术分数和商业分数排序（分数越高排名越靠前）
//            customSort.append(",technical_score:desc,business_score:desc");
//
//            // 按注册资本排序（注册资本越高排名越靠前）
//            customSort.append(",capital_weight:desc,regist_capi_value_cal:desc");
//
//            // 最后按资质标签数量排序
//            customSort.append(",qualification_tags_count:desc");

            return customSort.toString();
        }
        return sort;
    }

    /**
     * 构建searchSource
     */
    private SearchRequest.Builder buildSearchSource(final BoolQuery.Builder boolQuery,
                                                    final Highlight.Builder highlightBuilder, final MultidimensionalQuery query) {
        final SearchSourceQuery sourceQuery = new SearchSourceQuery();
        sourceQuery.setKeyword(query.getKeyword());
        sourceQuery.setHighlightBuilder(highlightBuilder);
        sourceQuery.setQueryBuilder(boolQuery);
        sourceQuery.setSort(query.getSort());
        sourceQuery.setPageNum(query.getPageNum());
        sourceQuery.setPageSize(query.getPageSize());
        List<String> requiredList = query.getRequiredList();
        if (CollUtil.isEmpty(requiredList)) {
            requiredList = keywordSearchProperties.getRequiredList(query.getIndex());
        }
        if (CollUtil.isNotEmpty(requiredList)) {
            sourceQuery.setIncludes(requiredList.toArray(new String[0]));
        }
        return ElasticsearchBuilder.buildSearchSource(sourceQuery);
    }

    /**
     * 根据字段的自定义类型和筛选类型进行导航栏参数查询语句的构建
     */
    protected void buildBoolQueryForNavigationField(final String index, final String field,
                                                    final List<String> valueList,
                                                    final CustomIndexNavSetting indexNavigationSetting,
                                                    final BoolQuery.Builder boolQuery,
                                                    final Operator operator) {
        if (StrUtil.isBlank(field) || CollUtil.isEmpty(valueList)) {
            return;
        }
        final Integer searchType = indexNavigationSetting.getSearchType();
        final List<String> matchTypes = indexNavigationSetting.getMatchTypes();
        final BasicBoolQuery queryBO = new BasicBoolQuery();
        queryBO.setBoolQueryBuilder(boolQuery);
        queryBO.setField(field);
        queryBO.setValues(valueList);
        queryBO.setMatchTypes(matchTypes);
        queryBO.setFieldKeyword(indexNavigationSetting.getFieldKeyword());
        queryBO.setSearchType(searchType);
        queryBO.setOperator(operator);
        queryBO.setIndex(index);
        queryBO.setOperator(Operator.And);
        // 3.将前端传入的value转换为实际查询的value
        final List<String> rangeFields = navigationSettings
                .getRangeFields(indexNavigationSetting, valueList, searchType);
        if (!CollUtil.isEmpty(rangeFields)) {
            queryBO.setValues(rangeFields);
        }
        // 4.构建查询
        ElasticsearchBuilder.buildBoolQuery(queryBO);
    }

    /**
     * 构建关键词搜索的boolQuery，如果关键词需要纠错，那么返回纠错后的关键词，如果不需要则返回null
     */
    protected String buildKeywordBoolQuery(final String index,
                                           String keyword,
                                           final BoolQuery.Builder boolQuery,
                                           final Highlight.Builder highlightBuilder,
                                           final List<KeywordSearchProperty> keywordSearchList,
                                           final Boolean isNeedCorrect) {

        final BasicBoolQuery queryBO = new BasicBoolQuery();

        if (EsIndexEnum.COMPANY.getEsIndex().equals(index)) {

            // 调整权重 - 企业名：注册资本：法人 = 7:2:1
            List<KeywordSearchProperty> adjustedKeywordSearchList = adjustCompanySearchWeights(keywordSearchList);
            queryBO.setKeywordFields(adjustedKeywordSearchList);

            // 设置企业状态过滤条件，大幅提高在业和存续企业的权重，注销企业排在最后
            BoolQuery.Builder statusQueryBuilder = new BoolQuery.Builder();
            statusQueryBuilder.should(q -> q.term(t -> t.field("status").value("在业").boost(20.0f)));
            statusQueryBuilder.should(q -> q.term(t -> t.field("status").value("存续").boost(50.0f)));
            statusQueryBuilder.should(q -> q.term(t -> t.field("status").value("开业").boost(20.0f)));
            boolQuery.should(statusQueryBuilder.build()._toQuery());

            // 使用must_not降低注销企业的排名
            BoolQuery.Builder cancelledStatusQueryBuilder = new BoolQuery.Builder();
            cancelledStatusQueryBuilder.should(q -> q.term(t -> t.field("status").value("注销")));
            cancelledStatusQueryBuilder.should(q -> q.term(t -> t.field("status").value("吊销")));
            cancelledStatusQueryBuilder.should(q -> q.term(t -> t.field("status").value("除名")));
            cancelledStatusQueryBuilder.should(q -> q.term(t -> t.field("status").value("撤销")));
            cancelledStatusQueryBuilder.should(q -> q.term(t -> t.field("status").value("责令关闭")));
            cancelledStatusQueryBuilder.should(q -> q.term(t -> t.field("status").value("已歇业")));
            // 使用must_not将注销企业降权
            boolQuery.mustNot(cancelledStatusQueryBuilder.build()._toQuery());

            // 将不同类型的条件分开处理，以便更精确地控制各个因素的权重

            // 1. 企业标签和规模 - 单独处理企业标签和规模
            BoolQuery.Builder tagsQueryBuilder = new BoolQuery.Builder();
            tagsQueryBuilder.should(q -> q.exists(e -> e.field("labels").boost(100.0f)));
            tagsQueryBuilder.should(q -> q.term(t -> t.field("company_scale").value("大型").boost(10.0f)));
            // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
            tagsQueryBuilder.minimumShouldMatch("1");
            boolQuery.should(tagsQueryBuilder.boost(1.0f).build()._toQuery());
            boolQuery.should(q -> q.term(t -> t.field("labels").value("国家级").boost(10.0f)));

            // 2. 技术得分 - 单独处理技术得分，确保高分企业获得更高权重
            boolQuery.should(q -> q.range(r -> r.field("technical_score").gte(JsonData.of(90)).boost(60.0f)));
            BoolQuery.Builder technicalScoreQueryBuilder = new BoolQuery.Builder();
            technicalScoreQueryBuilder.should(q -> q.range(r -> r.field("technical_score").gte(JsonData.of(80)).lt(JsonData.of(90)).boost(50.0f)));
            technicalScoreQueryBuilder.should(q -> q.range(r -> r.field("technical_score").gte(JsonData.of(70)).lt(JsonData.of(80)).boost(30.0f)));
            technicalScoreQueryBuilder.should(q -> q.range(r -> r.field("technical_score").gte(JsonData.of(60)).lt(JsonData.of(70)).boost(20.0f)));
            technicalScoreQueryBuilder.should(q -> q.exists(e -> e.field("technical_score").boost(15.0f)));
            // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
            technicalScoreQueryBuilder.minimumShouldMatch("0");
            boolQuery.should(technicalScoreQueryBuilder.boost(1.0f).build()._toQuery());

            // 3. 商业得分 - 单独处理商业得分
            boolQuery.should(q -> q.range(r -> r.field("business_score").gte(JsonData.of(90)).boost(60.0f)));
            BoolQuery.Builder businessScoreQueryBuilder = new BoolQuery.Builder();
            businessScoreQueryBuilder.should(q -> q.range(r -> r.field("business_score").gte(JsonData.of(80)).lt(JsonData.of(90)).boost(50.0f)));
            businessScoreQueryBuilder.should(q -> q.range(r -> r.field("business_score").gte(JsonData.of(70)).lt(JsonData.of(80)).boost(30.0f)));
            businessScoreQueryBuilder.should(q -> q.range(r -> r.field("business_score").gte(JsonData.of(60)).lt(JsonData.of(70)).boost(20.0f)));
            businessScoreQueryBuilder.should(q -> q.exists(e -> e.field("business_score").boost(15.0f)));
            // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
            businessScoreQueryBuilder.minimumShouldMatch("0");
            boolQuery.should(businessScoreQueryBuilder.boost(1.0f).build()._toQuery());

            // 4. 网站和Logo - 单独处理网站和Logo信息
            BoolQuery.Builder webInfoQueryBuilder = new BoolQuery.Builder();
            webInfoQueryBuilder.should(q -> q.exists(e -> e.field("website").boost(15.0f)));
            webInfoQueryBuilder.should(q -> q.exists(e -> e.field("logo_source").boost(100.0f)));
            // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
            webInfoQueryBuilder.minimumShouldMatch("0");
            boolQuery.should(webInfoQueryBuilder.build()._toQuery());

            // 5. 注册资本 - 单独处理注册资本，确保高注册资本企业获得更高权重
            boolQuery.should(q -> q.range(r -> r.field("regist_capi_value_cal").gte(JsonData.of(1000000000)).boost(30.0f))); // 10亿以上
            BoolQuery.Builder registCapiQueryBuilder = new BoolQuery.Builder();
            registCapiQueryBuilder.should(q -> q.range(r -> r.field("regist_capi_value_cal").gte(JsonData.of(500000000)).lt(JsonData.of(1000000000)).boost(25.0f)));  // 5亿-10亿
            registCapiQueryBuilder.should(q -> q.range(r -> r.field("regist_capi_value_cal").gte(JsonData.of(10000000)).lt(JsonData.of(500000000)).boost(20.0f)));  // 1亿-5亿
            registCapiQueryBuilder.should(q -> q.range(r -> r.field("regist_capi_value_cal").gte(JsonData.of(50000000)).lt(JsonData.of(100000000)).boost(15.0f)));   // 5千万-1亿
            registCapiQueryBuilder.should(q -> q.range(r -> r.field("regist_capi_value_cal").gte(JsonData.of(10000000)).lt(JsonData.of(50000000)).boost(10.0f)));  // 1千万-5千万
            registCapiQueryBuilder.should(q -> q.range(r -> r.field("regist_capi_value_cal").gte(JsonData.of(5000000)).lt(JsonData.of(10000000)).boost(5.0f)));  // 500万-1千万
            registCapiQueryBuilder.should(q -> q.range(r -> r.field("regist_capi_value_cal").gte(JsonData.of(1000000)).lt(JsonData.of(5000000)).boost(2.0f)));  // 100万-500万
            registCapiQueryBuilder.minimumShouldMatch("0");
            boolQuery.should(registCapiQueryBuilder.boost(1.0f).build()._toQuery());

            // 6. 园区标签
            BoolQuery.Builder parkQueryBuilder = new BoolQuery.Builder();
            parkQueryBuilder.should(q -> q.exists(e -> e.field("park").boost(120.0f)));
            // 设置最小匹配数为0，确保不满足条件时不会影响其他评分
//            parkQueryBuilder.minimumShouldMatch("0");
            boolQuery.should(parkQueryBuilder.build()._toQuery());

            queryBO.setBoolQueryBuilder(boolQuery);
            queryBO.setOperator(Operator.And);
            queryBO.setIndex(index);
            ElasticsearchBuilder.buildBoolQuery(queryBO);
        }

        boolean isEdit = false;
        if (StrUtil.isBlank(keyword)) {
            return null;
        }
        if (isNeedCorrect) {
            final String correctKeyword = aliyunUtils.aliyunNlpCorrect(keyword);
            if (CharSequenceUtil.isNotBlank(correctKeyword)) {
                keyword = correctKeyword;
                isEdit = true;
            }
        }

        // 对于企业库搜索，增加曾用名检索和更合理的检索权重
        if (EsIndexEnum.COMPANY.getEsIndex().equals(index)) {
            // 设置高亮结果
            queryBO.setKeyword(keyword);

            // 处理并过滤包含停用词的关键词
            final String processedKeyword = processKeywordWithStopWords(keyword);
            queryBO.setKeyword(processedKeyword);

            // 添加企业名称搜索，大幅提高完全匹配的权重
            BoolQuery.Builder nameQueryBuilder = new BoolQuery.Builder();

            // 如果关键词在企业名称的开头，给予更高权重
            nameQueryBuilder.should(q -> q.matchPhrase(mp -> mp.field("name").query(processedKeyword).boost(10.0f)));
            // 这里不用match的话，有些公司就搜索不到，比如“中联重科股份”，但是用了match的话，就，搜索“量知数据科技有限公司”第一位却是“宁德时代新能源科技股份有限公司”，只要文档包含了 "中联" 和 "股份" 这两个词元（即使没有"重科"），这个查询也能匹配成功。相比纯粹的 OR，它要求匹配更多的词，相关性更高。需要根据实际测试调整百分比或具体数字。
            // _analyze API 结果显示，"中联重科股份有限公司" 这个名称在使用 ik_max_word 分词器后，被拆分成了以下主要词元（Tokens）：
            //["中联", "重", "科股", "股份有限公司", "股份有限", "股份", "有限公司", "有限", "公司"]
            // 2. 分词匹配，但不需要所有词都匹配 (使用 minimum_should_match)
            nameQueryBuilder.should(q -> q.matchPhrase(m -> m.field("name").query(processedKeyword)))
                    // .operator(Operator.OR) // OR 是默认的，可以不写
                    .minimumShouldMatch("75%")// <<< 关键：要求至少匹配75%的查询词元
                    // 或者 .minimumShouldMatch("2") 对于 "中联 重科 股份" 这个3词查询，要求至少匹配2个
                    .boost(3.0f); // 给一个中低权重，低于 phrase match

            // 地址搜索
            nameQueryBuilder.should(q -> q.matchPhrase(mp -> mp.field("address").query(processedKeyword).boost(5.0f)));
            nameQueryBuilder.should(q -> q.matchPhrase(mp -> mp.field("address").query(processedKeyword)))
                    .minimumShouldMatch("75%")// <<< 关键：要求至少匹配75%的查询词元
                    // 或者 .minimumShouldMatch("2") 对于 "中联 重科 股份" 这个3词查询，要求至少匹配2个
                    .boost(2.0f);

            // 确保 name/address 中至少有一个条件匹配
            nameQueryBuilder.minimumShouldMatch("1"); // <<< 在这个内部的 bool 查询中添加

            boolQuery.must(nameQueryBuilder.build()._toQuery());

            // 控制搜索结果数量，设置相关度阈值
            boolQuery.minimumShouldMatch("1");

            queryBO.setBoolQueryBuilder(boolQuery);
            queryBO.setOperator(Operator.And);
            queryBO.setIndex(index);
            ElasticsearchBuilder.buildBoolQuery(queryBO);
        } else {
            // 原有的搜索逻辑保持不变
            queryBO.setKeyword(keyword);
            queryBO.setKeywordFields(keywordSearchList);
            queryBO.setOperator(Operator.And);
            queryBO.setBoolQueryBuilder(boolQuery);
            queryBO.setIndex(index);
            ElasticsearchBuilder.buildBoolQuery(queryBO);
        }

        if (CollUtil.isNotEmpty(keywordSearchList)) {
            final Set<String> fields = new HashSet<>();
            for (final KeywordSearchProperty subFieldProp : keywordSearchList) {
                // keyword类型字段不使用es自带的高亮
                if (subFieldProp.getIsHighlight() && !Objects.equals(subFieldProp.getType(), "keyword")) {
                    final String field = subFieldProp.getField();
                    fields.add(field);
                }
            }
            if (highlightBuilder != null) {
                ElasticsearchBuilder.setHighlightBuilder(highlightBuilder, fields);
            }
        }
        if (isEdit) {
            return keyword;
        }
        return null;
    }

    /**
     * 处理关键词，降低停用词权重
     *
     * @param keyword 原始关键词
     * @return 处理后的关键词
     */
    private String processKeywordWithStopWords(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return keyword;
        }

        // 定义常见的企业名称停用词
        String[] stopWords = {"公司", "股份", "有限", "责任", "集团", "企业", "厂", "合伙"};

        String processedKeyword = keyword;
        for (String stopWord : stopWords) {
            // 不完全移除停用词，而是降低其权重（替换为更短的形式）
            if (processedKeyword.contains(stopWord)) {
                processedKeyword = processedKeyword.replace(stopWord, "");
            }
        }

        return processedKeyword.trim();
    }

    /**
     * 调整企业库搜索字段的权重
     *
     * @param originalList 原始搜索字段列表
     * @return 调整权重后的搜索字段列表
     */
    private List<KeywordSearchProperty> adjustCompanySearchWeights(List<KeywordSearchProperty> originalList) {
        if (CollUtil.isEmpty(originalList)) {
            return originalList;
        }

        List<KeywordSearchProperty> adjustedList = new ArrayList<>(originalList.size());

        for (KeywordSearchProperty property : originalList) {
            KeywordSearchProperty newProperty = new KeywordSearchProperty();
            newProperty.setField(property.getField());
            newProperty.setType(property.getType());
            newProperty.setIsHighlight(property.getIsHighlight());

            // 调整各字段权重 - 企业名：注册资本：法人 = 7:2:1
            if ("name".equals(property.getField())) {
                newProperty.setBoost(7.0f);
            } else if ("regist_capi_value_cal".equals(property.getField())) {
                newProperty.setBoost(2.0f);
            } else if ("legal_person".equals(property.getField())) {
                newProperty.setBoost(1.0f);
            } else if ("used_name".equals(property.getField())) {
                // 添加曾用名字段并设置权重
                newProperty.setBoost(6.0f);
            } else {
                newProperty.setBoost(property.getBoost());
            }

            adjustedList.add(newProperty);
        }

        // 确保曾用名字段被添加
        boolean hasFormerNames = adjustedList.stream().anyMatch(p -> "used_name".equals(p.getField()));
        if (!hasFormerNames) {
            KeywordSearchProperty formerNamesProperty = new KeywordSearchProperty();
            formerNamesProperty.setField("used_name");
            formerNamesProperty.setType("text");
            formerNamesProperty.setIsHighlight(true);
            formerNamesProperty.setBoost(6.0f);
            adjustedList.add(formerNamesProperty);
        }

        return adjustedList;
    }


    public EsPageResult specialDealWithLibraryPageResult(final EsPageResult pageResult) {
        final List<Map<String, Object>> list = pageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return pageResult;
        }
        final List<String> companyIdList = new ArrayList<>(list.size());
        for (final Map<String, Object> sourceAsMap : list) {
            companyIdList.add((String) sourceAsMap.get("id"));
            final Object tags = sourceAsMap.get("tags");
            if (tags instanceof List) {
                final Set<String> labelSet = new HashSet<>();
                ((List) tags).forEach(
                        tag -> {
                            if (tag instanceof Map) {
                                final Object tagObject = ((Map) tag).get("name");
                                if (tagObject != null) {
                                    labelSet.add((String) tagObject);
                                }
                            }
                        }
                );
                sourceAsMap.remove("tags");
                sourceAsMap.put("labels", labelSet);
            }
            sourceAsMap.put("region", Constants.getBelongArea((String) sourceAsMap.get("province"), (String) sourceAsMap.get("city"), null));
        }
        // 企业库额外查询字段
        final List<Company> companies = this.listByIds(companyIdList);
        final Map<String, Company> map = companies.stream().collect(Collectors.toMap(Company::getId, Function.identity()));
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(list.size());

        for (final Map<String, Object> sourceAsMap : list) {
            futureList.add(CompletableFuture.runAsync(() -> {
                if (map.containsKey((String) sourceAsMap.get("id"))) {
                    final String id = (String) sourceAsMap.get("id");
                    final Company company = map.get(id);
                    sourceAsMap.put("regist_capi", company.getRegistCapi());
                    sourceAsMap.put("logo_source", company.getLogoSource());
                    sourceAsMap.put("address", company.getAddress());
                    sourceAsMap.put("tel", company.getTel());
                    sourceAsMap.put("website", company.getWebsite());
                }
            }, docExecutor));
        }

        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[list.size()]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }

        return pageResult;
    }

    /**
     * 按企业名称模糊搜索
     * @param keyword 关键词
     * @return 企业名称列表
     */
    @Override
    public List<CompanyItemVO> searchByName(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return Collections.emptyList();
        }
        List<Company> companyList = this.list(Wrappers.<Company>lambdaQuery()
                .select(Company::getId, Company::getName)
                .like(Company::getName, keyword)
                .last("limit 50"));
        if (CollUtil.isEmpty(companyList)) {
            return Collections.emptyList();
        }
        return companyList.stream().map(c -> {
            CompanyItemVO vo = new CompanyItemVO();
            BeanUtils.copyProperties(c, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 新增企业，同时写入MySQL和ES
     * @param companyName 企业名称
     * @return 生成的企业ID
     */
    @Override
    public String addCompany(String companyName) {
        // 生成32位uuid并拼接前缀
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String companyId = "instance_add_company-" + uuid;
        // 构造Company对象
        Company company = new Company();
        company.setId(companyId);
        company.setName(companyName);
        company.setCreateTime(new Date());
        company.setUpdateTime(new Date());
        // 写入MySQL
        this.save(company);

        // 写入ES
        cn.hutool.json.JSONObject esDoc = new cn.hutool.json.JSONObject();
        esDoc.put("id", companyId);
        esDoc.put("name", companyName);
        esDoc.put("createTime", company.getCreateTime());
        esDoc.put("updateTime", company.getUpdateTime());
        // 可根据ES schema补充其它字段
        elasticsearchHelper.index(EsIndexEnum.COMPANY.getEsIndex(), companyId, esDoc.toString());

        return companyId;
    }

    @Override
    public String addCompanyInfo(Company company) {
        // 生成32位uuid并拼接前缀
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String companyId = "instance_add_company-" + uuid;
        // 构造Company对象
        company.setId(companyId);
        company.setCreateTime(new Date());
        company.setUpdateTime(new Date());
        // 写入MySQL
        this.save(company);

        // 写入ES
        cn.hutool.json.JSONObject esDoc = new cn.hutool.json.JSONObject();
        esDoc.put("id", companyId);
        esDoc.put("name", company.getName());
        esDoc.put("address", company.getAddress());
        esDoc.put("legal_person", company.getLegalPerson());
        esDoc.put("credit_code", company.getCreditCode());
        esDoc.put("registration", company.getRegistration());
        esDoc.put("createTime", company.getCreateTime());
        esDoc.put("updateTime", company.getUpdateTime());
        // 可根据ES schema补充其它字段
        elasticsearchHelper.index(EsIndexEnum.COMPANY.getEsIndex(), companyId, esDoc.toString());

        return companyId;
    }

    @Override
    public CompanyInfoVO getCompanyInfo(String companyId) {
        Company company = this.getById(companyId);
        CompanyInfoVO vo = new CompanyInfoVO();
        if (company != null) {
            BeanUtils.copyProperties(company, vo);
        }
        return vo;
    }

    @Override
    public boolean updateCompanyRegistration(String companyId, String registration) {
        try {
            log.info("开始修改企业注册方式，企业ID：{}，注册方式：{}", companyId, registration);

            // 验证注册方式参数
            if (!"实体注册".equals(registration) && !"虚拟注册".equals(registration)) {
                log.error("注册方式参数无效：{}，只支持'实体注册'或'虚拟注册'", registration);
                return false;
            }

            // 验证企业是否存在
            Company company = this.getById(companyId);
            if (company == null) {
                log.error("企业不存在，企业ID：{}", companyId);
                return false;
            }

            // 构建更新的字段Map
            Map<String, Object> updateFields = new HashMap<>();
            updateFields.put("registration", registration);

            // 更新ES中的registration字段
            elasticsearchHelper.updateInfo(EsIndexEnum.COMPANY.getEsIndex(), companyId, updateFields);

            log.info("企业注册方式修改成功，企业ID：{}，注册方式：{}", companyId, registration);
            return true;

        } catch (Exception e) {
            log.error("修改企业注册方式失败，企业ID：{}，注册方式：{}", companyId, registration, e);
            return false;
        }
    }
}
