package com.quantchi.application.service;

import com.quantchi.application.model.bo.ApplicationDecorationBO;
import com.quantchi.application.model.entity.ApplicationDecoration;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.application.model.vo.ApplicationDecorationVO;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <p>
 * 装修申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IApplicationDecorationService extends IService<ApplicationDecoration> {

    void applicationDecorate(@Valid ApplicationDecorationBO bo);

    ApplicationDecorationVO applicationDecorateDetail(String applicationId);

    /**
     * 批量下载装修申请相关文件
     * @param applicationId 申请ID
     * @param response HTTP响应对象
     */
    void downloadDecorationFiles(String applicationId, HttpServletResponse response);
}
