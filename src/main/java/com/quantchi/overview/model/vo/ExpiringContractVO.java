package com.quantchi.overview.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 合同即将到期企业列表展示对象
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@ApiModel(value = "ExpiringContractVO", description = "合同即将到期企业列表展示对象")
public class ExpiringContractVO {

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("入驻时间")
    private LocalDate entryTime;

    @ApiModelProperty("入驻房号")
    private String roomNumber;

    @ApiModelProperty("剩余天数")
    private Integer remainingDays;

    @ApiModelProperty("合同金额")
    private Integer contractAmount;
    
    @ApiModelProperty("续租状态")
    private String renewalStatus;
}
