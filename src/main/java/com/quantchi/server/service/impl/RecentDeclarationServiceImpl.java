package com.quantchi.server.service.impl;

import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.common.enums.EsIndexEnum;
import com.quantchi.common.helper.ElasticsearchHelper;
import com.quantchi.server.model.entity.RecentDeclaration;
import com.quantchi.server.mapper.RecentDeclarationMapper;
import com.quantchi.server.model.vo.ParkDynamicsVO;
import com.quantchi.server.service.IRecentDeclarationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RecentDeclarationServiceImpl extends ServiceImpl<RecentDeclarationMapper, RecentDeclaration> implements IRecentDeclarationService {

    private final ElasticsearchHelper elasticsearchHelper;

    @Override
    public IPage<RecentDeclaration> page(final int pageNum, final int pageSize) {
        // 创建分页对象
        final Page<RecentDeclaration> page = new Page<>(pageNum, pageSize);
        // 创建查询条件
        final LambdaQueryWrapper<RecentDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        // 按照创建时间倒序排列
        queryWrapper.orderByDesc(RecentDeclaration::getCreateTime);
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }

    @Override
    public IPage<ParkDynamicsVO> parkDynamics(int pageNum, int pageSize) {
        // 创建分页对象
        final Page<ParkDynamicsVO> page = new Page<>(pageNum, pageSize);

        SearchResponse<Map<String, Object>> response = elasticsearchHelper.pageByFields(EsIndexEnum.NEWS.getEsIndex(),
                EsIndexEnum.NEWS.getIncludeFields().split(","), null,
                pageNum, pageSize, EsIndexEnum.NEWS.getSort());

        if (response == null || response.hits() == null) {
            return page;
        }

        List<Hit<Map<String, Object>>> hits = response.hits().hits();

        if (CollectionUtils.isEmpty(hits)) {
            return page;
        }

        List<ParkDynamicsVO> list = new ArrayList<>();

        for (Hit<Map<String, Object>> hit : hits) {
            Map<String, Object> source = hit.source();

            if (source == null) {
                continue;
            }

            final ParkDynamicsVO vo = new ParkDynamicsVO();
            vo.setId(Optional.ofNullable(source.get("id")).map(Object::toString).orElse(""));
            vo.setArticleName(Optional.ofNullable(source.get("title")).map(Object::toString).orElse(""));
            vo.setImage(Optional.ofNullable(source.get("images_url")).map(Object::toString).orElse(""));
            vo.setUrl(Optional.ofNullable(source.get("url")).map(Object::toString).orElse(""));
            vo.setSource(Optional.ofNullable(source.get("channel")).map(Object::toString).orElse(""));
            vo.setPublishTime(Optional.ofNullable(source.get("publish_date")).map(Object::toString).orElse(""));
            list.add(vo);
        }

        page.setRecords(list);
        page.setTotal(response.hits().total().value());
        return page;
    }
}
