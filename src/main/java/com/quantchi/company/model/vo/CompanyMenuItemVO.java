package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 企业画像菜单项VO
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("企业画像菜单项VO")
public class CompanyMenuItemVO {

    @ApiModelProperty("菜单标签")
    private String label;

    @ApiModelProperty("菜单键值")
    private String key;

    @ApiModelProperty("是否显示")
    private Boolean display;

    @ApiModelProperty("子菜单项")
    private List<CompanyMenuItemVO> children;
}
