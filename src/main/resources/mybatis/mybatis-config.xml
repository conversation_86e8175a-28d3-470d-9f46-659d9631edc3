<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <!-- 全局的映射器启用或禁用缓存。 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 全局启用或禁用延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 允许或不允许多种结果集从一个单独的语句中返回 -->
        <setting name="multipleResultSetsEnabled" value="true"/>
        <!-- 使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true"/>
        <!-- 允许JDBC支持生成的键 -->
        <setting name="useGeneratedKeys" value="true"/>
        <!-- 对于批量更新操作缓存SQL以提高性能  -->
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <!-- 设置超时时间 -->
        <setting name="defaultStatementTimeout" value="25000"/>
        <setting name="logImpl" value="SLF4J"/>
        <setting name="logPrefix" value="MYBATIS"/>
        <!-- 驼峰自动映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 指定 MyBatis 使用 STDOUT_LOGGING 输出日志到控制台 -->
        <setting name="logImpl" value="STDOUT_LOGGING" />
    </settings>

</configuration>