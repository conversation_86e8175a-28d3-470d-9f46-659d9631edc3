package com.quantchi.company.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.common.domain.CustomIndexNavSetting;
import com.quantchi.common.model.EsPageResult;
import com.quantchi.common.model.MultidimensionalQuery;
import com.quantchi.company.model.entity.Company;
import com.quantchi.company.model.vo.CompanyInfoVO;
import com.quantchi.company.model.vo.CompanyItemVO;
import com.quantchi.company.model.vo.CompanyMenuItemVO;
import com.quantchi.company.model.vo.CompanyVO;
import lombok.NonNull;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
public interface ICompanyService extends IService<Company> {

    List<CustomIndexNavSetting> getNavSetting();

    CompanyVO baseInfo(String id);

    EsPageResult getLibraryList(@NonNull MultidimensionalQuery query);

    /**
     * 按企业名称模糊搜索
     * @param keyword 关键词
     * @return 企业id和名称列表
     */
    List<CompanyItemVO> searchByName(String keyword);

    /**
     * 新增企业，同时写入MySQL和ES
     */
    String addCompany(String companyName);

    /**
     * 新增企业，同时写入MySQL和ES
     */
    String addCompanyInfo(Company company);

    /**
     * 查询企业关键信息
     * @param companyId 企业ID
     * @return CompanyInfoVO
     */
    CompanyInfoVO getCompanyInfo(String companyId);

    /**
     * 修改企业注册方式
     * @param companyId 企业ID
     * @param registration 注册方式
     * @return 是否修改成功
     */
    boolean updateCompanyRegistration(String companyId, String registration);
}
