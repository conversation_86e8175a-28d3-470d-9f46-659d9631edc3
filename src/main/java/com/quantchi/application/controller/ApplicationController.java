package com.quantchi.application.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.quantchi.application.model.bo.*;
import com.quantchi.application.model.entity.MoveOutReason;
import com.quantchi.application.model.vo.*;
import com.quantchi.application.service.IApplicationDecorationService;
import com.quantchi.application.service.IApplicationService;
import com.quantchi.application.service.impl.ApplicationMoveInServiceImpl;
import com.quantchi.application.service.IApplicationMoveOutService;
import com.quantchi.common.domain.InMemoryCache;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.application.model.vo.ApproverVO;
import com.quantchi.sys.service.impl.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 入驻申请 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@RestController
@RequestMapping("/application")
@Slf4j
@Api(tags = "申请管理接口")
@RequiredArgsConstructor
@Validated
public class ApplicationController {

    private final ApplicationMoveInServiceImpl applicationMoveInService;
    private final IApplicationMoveOutService applicationMoveOutService;
    private final IApplicationDecorationService applicationDecorationService;
    private final IApplicationService applicationService;
    private final SysUserService userService;

//    @PostMapping("/applicationNew")
//    @ApiOperation("新注册企业-入驻申请提交")
//    public ResultInfo<String> applicationNew(@RequestBody @Valid ApplicationNewBO bo) {
//        try {
//            applicationMoveInService.submitNewApplication(bo);
//            return ResultConvert.success("入驻申请提交成功");
//        } catch (Exception e) {
//            log.error("入驻申请提交失败", e);
//            return ResultConvert.error("入驻申请提交失败: " + e.getMessage());
//        }
//    }

    @PostMapping("/applicationMoveIn")
    @ApiOperation("入驻申请提交")
    public ResultInfo<String> applicationMoveIn(@RequestBody @Valid ApplicationMoveInBO bo) {
        try {
            applicationMoveInService.submitMoveInApplication(bo);
            return ResultConvert.success("入驻申请提交成功");
        } catch (Exception e) {
            log.error("入驻申请提交失败", e);
            return ResultConvert.error("入驻申请提交失败: " + e.getMessage());
        }
    }

    @GetMapping("/applicationMoveInDetail")
    @ApiOperation("入驻申请详情")
    public ResultInfo<ApplicationMoveInVO> applicationMoveInDetail(@RequestParam String applicationId) {
        try {
            return ResultConvert.success(applicationMoveInService.applicationMoveInDetail(applicationId));
        } catch (Exception e) {
            log.error("查看入驻申请详情异常", e);
            return ResultConvert.error("查看入驻申请详情异常: " + e.getMessage());
        }
    }

    @GetMapping("/moveInOptionalList")
    @ApiOperation("入驻申请下拉框选项")
    public ResultInfo<MoveInOptionalListVO> moveInOptionalList() {

        return ResultConvert.success(InMemoryCache.getMoveInOptionalListVO());

    }

    @PostMapping("/applicationMoveOut")
    @ApiOperation("迁出申请提交")
    public ResultInfo<String> applicationMoveOut(@RequestBody @Valid ApplicationMoveOutBO bo) {
        try {
            applicationMoveOutService.submitMoveOutApplication(bo);
            return ResultConvert.success("迁出申请提交成功");
        } catch (Exception e) {
            log.error("迁出申请提交失败", e);
            return ResultConvert.error("迁出申请提交失败: " + e.getMessage());
        }
    }

    @GetMapping("/applicationMoveOutDetail")
    @ApiOperation("迁出申请详情")
    public ResultInfo<ApplicationMoveOutVO> applicationMoveOutDetail(@RequestParam String applicationId) {
        try {
            return ResultConvert.success(applicationMoveOutService.applicationMoveOutDetail(applicationId));
        } catch (Exception e) {
            log.error("查看迁出申请详情异常", e);
            return ResultConvert.error("查看迁出申请详情异常: " + e.getMessage());
        }
    }

    @GetMapping("/moveOutOptionalList")
    @ApiOperation("迁出申请下拉框选项")
    public ResultInfo<List<MoveOutReason>> moveOutOptionalList() {

        return ResultConvert.success(InMemoryCache.getMoveOutReasonList());

    }

    @PostMapping("/applicationDecorate")
    @ApiOperation("装修申请提交")
    public ResultInfo<String> applicationDecorate(@RequestBody @Valid ApplicationDecorationBO bo) {
        try {
            applicationDecorationService.applicationDecorate(bo);
            return ResultConvert.success("装修申请提交成功");
        } catch (Exception e) {
            log.error("装修申请提交失败", e);
            return ResultConvert.error("装修申请提交失败: " + e.getMessage());
        }
    }

    @GetMapping("/applicationDecorateDetail")
    @ApiOperation("装修申请详情")
    public ResultInfo<ApplicationDecorationVO> applicationDecorateDetail(@RequestParam String applicationId) {
        try {
            return ResultConvert.success(applicationDecorationService.applicationDecorateDetail(applicationId));
        } catch (Exception e) {
            log.error("查看装修申请详情异常", e);
            return ResultConvert.error("查看装修申请详情异常: " + e.getMessage());
        }
    }

    @GetMapping("/downloadDecorationFiles")
    @ApiOperation("批量下载装修申请相关文件")
    @SaIgnore
    public void downloadDecorationFiles(@RequestParam String applicationId, HttpServletResponse response) {
        try {
            applicationDecorationService.downloadDecorationFiles(applicationId, response);
        } catch (Exception e) {
            log.error("批量下载装修申请相关文件异常", e);
            try {
                // 重置响应状态和内容类型
                response.reset();
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入响应异常", ex);
            }
        }
    }

    @PostMapping("/pageApplicationRecords")
    @ApiOperation("分页查询办事记录")
    public ResultInfo<ApplicationPageVO> pageApplicationRecords(@RequestBody @Valid ApplicationQueryBO queryBO) {
        try {
            return ResultConvert.success(applicationService.pageApplicationRecords(queryBO));
        } catch (Exception e) {
            log.error("分页查询办事记录异常", e);
            return ResultConvert.error("分页查询办事记录异常: " + e.getMessage());
        }
    }

    @GetMapping("/cancelApplication")
    @ApiOperation("撤销办事申请")
    public ResultInfo<String> cancelApplication(@RequestParam String applicationId) {
        try {
            boolean success = applicationService.cancelApplication(applicationId);
            if (success) {
                return ResultConvert.success("撤销办事申请成功");
            } else {
                return ResultConvert.error("撤销办事申请失败，申请可能已处理或不存在");
            }
        } catch (Exception e) {
            log.error("撤销办事申请异常", e);
            return ResultConvert.error("撤销办事申请异常: " + e.getMessage());
        }
    }

    @GetMapping("/downloadMoveInApplication")
    @ApiOperation("下载迁入申请表")
    public void downloadMoveInApplication(@RequestParam String applicationId, HttpServletResponse response) {
        try {
            applicationMoveInService.downloadMoveInApplication(applicationId, response);
        } catch (Exception e) {
            log.error("下载迁入申请表异常", e);
            try {
                // 重置响应状态和内容类型
                response.reset();
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入响应异常", ex);
            }
        }
    }

    @GetMapping("/downloadMoveOutApplication")
    @ApiOperation("下载迁出申请表")
    public void downloadMoveOutApplication(@RequestParam String applicationId, HttpServletResponse response) {
        try {
            applicationMoveOutService.downloadMoveOutApplication(applicationId, response);
        } catch (Exception e) {
            log.error("下载迁出申请表异常", e);
            try {
                // 重置响应状态和内容类型
                response.reset();
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入响应异常", ex);
            }
        }
    }

    @GetMapping("/approverList")
    @ApiOperation("获取审批人列表")
    public ResultInfo<List<OrgApproverVO>> getApproverList() {
        List<OrgApproverVO> approverList = userService.getApproverList();
        return ResultConvert.success(approverList);
    }

    @GetMapping("/downloadDecorationCommitment")
    @ApiOperation("下载装修承诺书")
    public void downloadDecorationCommitment(HttpServletResponse response) {
        try {
            applicationService.downloadDecorationCommitment(response);
        } catch (Exception e) {
            log.error("下载装修承诺书失败", e);
        }
    }

}
