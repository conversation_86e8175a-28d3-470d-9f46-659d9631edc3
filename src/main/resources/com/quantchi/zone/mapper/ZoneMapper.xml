<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.zone.mapper.ZoneMapper">
    <resultMap id="ZoneDetailVOResultMap" type="com.quantchi.zone.model.vo.ZoneDetailVO">
        <id property="zoneId" column="zoneId"/>
        <result property="zoneName" column="zone_name"/>
        <result property="area" column="area"/>
        <result property="floorId" column="floor_id"/>
        <result property="buildId" column="build_id"/>
        <result property="parkId" column="park_id"/>
        <result property="floor" column="floor_name"/>
        <result property="buildingName" column="building_name"/>
        <result property="parkName" column="park_name"/>
    </resultMap>
    <select id="getZoneInfo" resultMap="ZoneDetailVOResultMap">
        SELECT z.id as zoneId, z.zone_name, z.area, z.floor_id, z.build_id, z.park_id,
               f.floor_name, b.building_name, p.park_name
        FROM zone z
        LEFT JOIN floor f ON z.floor_id = f.id
        LEFT JOIN building b ON z.build_id = b.id
        LEFT JOIN park p ON z.park_id = p.id
        WHERE z.id = #{zoneId}
    </select>
</mapper>
