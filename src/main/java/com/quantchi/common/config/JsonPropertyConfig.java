package com.quantchi.common.config;

import com.alibaba.fastjson.JSONObject;
import com.quantchi.common.config.properties.KeywordSearchProperties;
import com.quantchi.common.domain.NavigationSettings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;

/**
 * json文件格式配置读取和注册对应的bean
 *
 * <AUTHOR>
 * @date 2022/4/20 14:12
 */
@Configuration
@Slf4j
public class JsonPropertyConfig {

    @Bean
    public NavigationSettings navigationSettings() throws IOException {
        final Resource resource = new ClassPathResource("navigation_setting.json");
        final String jsonStr = readResourceAsString(resource);
        return JSONObject.parseObject(jsonStr, NavigationSettings.class);
    }

    @Bean
    public KeywordSearchProperties giksKeywordSearch() throws IOException {
        final Resource resource = new ClassPathResource("keyword_search.json");
        final String jsonStr = readResourceAsString(resource);
        return JSONObject.parseObject(jsonStr, KeywordSearchProperties.class);
    }


    /**
     * 将资源加载为一个字符串
     *
     * @param resource
     * @return
     * @throws IOException
     */
    private String readResourceAsString(final Resource resource) throws IOException {
        final StringBuilder stringBuilder = new StringBuilder();
        final InputStream in = resource.getInputStream();
        final InputStreamReader inputStreamReader = new InputStreamReader(in);
        final BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        try {
            String line;
            while (((line = bufferedReader.readLine()) != null)) {
                stringBuilder.append(line);
            }
            return stringBuilder.toString();
        } finally {
            bufferedReader.close();
            inputStreamReader.close();
            in.close();
        }
    }
}