package com.quantchi.common.model;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/26 10:36
 */
@Data
@ApiModel("SearchSourceBuilder搜索设置")
public class SearchSourceQuery {

    @ApiModelProperty("索引库")
    private String index;

    @ApiModelProperty("查询参数")
    private BoolQuery.Builder queryBuilder;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("高亮参数")
    private Highlight.Builder highlightBuilder;

    @ApiModelProperty("排序方式，多个排序方式中间用;分隔，如'date:desc;year:asc'")
    private String sort;

    @ApiModelProperty("页面大小")
    private Integer pageSize;

    @ApiModelProperty("页面编号")
    private Integer pageNum;

    @ApiModelProperty("包含字段")
    private String[] includes;

    @ApiModelProperty("排除字段")
    private String[] excludes;

    @ApiModelProperty("脚本")
    private String script;
}
