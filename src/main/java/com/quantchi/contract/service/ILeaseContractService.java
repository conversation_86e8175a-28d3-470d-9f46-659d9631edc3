package com.quantchi.contract.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.contract.model.entity.LeaseContract;
import com.quantchi.contract.model.bo.LeaseContractBO;
import com.quantchi.contract.model.vo.LeaseContractVO;
import com.quantchi.contract.model.vo.LeaseContractListVO;
import com.quantchi.contract.model.vo.LeaseContractPageQuery;
import com.quantchi.contract.model.vo.LeaseContractSimpleVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * 租赁合同 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
public interface ILeaseContractService extends IService<LeaseContract> {

    /**
     * 新增或编辑租赁合同
     * @param bo LeaseContractBO
     * @return 合同主键id
     */
    String saveOrUpdateLeaseContract(LeaseContractBO bo);

    /**
     * 查询合同详情
     * @param id 合同ID
     * @return LeaseContractVO
     */
    LeaseContractVO getLeaseContractDetail(String id);

    /**
     * 分页查询合同列表（支持多条件）
     */
    IPage<LeaseContractListVO> pageLeaseContractList(LeaseContractPageQuery query);

    /**
     * 根据id删除合同
     */
    boolean deleteLeaseContractById(String id);

    /**
     * 上传合同扫描件，返回文件保存路径
     */
    String uploadContractScanCopy(MultipartFile file, String contractId);

    /**
     * 上传投资协议文件，返回文件保存路径
     */
    String uploadInvestAgreement(MultipartFile file, String contractId);

    void download(@NotBlank String fileId, boolean inline, HttpServletResponse response);

    /**
     * 根据lesseeId查询合同id和contractNo
     */
    List<LeaseContractSimpleVO> getContractIdAndNoByLesseeId(String lesseeId);
    
    /**
     * 下载租赁合同
     * @param contractId 合同ID
     * @param response HTTP响应
     * @throws Exception 处理异常
     */
    void downloadLeaseContract(String contractId, HttpServletResponse response) throws Exception;
}
