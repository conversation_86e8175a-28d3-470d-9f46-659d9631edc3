package com.quantchi.company.model.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;


@Data
@ApiModel(value = "CompanyLeaseContractVO")
public class CompanyLeaseContractVO {
    /**
     * 园区名称
     */
    @ApiModelProperty(value = "园区名称")
    private String parkName;

    /**
     * 楼栋名称
     */
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    /**
     * 楼层名称
     */
    @ApiModelProperty(value = "楼层名称")
    private String floorName;

    /**
     * 房号
     */
    @ApiModelProperty(value = "房号")
    private String zone;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private Double area;

    /**
     * 租赁时间（yyyy.MM.dd-yyyy.MM.dd）
     */
    @ApiModelProperty(value = "租赁时间（yyyy.MM.dd-yyyy.MM.dd）")
    private String leasePeriod;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = "租金标准")
    private String rentStandard;

    /**
     * 租金总额
     */
    @ApiModelProperty(value = "租金总额")
    private Double totalRent;

    /**
     * 入住时间
     */
    @ApiModelProperty(value = "入住时间")
    private LocalDate checkInTime;

    /**
     * 优惠政策
     */
    @ApiModelProperty(value = "优惠政策")
    private String preferentialPolicy;

    /**
     * 有无投资协议
     */
    @ApiModelProperty(value = "有无投资协议")
    private Boolean hasInvestmentAgreement;

    @ApiModelProperty("投资协议文件名称")
    private String investAgreementName;

    @ApiModelProperty("投资协议文件路径")
    private String investAgreementPath;

//    /**
//     * 享受政策
//     */
//    @ApiModelProperty(value = "享受政策")
//    private String enjoyPolicy;

    @ApiModelProperty("备注")
    private String remark;

}
