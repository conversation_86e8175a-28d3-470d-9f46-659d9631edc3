package com.quantchi.common.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 单字段搜索设置
 * <AUTHOR>
 * @date 2022/4/22 14:19
 */
@Data
public class FieldSearchSetting {

    public static final String MATCH_FUZZY = "模糊";
    public static final String MATCH_EXACT = "精确";
    public static final String MATCH_EQUAL = "=";
    public static final String MATCH_GTE = ">=";
    public static final String MATCH_LE = "<=";

    @ApiModelProperty("筛选项名称")
    private String name;

    @ApiModelProperty("筛选项字段")
    private String field;

    @ApiModelProperty("匹配类型")
    private List<String> matchTypes;

}
