package com.quantchi.company.service.impl;

import com.quantchi.company.model.entity.CompanyRegistrationForm;
import com.quantchi.company.mapper.CompanyRegistrationFormMapper;
import com.quantchi.company.service.ICompanyRegistrationFormService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 企业注册形式：实体注册/虚拟注册 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class CompanyRegistrationFormServiceImpl extends ServiceImpl<CompanyRegistrationFormMapper, CompanyRegistrationForm> implements ICompanyRegistrationFormService {

}
