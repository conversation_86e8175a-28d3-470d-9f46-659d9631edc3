package com.quantchi.contract.model.enums;

public enum PayStatusEnum {
    UNPAID(0, "未缴纳"),
    PAID(1, "已缴纳"),
    PARTIAL(2, "部分缴纳");

    private final int code;
    private final String desc;

    PayStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PayStatusEnum fromCode(int code) {
        for (PayStatusEnum e : values()) {
            if (e.code == code) {
                return e;
            }
        }
        return null;
    }
}
