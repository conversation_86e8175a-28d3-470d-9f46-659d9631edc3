package com.quantchi.common.config.properties;

import cn.hutool.core.collection.CollUtil;
import com.quantchi.common.model.KeywordSearchProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel("es查询的关键词和所需字段配置文件")
public class KeywordSearchProperties {

    private List<KeywordSearchProperty> keywordSearchList;

    public List<KeywordSearchProperty> getAllKeywordSearchProperty() {
        return keywordSearchList;
    }


    /**
     * 获取列表所需字段
     *
     * @param index
     * @return
     */
    public List<String> getRequiredList(final String index) {
        final List<KeywordSearchProperty> indexBoostProperty = keywordSearchList.stream()
                .filter(boostProperty -> boostProperty.getField().equals(index))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(indexBoostProperty)) {
            return Collections.emptyList();
        }
        final KeywordSearchProperty fieldBoostProperty = indexBoostProperty.get(0);
        return fieldBoostProperty.getRequiredList();
    }

    /**
     * 获取关键词搜索字段
     *
     * @param index
     * @return
     */
    public List<KeywordSearchProperty> getKeywordSearchList(final String index) {
        final List<KeywordSearchProperty> indexBoostProperty = keywordSearchList.stream()
                .filter(boostProperty -> boostProperty.getField().equals(index))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(indexBoostProperty)) {
            return Collections.emptyList();
        }
        final KeywordSearchProperty fieldBoostProperty = indexBoostProperty.get(0);
        return fieldBoostProperty.getKeywordSearchList();
    }

    public List<String> getFieldListByIndex(final String index) {
        List<KeywordSearchProperty> subFields = new ArrayList<>();
        for (final KeywordSearchProperty fieldBoostProperty : keywordSearchList) {
            if (fieldBoostProperty.getField().equals(index)) {
                subFields = fieldBoostProperty.getKeywordSearchList();
            }
        }
        return subFields.stream().map(KeywordSearchProperty::getField).collect(Collectors.toList());
    }


}
