package com.quantchi.application.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.application.model.bo.ApplicationNewBO;
import com.quantchi.application.model.bo.EquityStructureBO;
import com.quantchi.application.model.entity.*;
import com.quantchi.application.model.bo.ApplicationMoveInBO;
import com.quantchi.application.model.enums.ApplicationStatus;
import com.quantchi.application.model.enums.ApplyType;
import com.quantchi.application.model.enums.ProcessStatus;
import com.quantchi.application.model.vo.ApplicationMoveInVO;
import com.quantchi.application.model.vo.ProcessVO;
import com.quantchi.application.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.application.mapper.ApplicationMoveInMapper;
import com.quantchi.common.domain.InMemoryCache;
import com.quantchi.company.model.entity.Company;
import com.quantchi.company.service.ICompanyService;
import com.quantchi.sys.mapper.SysUserMapper;
import com.quantchi.sys.model.entity.SysUser;
import com.quantchi.sys.service.impl.CaptchaSendService;
import com.quantchi.sys.service.impl.SysUserService;
import com.quantchi.sys.utils.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.quantchi.common.utils.WordUtils;
import com.quantchi.application.model.entity.IndustryChain;
import com.quantchi.application.mapper.IndustryChainMapper;

/**
 * <p>
 * 申请基本内容表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
@Slf4j
public class ApplicationMoveInServiceImpl extends ServiceImpl<ApplicationMoveInMapper, ApplicationMoveIn> implements IApplicationMoveInService {

    @Autowired
    private IApplicationService applicationService;
    @Autowired
    private IApplicationEquityStructureService equityStructureService;
    @Autowired
    private IApplicationContactService contactService;
    @Autowired
    private IApplicationLeaderService leaderService;
    @Autowired
    private IApplicationProcessService processService;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private IApplicationFileService applicationFileService;
    @Autowired
    private IndustryChainMapper industryChainMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private CaptchaSendService captchaSendService;


    @Override
    public void submitNewApplication(ApplicationNewBO bo) {
        try {

            if (StringUtils.isBlank(bo.getCompanyId()) && StringUtils.isNotBlank(bo.getCompanyName())) {
                Company company = new Company();
                company.setName(bo.getCompanyName());
                company.setLegalPerson(bo.getLeaderName());
                company.setEstablishDate(bo.getEstablishmentDate());
                company.setRegistration(bo.getRegistration() == 1 ? "实体注册" : "虚拟注册");
                bo.setCompanyId(companyService.addCompanyInfo(company));
                if (StringUtils.isBlank(bo.getCompanyId())) {
                    throw new IllegalArgumentException("企业创建失败");
                }
            }

            String user = LoginHelper.getUsername();
            // 1. BO -> Application 实体映射
            Application application = new Application();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                BeanUtils.copyProperties(bo, application);
                application.setId(UUID.randomUUID().toString().replace("-", ""));
                application.setApplicantId(StpUtil.getLoginIdAsString());
                application.setApprover(bo.getApprover());
                // 入驻申请类型
                application.setApplyType(Application.MOVE_IN);
                application.setApplyTime(LocalDate.now());
                long count = applicationService.count(Wrappers.lambdaQuery(Application.class).eq(Application::getApplyType, Application.MOVE_IN));
                application.setApplyCode(LocalDate.now().getYear() + "_" + (count + 1));
                // 处理中
                application.setStatus(Application.PROCESSING);
                application.setCreateTime(LocalDateTime.now());
                application.setUpdateTime(LocalDateTime.now());
                application.setCreateUser(user);
                application.setUpdateUser(user);
                applicationService.save(application);
            } else {
                List<Application> list = applicationService.list(Wrappers.lambdaQuery(Application.class)
                        .eq(Application::getId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请不存在");
                }
                application = list.get(0);
                application.setApprover(bo.getApprover());
                application.setStatus(Application.PROCESSING);
                application.setUpdateTime(LocalDateTime.now());
                application.setUpdateUser(user);
                applicationService.updateById(application);
            }
            String mainId = application.getId();

            // 2. 保存股权结构子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                equityStructureService.remove(Wrappers.lambdaQuery(ApplicationEquityStructure.class)
                        .eq(ApplicationEquityStructure::getApplicationId, bo.getApplicationId()));
            }
            if (CollectionUtils.isNotEmpty(bo.getEquityStructureList())) {
                List<ApplicationEquityStructure> equityList = new ArrayList<>();
                for (EquityStructureBO eqBo : bo.getEquityStructureList()) {
                    ApplicationEquityStructure eqEntity = new ApplicationEquityStructure();
                    eqEntity.setId(UUID.randomUUID().toString().replace("-", ""));
                    eqEntity.setApplicationId(mainId);
                    BeanUtils.copyProperties(eqBo, eqEntity);
                    eqEntity.setCreateTime(LocalDateTime.now());
                    eqEntity.setCreateUser(user);
                    equityList.add(eqEntity);
                }
                equityStructureService.saveBatch(equityList);
            }

            // 3. 保存联系人子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                contactService.remove(Wrappers.lambdaQuery(ApplicationContact.class)
                        .eq(ApplicationContact::getApplicationId, bo.getApplicationId()));
            }
            ApplicationContact contact = new ApplicationContact();
            BeanUtils.copyProperties(bo, contact);
            contact.setId(UUID.randomUUID().toString().replace("-", ""));
            contact.setApplicationId(mainId);
            contact.setCompanyId(bo.getCompanyId());
            contact.setName(bo.getContactName());
            contact.setGender(bo.getContactGender());
            contact.setPosition(bo.getContactPosition());
            contact.setMobile(bo.getContactMobile());
            contact.setFax(bo.getContactFax());
            contact.setTelephone(bo.getContactPhone());
            contact.setEmail(bo.getContactEmail());
            contact.setCreateUser("入驻申请联系人");
            contact.setJobStatus(1);
            contact.setCreateTime(LocalDateTime.now());
            contactService.save(contact);

            // 4. 保存负责人子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                leaderService.remove(Wrappers.lambdaQuery(ApplicationLeader.class)
                        .eq(ApplicationLeader::getApplicationId, bo.getApplicationId()));
            }
            ApplicationLeader leader = new ApplicationLeader();
            BeanUtils.copyProperties(bo, leader);
            leader.setId(UUID.randomUUID().toString().replace("-", ""));
            leader.setApplicationId(mainId);
            leader.setName(bo.getLeaderName());
            leader.setGender(bo.getLeaderGender());
            leader.setNationality(bo.getLeaderNationality());
            leader.setAge(bo.getLeaderAge());
            leader.setEducation(bo.getLeaderEducation());
            leader.setDegree(bo.getLeaderDegree());
            leader.setTitle(bo.getLeaderTitle());
            leader.setMajor(bo.getLeaderMajor());
            leader.setGraduateSchool(bo.getLeaderGraduateSchool());
            leader.setCurrentEmployer(bo.getLeaderCurrentOrg());
            leader.setContactPhone(bo.getLeaderPhone());
            leader.setEmail(bo.getLeaderEmail());
            leader.setCreateTime(LocalDateTime.now());
            leader.setCreateUser(user);
            leaderService.save(leader);

            // 5. 保存入驻申请详情子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                this.remove(Wrappers.lambdaQuery(ApplicationMoveIn.class)
                        .eq(ApplicationMoveIn::getApplicationId, bo.getApplicationId()));
            }
            ApplicationMoveIn moveIn = new ApplicationMoveIn();
            BeanUtils.copyProperties(bo, moveIn);
            moveIn.setId(UUID.randomUUID().toString().replace("-", ""));
            moveIn.setApplicationId(mainId);
            moveIn.setCreateTime(LocalDateTime.now());
            moveIn.setCreateUser(user);
            this.baseMapper.insert(moveIn);

            // 6. 保存审批流程（发起审批）
            ApplicationProcess process = new ApplicationProcess();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                process.setId(UUID.randomUUID().toString().replace("-", ""));
                process.setApplicationId(mainId);
                process.setProcessor(user);
                process.setProcess(ApplicationProcess.START_APPLY);
                process.setProcessTime(LocalDate.now());
                process.setCreateTime(LocalDateTime.now());
                process.setCreateUser(user);
                processService.save(process);
            } else {
                List<ApplicationProcess> list = processService.list(Wrappers.lambdaQuery(ApplicationProcess.class)
                        .eq(ApplicationProcess::getApplicationId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请流程不存在");
                }
                process = list.get(0);
                process.setUpdateTime(LocalDateTime.now());
                process.setUpdateUser(user);
                processService.updateById(process);
            }

            SysUser sysUser = sysUserService.selectUserById(Long.valueOf(application.getApplicantId()));
            captchaSendService.sendApplicationByAliyun(sysUser.getPhonenumber(), bo.getCompanyName(), "SMS_489760403");

        } catch (Exception e) {
            log.error("error occurred when submit move in application: ", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitMoveInApplication(ApplicationMoveInBO bo) {
        try {

            if (StringUtils.isBlank(bo.getCompanyId()) && StringUtils.isNotBlank(bo.getCompanyName())) {
                Company company = new Company();
                company.setName(bo.getCompanyName());
                company.setLegalPerson(bo.getLeaderName());
                company.setEstablishDate(bo.getEstablishmentDate());
                company.setRegistration(bo.getRegistration() == 1 ? "实体注册" : "虚拟注册");
                bo.setCompanyId(companyService.addCompanyInfo(company));
                if (StringUtils.isBlank(bo.getCompanyId())) {
                    throw new IllegalArgumentException("企业创建失败");
                }
            }

            String user = LoginHelper.getUsername();
            // 1. BO -> Application 实体映射
            Application application = new Application();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                BeanUtils.copyProperties(bo, application);
                application.setId(UUID.randomUUID().toString().replace("-", ""));
                application.setApplicantId(StpUtil.getLoginIdAsString());
                application.setApprover(bo.getApprover());
                // 入驻申请类型
                application.setApplyType(Application.MOVE_IN);
                application.setApplyTime(LocalDate.now());
                long count = applicationService.count(Wrappers.lambdaQuery(Application.class).eq(Application::getApplyType, Application.MOVE_IN));
                application.setApplyCode(LocalDate.now().getYear() + "_" + (count + 1));
                // 处理中
                application.setStatus(Application.PROCESSING);
                application.setCreateTime(LocalDateTime.now());
                application.setUpdateTime(LocalDateTime.now());
                application.setCreateUser(user);
                application.setUpdateUser(user);
                applicationService.save(application);
            } else {
                List<Application> list = applicationService.list(Wrappers.lambdaQuery(Application.class)
                        .eq(Application::getId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请不存在");
                }
                application = list.get(0);
                application.setApprover(bo.getApprover());
                application.setStatus(Application.PROCESSING);
                application.setUpdateTime(LocalDateTime.now());
                application.setUpdateUser(user);
                applicationService.updateById(application);
            }
            String mainId = application.getId();

            // 2. 保存股权结构子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                equityStructureService.remove(Wrappers.lambdaQuery(ApplicationEquityStructure.class)
                        .eq(ApplicationEquityStructure::getApplicationId, bo.getApplicationId()));
            }
            if (CollectionUtils.isNotEmpty(bo.getEquityStructureList())) {
                List<ApplicationEquityStructure> equityList = new ArrayList<>();
                for (EquityStructureBO eqBo : bo.getEquityStructureList()) {
                    ApplicationEquityStructure eqEntity = new ApplicationEquityStructure();
                    eqEntity.setId(UUID.randomUUID().toString().replace("-", ""));
                    eqEntity.setApplicationId(mainId);
                    BeanUtils.copyProperties(eqBo, eqEntity);
                    eqEntity.setCreateTime(LocalDateTime.now());
                    eqEntity.setCreateUser(user);
                    equityList.add(eqEntity);
                }
                equityStructureService.saveBatch(equityList);
            }

            // 3. 保存联系人子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                contactService.remove(Wrappers.lambdaQuery(ApplicationContact.class)
                        .eq(ApplicationContact::getApplicationId, bo.getApplicationId()));
            }
            ApplicationContact contact = new ApplicationContact();
            BeanUtils.copyProperties(bo, contact);
            contact.setId(UUID.randomUUID().toString().replace("-", ""));
            contact.setApplicationId(mainId);
            contact.setCompanyId(bo.getCompanyId());
            contact.setName(bo.getContactName());
            contact.setGender(bo.getContactGender());
            contact.setPosition(bo.getContactPosition());
            contact.setMobile(bo.getContactMobile());
            contact.setFax(bo.getContactFax());
            contact.setTelephone(bo.getContactPhone());
            contact.setEmail(bo.getContactEmail());
            contact.setCreateUser("入驻申请联系人");
            contact.setJobStatus(1);
            contact.setCreateTime(LocalDateTime.now());
            contactService.save(contact);

            // 4. 保存负责人子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                leaderService.remove(Wrappers.lambdaQuery(ApplicationLeader.class)
                        .eq(ApplicationLeader::getApplicationId, bo.getApplicationId()));
            }
            ApplicationLeader leader = new ApplicationLeader();
            BeanUtils.copyProperties(bo, leader);
            leader.setId(UUID.randomUUID().toString().replace("-", ""));
            leader.setApplicationId(mainId);
            leader.setName(bo.getLeaderName());
            leader.setGender(bo.getLeaderGender());
            leader.setNationality(bo.getLeaderNationality());
            leader.setAge(bo.getLeaderAge());
            leader.setEducation(bo.getLeaderEducation());
            leader.setDegree(bo.getLeaderDegree());
            leader.setTitle(bo.getLeaderTitle());
            leader.setMajor(bo.getLeaderMajor());
            leader.setGraduateSchool(bo.getLeaderGraduateSchool());
            leader.setCurrentEmployer(bo.getLeaderCurrentOrg());
            leader.setContactPhone(bo.getLeaderPhone());
            leader.setEmail(bo.getLeaderEmail());
            leader.setCreateTime(LocalDateTime.now());
            leader.setCreateUser(user);
            leaderService.save(leader);

            // 5. 保存入驻申请详情子表
            if (StringUtils.isNotBlank(bo.getApplicationId())) {
                this.remove(Wrappers.lambdaQuery(ApplicationMoveIn.class)
                        .eq(ApplicationMoveIn::getApplicationId, bo.getApplicationId()));
            }
            ApplicationMoveIn moveIn = new ApplicationMoveIn();
            BeanUtils.copyProperties(bo, moveIn);
            moveIn.setId(UUID.randomUUID().toString().replace("-", ""));
            moveIn.setApplicationId(mainId);
            moveIn.setCreateTime(LocalDateTime.now());
            moveIn.setCreateUser(user);
            this.baseMapper.insert(moveIn);

            // 6. 保存审批流程（发起审批）
            ApplicationProcess process = new ApplicationProcess();
            if (StringUtils.isBlank(bo.getApplicationId())) {
                process.setId(UUID.randomUUID().toString().replace("-", ""));
                process.setApplicationId(mainId);
                process.setProcessor(user);
                process.setProcess(ApplicationProcess.START_APPLY);
                process.setProcessTime(LocalDate.now());
                process.setCreateTime(LocalDateTime.now());
                process.setCreateUser(user);
                processService.save(process);
            } else {
                List<ApplicationProcess> list = processService.list(Wrappers.lambdaQuery(ApplicationProcess.class)
                        .eq(ApplicationProcess::getApplicationId, bo.getApplicationId()).last("limit 1"));
                if (CollectionUtils.isEmpty(list)) {
                    throw new IllegalArgumentException("该申请流程不存在");
                }
                process = list.get(0);
                process.setUpdateTime(LocalDateTime.now());
                process.setUpdateUser(user);
                processService.updateById(process);
            }

            SysUser sysUser = sysUserService.selectUserById(Long.valueOf(application.getApplicantId()));
            captchaSendService.sendApplicationByAliyun(sysUser.getPhonenumber(), bo.getCompanyName(), "SMS_489760403");

        } catch (Exception e) {
            log.error("error occurred when submit move in application: ", e);
            throw e;
        }

    }

    @Override
    public ApplicationMoveInVO applicationMoveInDetail(final String applicationId) {
        if (StringUtils.isBlank(applicationId)) {
            throw new IllegalArgumentException("申请ID不能为空");
        }
        
        ApplicationMoveInVO vo = new ApplicationMoveInVO();
        
        try {
            // 1. 查询申请表基本信息
            Application application = applicationService.getOne(
                new LambdaQueryWrapper<Application>()
                    .eq(Application::getId, applicationId)
                    .last("limit 1")
            );
            
            if (application == null) {
                throw new IllegalArgumentException("申请不存在");
            }
            
            // 设置申请基本信息
            BeanUtils.copyProperties(application, vo);
            vo.setApplicationId(application.getId());
            vo.setApplyTypeCN(application.getApplyType() != null ? ApplyType.getInfoByCode(application.getApplyType()) : null);
            vo.setStatusCN(application.getStatus() != null ? ApplicationStatus.getInfoByCode(application.getStatus()) : null);

            // 查询审批人姓名
            if (application.getApprover() != null) {
                SysUser approver = sysUserMapper.selectById(application.getApprover());
                if (approver != null) {
                    vo.setApproverName(approver.getUserName());
                }
            }

            // 2. 查询入驻申请详情
            ApplicationMoveIn moveIn = this.getOne(
                new LambdaQueryWrapper<ApplicationMoveIn>()
                    .eq(ApplicationMoveIn::getApplicationId, applicationId)
                    .last("limit 1")
            );
            
            if (moveIn != null) {
                BeanUtils.copyProperties(moveIn, vo);
                // 设置公司信息
                if (StringUtils.isNotBlank(moveIn.getCompanyId())) {
                    Company company = companyService.getById(moveIn.getCompanyId());
                    if (company != null) {
                        vo.setCompanyName(company.getName());
                    }
                }
                vo.setProjectName(moveIn.getProjectName());
                vo.setIndustryField(InMemoryCache.getIndustryChainMap().get(moveIn.getIndustryFieldId()));
                vo.setType(InMemoryCache.getCompanyTypeMap().get(moveIn.getTypeId()));
            }
            
            // 3. 查询股权结构
            List<ApplicationEquityStructure> equityStructures = equityStructureService.list(
                new LambdaQueryWrapper<ApplicationEquityStructure>()
                    .eq(ApplicationEquityStructure::getApplicationId, applicationId)
            );
            
            if (CollectionUtils.isNotEmpty(equityStructures)) {
                List<EquityStructureBO> equityStructureBOs = equityStructures.stream().map(equity -> {
                    EquityStructureBO bo = new EquityStructureBO();
                    BeanUtils.copyProperties(equity, bo);
                    return bo;
                }).collect(Collectors.toList());
                vo.setEquityStructureList(equityStructureBOs);
            }
            
            // 4. 查询联系人信息
            ApplicationContact contact = contactService.getOne(
                new LambdaQueryWrapper<ApplicationContact>()
                    .eq(ApplicationContact::getApplicationId, applicationId)
                    .last("limit 1")
            );
            
            if (contact != null) {
                vo.setContactName(contact.getName());
                vo.setContactGender(contact.getGender());
                vo.setContactPosition(contact.getPosition());
                vo.setContactMobile(contact.getMobile());
                vo.setContactFax(contact.getFax());
                vo.setContactPhone(contact.getTelephone());
                vo.setContactEmail(contact.getEmail());
            }
            
            // 5. 查询负责人信息
            ApplicationLeader leader = leaderService.getOne(
                new LambdaQueryWrapper<ApplicationLeader>()
                    .eq(ApplicationLeader::getApplicationId, applicationId)
                    .last("limit 1")
            );
            
            if (leader != null) {
                vo.setLeaderName(leader.getName());
                vo.setLeaderGender(leader.getGender());
                vo.setLeaderNationality(leader.getNationality());
                vo.setLeaderAge(leader.getAge());
                vo.setLeaderEducation(leader.getEducation());
                vo.setLeaderDegree(leader.getDegree());
                vo.setLeaderTitle(leader.getTitle());
                vo.setLeaderMajor(leader.getMajor());
                vo.setLeaderGraduateSchool(leader.getGraduateSchool());
                vo.setLeaderCurrentOrg(leader.getCurrentEmployer());
                vo.setLeaderPhone(leader.getContactPhone());
                vo.setLeaderEmail(leader.getEmail());
            }

            // 6. 查询审批流程
            List<ApplicationFile> fileList = applicationFileService.list(
                    new LambdaQueryWrapper<ApplicationFile>()
                            .eq(ApplicationFile::getApplicationId, applicationId)
            );
            vo.setProcess(processService.getProcessVOList(applicationId, fileList));
            
        } catch (Exception e) {
            log.error("查询入驻申请详情异常", e);
            throw e;
        }
        
        return vo;
    }

    @Override
    public void downloadMoveInApplication(final String applicationId, final HttpServletResponse response) throws Exception {
        if (StringUtils.isBlank(applicationId)) {
            throw new IllegalArgumentException("申请ID不能为空");
        }

        log.info("开始下载迁入申请表, applicationId: {}", applicationId);

        // 查询申请详情
        ApplicationMoveInVO vo = this.applicationMoveInDetail(applicationId);
        if (vo == null) {
            throw new IllegalArgumentException("申请不存在");
        }

        // 获取公司信息
        String companyName = vo.getCompanyName();
        if (StringUtils.isBlank(companyName)) {
            throw new IllegalArgumentException("企业名称不存在");
        }

        log.info("获取到企业名称: {}", companyName);

        // 获取模板文件路径
        String templatePath = "templates/企业入驻申请表.docx";

        if (vo.getMoveInType() != null && vo.getMoveInType() == 0) {
            templatePath = "templates/企业入驻申请表-新注册企业.docx";
        } else if (vo.getMoveInType() != null && vo.getMoveInType() == 1) {
            templatePath = "templates/企业入驻申请表-迁址企业.docx";
        }

        // 设置文件名
        String fileName = companyName + "企业入驻申请表.docx";
        
        log.info("使用模板: {}, 生成文件: {}", templatePath, fileName);
        
        // 准备替换参数
        Map<String, String> params = new HashMap<>();
        // 基本信息
        params.put("companyName", companyName);
        params.put("projectName", vo.getProjectName());
        
        // 成立时间
        if (vo.getEstablishmentDate() != null) {
            params.put("establishmentDate", vo.getEstablishmentDate().toString());
        } else {
            params.put("establishmentDate", null);
        }
        
        params.put("actualLocation", vo.getActualLocation());
        params.put("projectSource", vo.getProjectSource());

        // 上年度信息
        params.put("regCap", vo.getRegisteredCapitalLastYear());
        params.put("employ", vo.getEmployeesLastYear());
        params.put("intel", vo.getIntellectualPropertyLastYear());
        params.put("reve", vo.getTotalRevenueLastYear());
        params.put("netPro", vo.getNetProfitLastYear());
        params.put("taxPay", vo.getTaxPaidLastYear());
        
        // 项目内容
        params.put("projectContent", vo.getProjectContent());
        
        // 投资计划
        params.put("IPFY", vo.getInvestmentPlanFirstYear());
        params.put("IPTY", vo.getInvestmentPlanThreeYears());
        params.put("EFY", vo.getEmployeesFirstYear());
        params.put("ETY", vo.getEmployeesThreeYears());
        params.put("SDFY", vo.getSiteDemandFirstYear());
        params.put("SDTY", vo.getSiteDemandThreeYears());
        params.put("OVFY", vo.getOutputValueFirstYear());
        params.put("OVTY", vo.getOutputValueThreeYears());
        
        // 团队和技术
        params.put("team", vo.getTeam());
        params.put("technology", vo.getTechnology());
        params.put("product", vo.getProduct());
        params.put("market", vo.getMarket());
        params.put("financing", vo.getFinancing());
        params.put("honors", vo.getHonors());
        
        // 联系人信息
        params.put("cName", vo.getContactName());
        params.put("cSex", vo.getContactGender());
        params.put("cPosition", vo.getContactPosition());
        params.put("cMobile", vo.getContactMobile());
        params.put("cFax", vo.getContactFax());
        params.put("cPhone", vo.getContactPhone());
        params.put("cEmail", vo.getContactEmail());
        
        // 负责人信息
        params.put("lName", vo.getLeaderName());
        params.put("lSex", vo.getLeaderGender());
        params.put("lNation", vo.getLeaderNationality());
        params.put("lAge", vo.getLeaderAge());
        params.put("lEdu", vo.getLeaderEducation());
        params.put("lDegree", vo.getLeaderDegree());
        params.put("lTitle", vo.getLeaderTitle());
        params.put("lMajor", vo.getLeaderMajor());
        params.put("lSchool", vo.getLeaderGraduateSchool());
        params.put("lOrg", vo.getLeaderCurrentOrg());
        params.put("lPhone", vo.getLeaderPhone());
        params.put("lEmail", vo.getLeaderEmail());

        // 股权结构
        if (CollectionUtils.isNotEmpty(vo.getEquityStructureList())) {
            for (int i = 0; i < 3; i++) {
                if (i < vo.getEquityStructureList().size()) {
                    EquityStructureBO equityInfo = vo.getEquityStructureList().get(i);
                    params.put("shareholder" + (i + 1), equityInfo.getShareholderName());
                    params.put("equityRatio" + (i + 1), equityInfo.getEquityRatio());
                    params.put("capital" + (i + 1), equityInfo.getCapitalContribution());
                    params.put("cType" + (i + 1), equityInfo.getContributionType());
                } else {
                    params.put("shareholder" + (i + 1), null);
                    params.put("equityRatio" + (i + 1), null);
                    params.put("capital" + (i + 1), null);
                    params.put("cType" + (i + 1), null);
                }
            }

        } else {
            for (int i = 0; i < 3; i++) {
                params.put("shareholder" + (i + 1), null);
                params.put("equityRatio" + (i + 1), null);
                params.put("capital" + (i + 1), null);
                params.put("cType" + (i + 1), null);
            }
        }
        
        // 处理复选框勾选
        handleCheckboxes(vo, params);
        
        // 申请时间
        if (vo.getApplyTime() != null) {
            params.put("yy", String.valueOf(vo.getApplyTime().getYear()));
            params.put("mm", String.valueOf(vo.getApplyTime().getMonthValue()));
            params.put("dd", String.valueOf(vo.getApplyTime().getDayOfMonth()));
        }
        //申请编号
        if (StringUtils.isNotBlank(vo.getApplyCode()) && vo.getApplyCode().contains("_")) {
            String[] applyCode = vo.getApplyCode().split("_");
            params.put("year", applyCode[0]);
            params.put("num", applyCode[1]);
        }
        
        // 输出参数用于调试
        WordUtils.logParams(params);
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setCharacterEncoding("UTF-8");
//        response.setHeader("Content-Disposition", "attachment; filename=" +
//                new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));
        String encodedFilename = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + encodedFilename);
        
        // 获取资源文件
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath)) {
            if (is == null) {
                log.error("模板文件不存在: {}", templatePath);
                throw new java.io.FileNotFoundException("模板文件不存在: " + templatePath);
            }
            
            log.info("成功读取模板文件: {}", templatePath);
            
            // 处理Word文档
            WordUtils.replaceTextInDocument(is, response.getOutputStream(), params);
            log.info("文件生成完成");
        } catch (IOException e) {
            log.error("处理Word文档异常", e);
            throw e;
        }
    }

    /**
     * 处理复选框勾选
     *
     * @param vo 申请详情对象
     * @param params 参数映射
     */
    private void handleCheckboxes(ApplicationMoveInVO vo, Map<String, String> params) {
        log.info("处理复选框勾选");
        
        // 处理产业领域复选框勾选
        if (StringUtils.isNotBlank(vo.getIndustryField())) {
            String industryField = vo.getIndustryField();
            log.info("产业领域：{}", industryField);
            
            // 使用InMemoryCache缓存的产业链数据
            List<String> industryChains = Arrays.asList("光电技术", "信息技术", "智能装备", "医疗健康", "节能环保", "科技服务");
            
            boolean matched = false;
            if (CollectionUtils.isNotEmpty(industryChains) && industryChains.contains(industryField)) {
                params.put("checkbox_" + industryField + "|产业领域", "true");
                matched = true;
                log.info("匹配到产业领域复选框: {}", industryField);
            }
            
            // 如果没有匹配到任何预定义的产业领域，则勾选"其他"
            if (!matched) {
                // 使用不同的参数键，添加上下文信息，避免与公司类型的"其他"冲突
                params.put("checkbox_其他|产业领域", "true");
                // 如果是其他，则填写具体内容
                params.put("otherIndustry", industryField);
                log.info("未匹配到预定义产业领域，设置为其他: {}", industryField);
            }

        }

        // 处理公司类型复选框
        if (StringUtils.isNotBlank(vo.getType())) {
            String companyType = vo.getType();
            log.info("所属类型：{}", companyType);

            // 使用InMemoryCache缓存的公司类型数据
            List<String> companyTypes = Arrays.asList("创业孵化", "投资加速", "创新平台", "总部研发", "规模生产", "配套机构");

            boolean matched = false;
            if (CollectionUtils.isNotEmpty(companyTypes) && companyTypes.contains(companyType)) {
                params.put("checkbox_" + companyType + "|所属类型", "true");
                matched = true;
                log.info("匹配到所属类型复选框: {}", companyType);
            }

            // 如果没有匹配到任何预定义的公司类型，则勾选"其他"选项
            if (!matched) {
                // 使用不同的参数键，添加上下文信息，避免与产业领域的"其他"冲突
                params.put("checkbox_其他|所属类型", "true");
                // 同时设置普通的其他参数，确保兼容性
//                params.put("checkbox_其他", "true");
                log.info("未找到匹配的所属类型，设置为其他: {}", companyType);
            }
        }
        
        // 处理注册类型复选框 - 根据模板结构进行精确处理
        // 如果值为null，默认设置为否
        String registration = (vo.getRegistration() != null && vo.getRegistration() == 1) ? "是" : "否";
        params.put("checkbox_" + registration + "|是否实际入驻", "true");
        log.info("设置注册类型为：{}", registration);

        // 处理高新产品复选框 - 根据模板结构进行精确处理
        // 如果值为null，默认设置为否
        String productOption = (vo.getIsHighTechProduct() != null && vo.getIsHighTechProduct() == 1) ? "是" : "否";
        // 设置两种格式的参数，确保兼容性
//        params.put(productOption + "|高新产品", "true");
        params.put("checkbox_" + productOption + "|高新产品", "true");
        log.info("设置高新产品为：{}", productOption);
        
        // 处理高新企业复选框 - 根据模板结构进行精确处理
        // 如果值为null，默认设置为否
        String enterpriseOption = (vo.getIsHighTechEnterprise() != null && vo.getIsHighTechEnterprise() == 1) ? "是" : "否";
        // 设置两种格式的参数，确保兼容性
//        params.put(enterpriseOption + "|高新企业", "true");
        params.put("checkbox_" + enterpriseOption + "|高新企业", "true");
        log.info("设置高新企业为：{}", enterpriseOption);

        if (vo.getMoveInType() != null && vo.getMoveInType() == 1) {
            // 处理经营异常复选框 - 根据模板结构进行精确处理
            // 如果值为null，默认设置为否
            String abnormalOperation = (vo.getIsAbnormalOperation() != null && vo.getIsAbnormalOperation() == 1) ? "是" : "否";
            params.put("checkbox_" + abnormalOperation + "|是否有经营异常", "true");
            log.info("设置经营异常为：{}", abnormalOperation);

            // 处理科技型中小企业复选框 - 根据模板结构进行精确处理
            // 如果值为null，默认设置为否
            String techEnterprise = (vo.getIsTechEnterprise() != null && vo.getIsTechEnterprise() == 1) ? "是" : "否";
            params.put("checkbox_" + techEnterprise + "|科技型中小企业", "true");
            log.info("设置科技型中小企业为：{}", techEnterprise);
        }

    }
}
