package com.quantchi.contract.model.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("编辑租金BO")
public class EditLeaseRentBO {

    @ApiModelProperty("合同id")
    private String contractId;

    @ApiModelProperty("企业ID")
    private String companyId;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("季度")
    private Integer quarter;

    @ApiModelProperty("折扣系数")
    private BigDecimal discount;

    @ApiModelProperty("减免金额")
    private BigDecimal reduction;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("已收金额")
    private BigDecimal receivedAmount;

    @ApiModelProperty("对赌金额")
    private BigDecimal betAmount;
}
