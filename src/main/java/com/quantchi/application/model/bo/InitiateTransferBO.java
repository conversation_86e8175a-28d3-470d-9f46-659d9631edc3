package com.quantchi.application.model.bo;

import com.quantchi.common.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 发起流转请求对象
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@ApiModel(value = "发起流转请求")
public class InitiateTransferBO {

    @ApiModelProperty(value = "申请ID", required = true)
    @NotBlank(message = "申请ID不能为空")
    private String applicationId;

    @ApiModelProperty(value = "审批人ID", required = true)
    @NotNull(message = "审批人ID不能为空")
    private Long approver;

    @ApiModelProperty(value = "流转说明")
    private String description;

    @ApiModelProperty("补充材料")
    private List<FileInfo> supplementaryFiles;
}
