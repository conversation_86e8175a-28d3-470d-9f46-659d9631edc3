package com.quantchi.company.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel("企业租赁合同编辑参数")
public class CompanyLeaseContractEditBO {
    @ApiModelProperty("企业ID")
    private String companyId;

    @ApiModelProperty(value = "入住时间")
    private LocalDate checkInTime;

//    @ApiModelProperty("有无投资协议")
//    private Boolean hasInvestmentAgreement;

    @ApiModelProperty("备注")
    private String remark;
}
