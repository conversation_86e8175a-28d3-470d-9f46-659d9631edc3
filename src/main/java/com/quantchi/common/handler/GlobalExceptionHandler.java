package com.quantchi.common.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.hutool.core.lang.Dict;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.exception.BusinessException;
import com.quantchi.common.utils.HttpServletRequestUtil;
import com.quantchi.common.utils.ResultConvert;
import org.apache.ibatis.exceptions.PersistenceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.validation.UnexpectedTypeException;
import javax.validation.ValidationException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    public GlobalExceptionHandler() {
    }

    @ExceptionHandler({BusinessException.class})
    public ResultInfo handleRRException(final BusinessException e) {
        return ResultConvert.error(e.getCode(), e.getMsg());
    }

    @ExceptionHandler({MaxUploadSizeExceededException.class})
    public ResultInfo handleFileLimitException(final MaxUploadSizeExceededException e) {
        return ResultConvert.error("文件太大，请压缩后上传");
    }

    @ExceptionHandler({UnexpectedTypeException.class})
    public ResultInfo handleRRException(final UnexpectedTypeException e) {
        return ResultConvert.error("参数异常");
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MethodArgumentTypeMismatchException.class})
    public ResultInfo handleMethodArgumentTypeMismatchException(final MethodArgumentTypeMismatchException e) {
        log.error("方法参数类型不匹配", e);
        return ResultConvert.error("方法参数类型不匹配");
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public ResultInfo handleMissingServletRequestParameterException(final MissingServletRequestParameterException e) {
        log.info(HttpServletRequestUtil.getAllRequestInfo());
        log.error("缺少请求参数", e.getParameterName());
        return ResultConvert.error("缺少请求参数:" + e.getParameterName());
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({HttpMessageNotReadableException.class})
    public ResultInfo handleHttpMessageNotReadableException(final HttpMessageNotReadableException e) {
        log.info(HttpServletRequestUtil.getAllRequestInfo());
        log.error("参数解析失败", e);
        return ResultConvert.error("参数解析失败");
    }

    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public ResultInfo handleHttpRequestMethodNotSupportedException(final HttpRequestMethodNotSupportedException e) {
        log.error("不支持当前请求方法", e);
        return ResultConvert.error("不支持当前请求方法");
    }

    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    @ExceptionHandler({HttpMediaTypeNotSupportedException.class})
    public ResultInfo handleHttpMediaTypeNotSupportedException(final Exception e) {
        log.error("不支持当前媒体类型", e);
        return ResultConvert.error("不支持当前媒体类型");
    }

    @ExceptionHandler({DuplicateKeyException.class})
    public ResultInfo handleDuplicateKeyException(final DuplicateKeyException e) {
        log.info(HttpServletRequestUtil.getAllRequestInfo());
        log.error(e.getMessage(), e);
        return ResultConvert.error(500, "数据库中已存在该记录");
    }

    @ExceptionHandler({NotLoginException.class})
    public ResponseEntity handleNotLoginException(final NotLoginException e) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ResultConvert.error(401, "登录状态失效，请重新登录！"));
    }

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResultInfo handleMethodArgumentNotValidException(final MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        final List<Map> result = new ArrayList();
        e.getBindingResult().getFieldErrors().forEach((fieldError) -> {
            result.add(Dict.create().set("field", fieldError.getField()).set("msg", fieldError.getDefaultMessage()));
        });
        return ResultConvert.error(result.toString());
    }

    @ExceptionHandler({ValidationException.class})
    public ResultInfo handleValidationException(final ValidationException e) {
        log.error(e.getMessage(), e);
        return ResultConvert.error(500, e.getCause().getMessage());
    }

    @ExceptionHandler({AbstractMethodError.class})
    public void handlerAbstractMethodError(final Exception e) {
        log.info(HttpServletRequestUtil.getAllRequestInfo());
        log.error(e.getMessage(), e);
    }

    @ExceptionHandler({java.sql.SQLSyntaxErrorException.class, SQLException.class, PersistenceException.class})
    public ResultInfo handleSQLSyntaxErrorError(final Exception e) {
        log.error(e.getMessage(), e);
        return ResultConvert.error(500, "语句错误，执行失败");
    }

    @ExceptionHandler({Exception.class})
    public ResultInfo handleException(final Exception e) {
        log.info(HttpServletRequestUtil.getAllRequestInfo());
        log.error(e.getMessage(), e);
        return ResultConvert.error(500, e.getMessage());
    }
}
