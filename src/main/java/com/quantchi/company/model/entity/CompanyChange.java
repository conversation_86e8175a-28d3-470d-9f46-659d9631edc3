package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_change")
@ApiModel(value = "CompanyChange对象", description = "")
public class CompanyChange implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录主键")
    private String id;

    @ApiModelProperty("企业ID")
    private String cid;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("变更日期")
    private Date changeDate;

    @ApiModelProperty("变更项目，如章程备案、股权变更、地址变更")
    private String changeProject;

    @ApiModelProperty("变更前")
    private String changeBefore;

    @ApiModelProperty("变更后")
    private String changeAfter;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String COMPANY_NAME = "company_name";

    public static final String CHANGE_DATE = "change_date";

    public static final String CHANGE_PROJECT = "change_project";

    public static final String CHANGE_BEFORE = "change_before";

    public static final String CHANGE_AFTER = "change_after";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
