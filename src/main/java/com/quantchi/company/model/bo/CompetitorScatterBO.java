package com.quantchi.company.model.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/27 下午5:07
 */
@Data
public class CompetitorScatterBO {

    @ApiModelProperty(value = "企业id")
    private String companyId;

    @ApiModelProperty(value = "x坐标指标")
    @JsonProperty("xCoordinateIndex")
    private String xCoordinateIndex;

    @ApiModelProperty(value = "y坐标指标")
    @JsonProperty("yCoordinateIndex")
    private String yCoordinateIndex;

    @ApiModelProperty("维度类型，分为经营实力、科创实力、资本青睐度、投资活跃度四种")
    private String dimensionType;

}
