package com.quantchi.application.model.vo;

import com.quantchi.common.model.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 装修申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@ApiModel(value = "ApplicationDecorationVO", description = "装修申请业务对象")
public class ApplicationDecorationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 申请ID */
    @ApiModelProperty("申请ID")
    private String applicationId;

    /** 审批人ID */
    @ApiModelProperty("审批人ID")
    private Long approver;

    /** 审批人姓名 */
    @ApiModelProperty("审批人姓名")
    private String approverName;

    /** 申请编号 */
    @ApiModelProperty("申请编号")
    private String applyCode;

    /** 申请时间 */
    @ApiModelProperty("申请时间")
    private LocalDate applyTime;

    /** 申请时间 */
    @ApiModelProperty("完成时间")
    private LocalDate finishTime;

    /** 申请类型 */
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private Integer applyType;

    /** 申请类型 */
    @ApiModelProperty("申请类型: 1-入驻；2-迁出；3-装修")
    private String applyTypeCN;

    /** 当前状态 */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private Integer status;

    /** 当前状态 */
    @ApiModelProperty("当前状态：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private String statusCN;

    /** 公司ID */
    @ApiModelProperty("公司ID")
    private String companyId;

    @ApiModelProperty("公司名称")
    private String companyName;

    /** 联系方式 */
    @ApiModelProperty("联系方式")
    private String contactInfo;

    @ApiModelProperty("装修申请说明")
    private List<FileInfo> decorateApplyInstructions;

    @ApiModelProperty("营业执照")
    private List<FileInfo> businessLicenses;

    @ApiModelProperty("有效期内租赁合同")
    private List<FileInfo> leaseContracts;

    @ApiModelProperty("装修承诺书")
    private List<FileInfo> decorateUndertakings;

    @ApiModelProperty("设计院盖章的设计图纸（及电子版）")
    private List<FileInfo> designDrawings;

    @ApiModelProperty("装修公司营业执照及资质证明等")
    private List<FileInfo> decorateCompanies;

    @ApiModelProperty("审批进度")
    private List<ProcessVO> process;

}
