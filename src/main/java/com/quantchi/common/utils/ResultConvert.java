package com.quantchi.common.utils;

import com.quantchi.common.domain.CommonHeader;
import com.quantchi.common.domain.ResultInfo;

public class ResultConvert {

    public static final Integer SUCCESS_CODE = 200;
    public static final Integer ERROR_CODE = 0;

    public static <T> ResultInfo<T> success() {
        final CommonHeader commonHeader = new CommonHeader(SUCCESS_CODE, "success");
        return new ResultInfo<>(commonHeader);
    }

    public static <T> ResultInfo<T> success(final T data) {
        final CommonHeader commonHeader = new CommonHeader(SUCCESS_CODE, "success");
        return new ResultInfo<>(data, commonHeader);
    }

    public static <T> ResultInfo<T> success(final T data, final String msg) {
        final CommonHeader commonHeader = new CommonHeader(SUCCESS_CODE, msg);
        return new ResultInfo<>(data, commonHeader);
    }


    public static <T> ResultInfo<T> error(final Integer code, final String message) {
        final CommonHeader commonHeader = new CommonHeader(code, message);
        return new ResultInfo<>(commonHeader);
    }

    public static <T> ResultInfo<T> error(final String message) {
        final CommonHeader commonHeader = new CommonHeader(-1, message);
        return new ResultInfo<>(commonHeader);
    }

    public static <T> ResultInfo<T> error(final String[] errors) {
        final CommonHeader commonHeader =
                new CommonHeader(Integer.valueOf(errors[0]), errors[1]);
        return new ResultInfo<>(commonHeader);
    }


    @Override
    public String toString() {
        return super.toString();
    }
}
