user root;
worker_processes  4;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
     gzip on;
      gzip_comp_level 5;
      gzip_min_length 1k;
      gzip_buffers 4 16k;
      gzip_proxied any;
      gzip_vary on;
      keepalive_timeout  300;
      gzip_types
        application/javascript
        application/x-javascript
        text/javascript
        text/css
        text/xml
        application/xhtml+xml
        application/xml
        application/atom+xml
        application/rdf+xml
        application/rss+xml
        application/geo+json
        application/json
        application/ld+json
        application/manifest+json
        application/x-web-app-manifest+json
        image/svg+xml
        text/x-cross-domain-policy;
      gzip_disable "MSIE [1-6]\.";
        include       mime.types;
        default_type  application/octet-stream;

    include /etc/nginx/conf.d/*.conf;
    server {
            listen 30816;
            add_header X-Content-Type-Options: nosniff;
            add_header X-XSS-Protection '1;mode=block';
            add_header Content-Security-Policy "frame-ancestors 'self'";
            error_page 403 =404 /404.html;

    location / {
                root  /usr/local/web;
                index   index.html;
                try_files $uri $uri/ /index.html;
    }

    location /auth-images {
    	    set $query '';
            if ($request_uri ~* "[^\?]+\?(.*)$") {
                 set $query $1;
            }
            proxy_pass http://************:8806/api/user/check;
            proxy_set_header X-Original-URI $request_uri;
    	    proxy_pass_request_body off;
    	    proxy_set_header Content-Length "";
    	    proxy_set_header Auth-Ticket $query;
    }

    location /api {
                client_max_body_size 100M;
                proxy_pass http://************:8806;
    }

    location /api/back/ {
                    client_max_body_size 100M;
                    proxy_pass http://************:8806;
    }


    }

}