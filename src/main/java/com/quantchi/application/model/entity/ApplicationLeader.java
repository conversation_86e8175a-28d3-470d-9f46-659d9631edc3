package com.quantchi.application.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("application_leader")
@ApiModel(value = "ApplicationLeader对象", description = "")
public class ApplicationLeader implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 申请ID */
    private String applicationId;

    /** 姓名 */
    @ApiModelProperty("姓名")
    private String name;

    /** 性别 */
    @ApiModelProperty("性别")
    private String gender;

    /** 国籍 */
    @ApiModelProperty("国籍")
    private String nationality;

    /** 年龄 */
    @ApiModelProperty("年龄")
    private String age;

    /** 学历 */
    @ApiModelProperty("学历")
    private String education;

    /** 职称 */
    @ApiModelProperty("职称")
    private String title;

    /** 学位 */
    @ApiModelProperty("学位")
    private String degree;

    /** 专业 */
    @ApiModelProperty("专业")
    private String major;

    /** 毕业学校 */
    @ApiModelProperty(value = "毕业学校")
    private String graduateSchool;

    /** 目前单位 */
    @ApiModelProperty(value = "目前单位")
    private String currentEmployer;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 电子邮箱 */
    @ApiModelProperty(value = "电子邮箱")
    private String email;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 创建人 */
    private String createUser;

    public static final String ID = "id";

    public static final String APPLICATION_ID = "application_id";

    public static final String NAME = "name";

    public static final String GENDER = "gender";

    public static final String NATIONALITY = "nationality";

    public static final String AGE = "age";

    public static final String EDUCATION = "education";

    public static final String TITLE = "title";

    public static final String DEGREE = "degree";

    public static final String MAJOR = "major";

    public static final String CREATE_TIME = "create_time";

    public static final String CREATE_USER = "create_user";
}
