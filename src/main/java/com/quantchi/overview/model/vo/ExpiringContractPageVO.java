package com.quantchi.overview.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 合同即将到期企业分页查询结果
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@ApiModel(value = "ExpiringContractPageVO", description = "合同即将到期企业分页查询结果")
public class ExpiringContractPageVO {

    @ApiModelProperty("总记录数")
    private Long total;

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("每页记录数")
    private Integer pageSize;

    @ApiModelProperty("总页数")
    private Long pages;

    @ApiModelProperty("记录列表")
    private List<ExpiringContractVO> records;
}
