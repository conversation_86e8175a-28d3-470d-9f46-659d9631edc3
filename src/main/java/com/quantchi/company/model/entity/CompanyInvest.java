package com.quantchi.company.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("company_invest")
@ApiModel(value = "CompanyInvest对象", description = "")
public class CompanyInvest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录主键")
    private String id;

    @ApiModelProperty("企业ID")
    private String cid;

    @ApiModelProperty("被投企业ID")
    private String investedId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("投资比例")
    private BigDecimal investRatio;

    @ApiModelProperty("投资金额描述")
    private String investAmount;

    @ApiModelProperty("被投企业名称")
    private String investedName;

    @ApiModelProperty("被投企业法人")
    private String investedLegalPerson;

    @ApiModelProperty("被投企业成立日期")
    private Date investedEstablishedDate;

    @ApiModelProperty("被投企业经营状态")
    private String investedStatus;

    @ApiModelProperty("被投企业注册资本描述")
    private String investedRegistCapi;

    @ApiModelProperty("被投企业注册行业")
    private String investedIndustry;

    @ApiModelProperty("企业地址")
    private String address;

    @ApiModelProperty("省份代码")
    private String provinceCode;

    @ApiModelProperty("城市代码")
    private String cityCode;

    @ApiModelProperty("区域代码")
    private String areaCode;

    @ApiModelProperty("经度")
    private String lng;

    @ApiModelProperty("纬度")
    private String lat;

    @ApiModelProperty("被投资企业地址")
    private String investAddress;

    @ApiModelProperty("省份代码")
    private String investProvinceCode;

    @ApiModelProperty("城市代码")
    private String investCityCode;

    @ApiModelProperty("区域代码")
    private String investAreaCode;

    @ApiModelProperty("经度")
    private String investLng;

    @ApiModelProperty("纬度")
    private String investLat;

    @ApiModelProperty("投资特征")
    private String investType;

    @ApiModelProperty("数据是否有效")
    private Byte isValid;

    @ApiModelProperty("数据创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("数据修改时间")
    private LocalDateTime updateTime;

    public static final String ID = "id";

    public static final String CID = "cid";

    public static final String INVESTED_ID = "invested_id";

    public static final String COMPANY_NAME = "company_name";

    public static final String INVEST_RATIO = "invest_ratio";

    public static final String INVEST_AMOUNT = "invest_amount";

    public static final String INVESTED_NAME = "invested_name";

    public static final String INVESTED_LEGAL_PERSON = "invested_legal_person";

    public static final String INVESTED_ESTABLISHED_DATE = "invested_established_date";

    public static final String INVESTED_STATUS = "invested_status";

    public static final String INVESTED_REGIST_CAPI = "invested_regist_capi";

    public static final String INVESTED_INDUSTRY = "invested_industry";

    public static final String ADDRESS = "address";

    public static final String PROVINCE_CODE = "province_code";

    public static final String CITY_CODE = "city_code";

    public static final String AREA_CODE = "area_code";

    public static final String LNG = "lng";

    public static final String LAT = "lat";

    public static final String INVEST_ADDRESS = "invest_address";

    public static final String INVEST_PROVINCE_CODE = "invest_province_code";

    public static final String INVEST_CITY_CODE = "invest_city_code";

    public static final String INVEST_AREA_CODE = "invest_area_code";

    public static final String INVEST_LNG = "invest_lng";

    public static final String INVEST_LAT = "invest_lat";

    public static final String INVEST_TYPE = "invest_type";

    public static final String IS_VALID = "is_valid";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";
}
