package com.quantchi.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.contract.model.bo.LeaseRentListQuery;
import com.quantchi.contract.model.enums.PayStatusEnum;
import com.quantchi.contract.model.bo.EditLeaseRentBO;
import com.quantchi.contract.model.vo.LeaseRentListVO;
import com.quantchi.contract.model.vo.PayStatusVO;
import com.quantchi.contract.model.vo.LeaseRentDetailVO;
import com.quantchi.contract.service.ILeaseRentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/leaseRent")
@Api(tags = "租金相关接口")
@Slf4j
public class LeaseRentController {

    @Autowired
    private ILeaseRentService leaseRentService;

    @GetMapping("/listByContractId")
    @ApiOperation("根据合同id查询租金明细及扩展信息")
    public ResultInfo<LeaseRentDetailVO> listByContractId(@RequestParam String contractId) {
        LeaseRentDetailVO detail = leaseRentService.listByContractId(contractId);
        return ResultConvert.success(detail);
    }

    @PostMapping("/pageByCondition")
    @ApiOperation("租金明细多条件分页查询，按合同聚类")
    public ResultInfo<IPage<LeaseRentListVO>> pageByCondition(@RequestBody LeaseRentListQuery query) {
        IPage<LeaseRentListVO> page = leaseRentService.pageRentByCondition(query);
        return ResultConvert.success(page);
    }

    @PostMapping("/exportByCondition")
    @ApiOperation("租金明细多条件导出Excel")
    public void exportByCondition(@RequestBody LeaseRentListQuery query, HttpServletResponse response) {
        try {
            leaseRentService.exportRentByCondition(query, response);
        } catch (Exception e) {
            log.error("导出租金明细Excel失败", e);
            try {
                // 重置响应状态和内容类型
                response.reset();
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("写入响应异常", ex);
            }
        }
    }

    @GetMapping("/payStatus/list")
    @ApiOperation("租金缴纳状态列表")
    public ResultInfo<List<PayStatusVO>> listPayStatus() {
        List<PayStatusVO> list = new ArrayList<>();
        for (PayStatusEnum e : PayStatusEnum.values()) {
            PayStatusVO vo = new PayStatusVO();
            vo.setCode(e.getCode());
            vo.setDesc(e.getDesc());
            list.add(vo);
        }
        return ResultConvert.success(list);
    }

    @PutMapping("/editByContactId")
    @ApiOperation("根据合同id编辑租金信息")
    public ResultInfo<Boolean> editLeaseRent(@RequestBody EditLeaseRentBO bo) {
        boolean updated = false;
        try {
            updated = leaseRentService.editLeaseRent(bo);
        } catch (Exception e) {
            return ResultConvert.error(e.getMessage());
        }
        if (updated) {
            return ResultConvert.success(updated);
        } else {
            return ResultConvert.error("编辑失败！");
        }
    }

    @PutMapping("/editByCompanyId")
    @ApiOperation("根据企业id编辑租金信息")
    public ResultInfo<Boolean> editLeaseRentByCompany(@RequestBody EditLeaseRentBO bo) {
        boolean updated = false;
        try {
            updated = leaseRentService.editLeaseRentByCompany(bo);
        } catch (Exception e) {
            return ResultConvert.error(e.getMessage());
        }
        if (updated) {
            return ResultConvert.success(updated);
        } else {
            return ResultConvert.error("编辑失败！");
        }
    }

    @GetMapping("/calculateReceivableAmount")
    @ApiOperation("计算应收金额")
    public ResultInfo<BigDecimal> calculateReceivableAmount(@ApiParam(value = "合同ID", required = true) @RequestParam String contractId,
                                                            @ApiParam(value = "折扣系数") @RequestParam(required = false) BigDecimal discount,
                                                            @ApiParam(value = "减免金额") @RequestParam(required = false) BigDecimal reduction) {
        try {
            BigDecimal originalAmount = leaseRentService.getOriginalAmountByContractId(contractId);
            discount = discount == null ? BigDecimal.ONE : discount;
            reduction = reduction == null ? BigDecimal.ZERO : reduction;
            BigDecimal receivableAmount = originalAmount.multiply(discount).subtract(reduction);
            return ResultConvert.success(receivableAmount);
        } catch (Exception e) {
            log.error("计算应收金额失败", e);
            return ResultConvert.error("计算失败：" + e.getMessage());
        }
    }
}
