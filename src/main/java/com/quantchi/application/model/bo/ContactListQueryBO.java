package com.quantchi.application.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 企业联系人列表查询参数
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@ApiModel(value = "企业联系人列表查询参数")
public class ContactListQueryBO {

    /**
     * 企业ID
     */
    @NotBlank(message = "企业ID不能为空")
    @ApiModelProperty(value = "企业ID", required = true)
    private String companyId;

    /**
     * 关键字搜索（姓名、职务）
     */
    @ApiModelProperty("关键字搜索（姓名、职务）")
    private String keyword;

    /**
     * 在职状态：0-离职，1-在职
     */
    @ApiModelProperty("在职状态：0-离职，1-在职")
    private Integer jobStatus;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    @ApiModelProperty(value = "页码", required = true)
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    @ApiModelProperty(value = "每页记录数", required = true)
    private Integer pageSize = 10;
}
