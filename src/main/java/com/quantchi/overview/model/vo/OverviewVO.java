package com.quantchi.overview.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 园区概况统计VO
 */
@Data
@ApiModel("园区概况统计VO")
public class OverviewVO {

    @ApiModelProperty("企业总数")
    private Long companyTotal;

    @ApiModelProperty("实体入驻企业数")
    private Long entityCompanyCount;

    @ApiModelProperty("虚拟入驻企业数")
    private Long virtualCompanyCount;

    @ApiModelProperty("合同即将到期的企业数")
    private Long contractExpiringCompanyCount;

    @ApiModelProperty("所有应收租金")
    private BigDecimal totalReceivableRent;

    @ApiModelProperty("已收租金")
    private BigDecimal totalReceivedRent;

    @ApiModelProperty("未收租金")
    private BigDecimal totalUnreceivedRent;

    @ApiModelProperty("即将到期租金")
    private BigDecimal expiringRent;
}
