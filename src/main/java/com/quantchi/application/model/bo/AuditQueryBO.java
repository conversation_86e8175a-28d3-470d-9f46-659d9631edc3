package com.quantchi.application.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 审批管理查询参数
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
@ApiModel(value = "审批管理查询参数")
public class AuditQueryBO {

    /**
     * 申请事项类型列表：1-入驻；2-迁出；3-装修
     */
    @ApiModelProperty("申请事项类型列表：1-入驻；2-迁出；3-装修")
    private List<Integer> applyTypes;

    /**
     * 当前状态列表：1-审批通过；2-处理中；3-已驳回；4-已撤销
     */
    @ApiModelProperty("当前状态列表：1-审批通过；2-处理中；3-已驳回；4-已撤销")
    private List<Integer> statuses;

    /**
     * 查询关键字
     */
    @ApiModelProperty("查询关键字")
    private String keyword;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    @ApiModelProperty("每页记录数")
    private Integer pageSize = 10;
}
