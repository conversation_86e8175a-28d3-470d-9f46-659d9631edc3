package com.quantchi.application.model.vo;

import com.quantchi.application.model.entity.CompanyType;
import com.quantchi.application.model.entity.IndustryChain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
@ApiModel
public class MoveInOptionalListVO {
    @ApiModelProperty("产业领域")
    private List<IndustryChain> industryChainList;
    @ApiModelProperty("所属类型")
    private List<CompanyType> companyTypeList;
}
