package com.quantchi.application.service;

import com.quantchi.application.model.bo.ApplicationNewBO;
import com.quantchi.application.model.entity.ApplicationMoveIn;
import com.baomidou.mybatisplus.extension.service.IService;
import com.quantchi.application.model.bo.ApplicationMoveInBO;
import com.quantchi.application.model.vo.ApplicationMoveInVO;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <p>
 * 申请基本内容表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IApplicationMoveInService extends IService<ApplicationMoveIn> {
    /**
     * 入驻申请提交，包含BO转Entity、主表及子表保存等逻辑
     * @param bo 入驻申请BO
     */
    void submitMoveInApplication(ApplicationMoveInBO bo);

    ApplicationMoveInVO applicationMoveInDetail(String applicationId);
    
    /**
     * 下载迁入申请表
     * @param applicationId 申请ID
     * @param response HTTP响应对象
     * @throws Exception 处理异常
     */
    void downloadMoveInApplication(String applicationId, HttpServletResponse response) throws Exception;

    void submitNewApplication(@Valid ApplicationNewBO bo);
}
