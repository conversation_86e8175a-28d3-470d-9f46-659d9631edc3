package com.quantchi.company.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class CompanyFundIndustryInvestBO {
    @ApiModelProperty("企业ID")
    private String cid;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("被投企业ID")
    private String investedId;

    @ApiModelProperty("被投企业名称")
    private String investedName;

    @ApiModelProperty("被投企业成立日期")
    private String investedEstablishedDate;

    @ApiModelProperty("投资金额描述")
    private String investAmount;

    @ApiModelProperty("投资比例")
    private BigDecimal investRatio;

    @ApiModelProperty(value = "国民经济行业分类-大类")
    private String nationIndustry2;

    @ApiModelProperty(value = "国民经济行业分类-中类")
    private String nationIndustry3;
}
