<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.contract.mapper.LeaseRentMapper">

    <resultMap id="LeaseRentListVOMap" type="com.quantchi.contract.model.vo.LeaseRentListVO">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="contractNo" column="contract_no"/>
        <result property="parkName" column="park_name"/>
        <result property="buildingName" column="building_name"/>
        <result property="zoneName" column="zone_name"/>
        <result property="lessee" column="lessee"/>
        <result property="leasePeriod" column="lease_period"/>
        <result property="year" column="year"/>
        <result property="quarter" column="quarter"/>
        <result property="discount" column="discount"/>
        <result property="reduction" column="reduction"/>
        <result property="rentStandard" column="rent_standard"/>
        <result property="rentTotal" column="rent_total"/>
        <result property="contractCreateTime" column="contract_create_time"/>
        <result property="receivableAmount" column="receivable_amount"/>
        <result property="receivedAmount" column="received_amount"/>
        <result property="unreceivedAmount" column="unreceived_amount"/>
    </resultMap>

    <resultMap id="LeaseRentDetailVOMap" type="com.quantchi.contract.model.vo.LeaseRentDetailVO">
        <result property="parkName" column="park_name"/>
        <result property="buildingName" column="building_name"/>
        <result property="zoneName" column="zone_name"/>
        <result property="lessee" column="lessee"/>
        <result property="leasePeriod" column="lease_period"/>
        <result property="preferentialPolicy" column="preferential_policy"/>
    </resultMap>

    <select id="selectPageByCondition" resultMap="LeaseRentListVOMap">
        SELECT
            r.id,
            r.contract_id,
            r.receivable_amount,
            r.received_amount,
            p.park_name,
            b.building_name,
            z.zone_name,
            c2.name AS lessee,
            CONCAT(DATE_FORMAT(c.lease_start_time, '%Y.%m.%d'), ' - ', DATE_FORMAT(c.lease_end_time, '%Y.%m.%d')) AS lease_period,
            c.rental_standard AS rent_standard,
            c.rental_collect AS rent_total
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN zone z ON c.zone_id = z.id
        LEFT JOIN building b ON z.build_id = b.id
        LEFT JOIN park p ON z.park_id = p.id
        LEFT JOIN company c2 ON c.lessee_id = c2.id
        WHERE 1=1
        <if test="query.yearLimit != null and query.quarterLimit != null">
            AND (r.year &lt; #{query.yearLimit} OR (r.year = #{query.yearLimit} AND r.quarter &lt;= #{query.quarterLimit}))
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
                c2.name LIKE CONCAT('%', #{query.keyword}, '%')
                OR z.zone_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        <if test="query.parkId != null and query.parkId != ''">
            AND z.park_id = #{query.parkId}
        </if>
        <if test="query.buildingId != null and query.buildingId != ''">
            AND z.build_id = #{query.buildingId}
        </if>
    </select>

    <select id="selectByCondition" resultMap="LeaseRentListVOMap">
        SELECT
            r.id,
            r.contract_id,
            c.contract_no,
            r.year,
            r.quarter,
            r.discount,
            r.reduction,
            r.receivable_amount,
            r.received_amount,
            r.unreceived_amount,
            c.create_time AS contract_create_time,
            GROUP_CONCAT(lcz.zone_id) AS zone_name,
            c2.name AS lessee,
            CONCAT(DATE_FORMAT(c.lease_start_time, '%Y.%m.%d'), '-', DATE_FORMAT(c.lease_end_time, '%Y.%m.%d')) AS lease_period,
            c.rental_standard AS rent_standard,
            c.rental_collect AS rent_total
        FROM lease_rent r
        INNER JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN lease_contract_zone lcz ON lcz.contract_id = c.id
        LEFT JOIN zone z ON lcz.zone_id = z.id
        LEFT JOIN company c2 ON c.lessee_id = c2.id
        WHERE 1=1
        <if test="query.yearLimit != null and query.quarterLimit != null">
            AND (r.year &lt; #{query.yearLimit} OR (r.year = #{query.yearLimit} AND r.quarter &lt;= #{query.quarterLimit}))
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
                c2.name LIKE CONCAT('%', #{query.keyword}, '%')
                OR z.zone_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        <if test="query.parkId != null and query.parkId != ''">
            AND z.park_id = #{query.parkId}
        </if>
        <if test="query.buildingId != null and query.buildingId != ''">
            AND z.build_id = #{query.buildingId}
        </if>
        GROUP BY r.id, r.contract_id, c.contract_no, r.year, r.quarter, r.discount, r.reduction, r.receivable_amount, r.received_amount, c.create_time, c2.name, c.lease_start_time, c.lease_end_time, c.rental_standard, c.rental_collect
    </select>

    <!-- 专门用于导出的查询，返回每个租金记录 -->
    <select id="selectForExport" resultMap="LeaseRentListVOMap">
        SELECT
            r.id,
            r.contract_id,
            c.contract_no,
            r.year,
            r.quarter,
            r.discount,
            r.reduction,
            CASE
                WHEN r.receivable_amount_edit IS NOT NULL THEN r.receivable_amount_edit
                ELSE r.receivable_amount
            END as receivable_amount,
            r.received_amount,
            r.unreceived_amount,
            c.create_time AS contract_create_time,
            c2.name AS lessee,
            CONCAT(DATE_FORMAT(c.lease_start_time, '%Y.%m.%d'), '-', DATE_FORMAT(c.lease_end_time, '%Y.%m.%d')) AS lease_period,
            c.rental_standard AS rent_standard,
            c.rental_collect AS rent_total
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN company c2 ON c.lessee_id = c2.id
        WHERE 1=1
        <if test="query.yearLimit != null and query.quarterLimit != null">
            AND (r.year &lt; #{query.yearLimit} OR (r.year = #{query.yearLimit} AND r.quarter &lt;= #{query.quarterLimit}))
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
                c2.name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ORDER BY c.create_time DESC, r.year, r.quarter
    </select>

    <select id="selectByContractIdForList" resultMap="LeaseRentListVOMap">
        SELECT
            r.id,
            r.contract_id,
            r.receivable_amount,
            r.received_amount,
            GROUP_CONCAT(lcz.zone_id) AS zone_name,
            c2.name AS lessee,
            CONCAT(DATE_FORMAT(c.lease_start_time, '%Y.%m.%d'), '-', DATE_FORMAT(c.lease_end_time, '%Y.%m.%d')) AS lease_period,
            c.rental_standard AS rent_standard,
            c.rental_collect AS rent_total
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN lease_contract_zone lcz ON lcz.contract_id = c.id
        LEFT JOIN company c2 ON c.lessee_id = c2.id
        WHERE r.contract_id = #{contractId}
        GROUP BY r.id, r.contract_id, r.receivable_amount, r.received_amount, c2.name, c.lease_start_time, c.lease_end_time, c.rental_standard, c.rental_collect
    </select>

    <select id="selectContractExtInfoById" resultMap="LeaseRentDetailVOMap">
        SELECT
            GROUP_CONCAT(lcz.zone_id) AS zone_name,
            c2.name AS lessee,
            c.remark AS preferential_policy,
            CONCAT(DATE_FORMAT(c.lease_start_time, '%Y.%m.%d'), '-', DATE_FORMAT(c.lease_end_time, '%Y.%m.%d')) AS lease_period
        FROM lease_contract c
        LEFT JOIN lease_contract_zone lcz ON lcz.contract_id = c.id
        LEFT JOIN company c2 ON c.lessee_id = c2.id
        WHERE c.id = #{contractId}
        GROUP BY c2.name, c.lease_start_time, c.lease_end_time
    </select>

    <!-- 租金应收总额 -->
    <select id="sumReceivable" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(receivable_amount), 0) FROM lease_rent
    </select>
    
    <!-- 根据园区ID查询租金应收总额 -->
    <select id="sumReceivableByPark" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(r.receivable_amount), 0)
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN lease_contract_zone lcz ON lcz.contract_id = c.id
        LEFT JOIN zone z ON lcz.zone_id = z.id
        LEFT JOIN park p ON z.park_id = p.id
        WHERE p.id = #{parkId}
    </select>

    <!-- 租金已收总额 -->
    <select id="sumReceived" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(received_amount), 0) FROM lease_rent
    </select>
    
    <!-- 根据园区ID查询租金已收总额 -->
    <select id="sumReceivedByPark" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(r.received_amount), 0)
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN lease_contract_zone lcz ON lcz.contract_id = c.id
        LEFT JOIN zone z ON lcz.zone_id = z.id
        LEFT JOIN park p ON z.park_id = p.id
        WHERE p.id = #{parkId}
    </select>

    <!-- 租金未收总额 -->
    <select id="sumUnreceived" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(receivable_amount - received_amount), 0) FROM lease_rent
    </select>
    
    <!-- 根据园区ID查询租金未收总额 -->
    <select id="sumUnreceivedByPark" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(r.receivable_amount - r.received_amount), 0)
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN lease_contract_zone lcz ON lcz.contract_id = c.id
        LEFT JOIN zone z ON lcz.zone_id = z.id
        LEFT JOIN park p ON z.park_id = p.id
        WHERE p.id = #{parkId}
    </select>

    <!-- 未来30天内到期合同的应收租金 -->
    <select id="sumExpiring" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(r.receivable_amount), 0)
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        WHERE c.lease_end_time BETWEEN NOW() AND #{endDate}
    </select>
    
    <!-- 根据园区ID查询未来30天内到期合同的应收租金 -->
    <select id="sumExpiringByPark" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(r.receivable_amount), 0)
        FROM lease_rent r
        LEFT JOIN lease_contract c ON r.contract_id = c.id
        LEFT JOIN lease_contract_zone lcz ON lcz.contract_id = c.id
        LEFT JOIN zone z ON lcz.zone_id = z.id
        LEFT JOIN park p ON z.park_id = p.id
        WHERE c.lease_end_time BETWEEN NOW() AND #{endDate}
        AND p.id = #{parkId}
    </select>

</mapper>
