package com.quantchi.application.service;

import com.quantchi.application.model.bo.AddContactBO;
import com.quantchi.application.model.bo.ContactListQueryBO;
import com.quantchi.application.model.bo.EditContactBO;
import com.quantchi.application.model.entity.ApplicationContact;
import com.quantchi.application.model.vo.CompanyContactVO;
import com.quantchi.application.model.vo.ContactListPageVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface IApplicationContactService extends IService<ApplicationContact> {

    /**
     * 根据企业ID查询联系人列表
     * @param companyId 企业ID
     * @return 联系人列表
     */
    List<CompanyContactVO> getContactsByCompanyId(String companyId);

    /**
     * 分页查询企业联系人列表
     * @param queryBO 查询参数
     * @return 分页结果
     */
    ContactListPageVO pageContactsByCondition(ContactListQueryBO queryBO);

    /**
     * 编辑企业联系人信息
     * @param editContactBO 编辑联系人请求参数
     * @return 是否编辑成功
     */
    boolean editContact(EditContactBO editContactBO);

    /**
     * 新增企业联系人
     * @param addContactBO 新增联系人请求参数
     * @return 是否新增成功
     */
    boolean addContact(AddContactBO addContactBO);

    /**
     * 删除企业联系人
     * @param contactId 联系人ID
     * @return 是否删除成功
     */
    boolean deleteContact(String contactId);

}
