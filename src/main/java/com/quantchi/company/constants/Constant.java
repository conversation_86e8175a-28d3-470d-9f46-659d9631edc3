package com.quantchi.company.constants;


import com.quantchi.company.model.entity.DmDivision;

import java.util.*;

public class Constant {

    /**
     * 最新年份
     */
    public static final int NEWEST_YEAR = 2024;

    //经济圈
    public static final List<String> ECONOMIC_CIRCLE = new ArrayList<>();
    //重点企业标签
    public static final List<String> TAGS = new ArrayList<>();
    //重点企业标签
    public static final Set<String> TAGSET = new HashSet<>();
    //重点企业标签
    public static final List<String> TAGLIST = new ArrayList<>();
    //经济圈
    public static final List<DmDivision> ECONOMIC_CIRCLE_DIVISION = new ArrayList<>();

    static {
        ECONOMIC_CIRCLE.add("京津冀经济圈");
        ECONOMIC_CIRCLE.add("长三角经济圈");
        ECONOMIC_CIRCLE.add("粤港澳大湾区");
        ECONOMIC_CIRCLE.add("东北经济圈");
        ECONOMIC_CIRCLE.add("中部经济圈");
        ECONOMIC_CIRCLE.add("成渝经济圈");
    }

    static {
        TAGS.add("高新技术企业");
        TAGS.add("专精特新企业");
        TAGS.add("专精特新小巨人企业");
        TAGS.add("企业技术中心");
        TAGS.add("港股");
        TAGS.add("A股");
        TAGS.add("B股");
        TAGS.add("中概股");
        TAGS.add("科技小巨人");
        TAGS.add("独角兽企业");
        TAGS.add("科技型中小企业");
        TAGS.add("隐形冠军企业");
        TAGS.add("雏鹰企业");
    }

    static {
        TAGSET.add("高新技术企业");
        TAGSET.add("专精特新企业");
        TAGSET.add("专精特新小巨人企业");
        TAGSET.add("企业技术中心");
        TAGSET.add("上市企业");
        TAGSET.add("科技小巨人");
        TAGSET.add("独角兽企业");
        TAGSET.add("科技型中小企业");
        TAGSET.add("隐形冠军企业");
        TAGSET.add("雏鹰企业");
    }

    static {
        TAGLIST.add("高新技术企业");
        TAGLIST.add("专精特新企业");
        TAGLIST.add("专精特新小巨人企业");
        TAGLIST.add("企业技术中心");
        TAGLIST.add("上市企业");
        TAGLIST.add("科技小巨人");
        TAGLIST.add("独角兽企业");
        TAGLIST.add("科技型中小企业");
        TAGLIST.add("隐形冠军企业");
        TAGLIST.add("雏鹰企业");
    }

    static {
        ECONOMIC_CIRCLE_DIVISION.add(new DmDivision("京津冀经济圈", "京津冀经济圈"));
        ECONOMIC_CIRCLE_DIVISION.add(new DmDivision("长三角经济圈", "长三角经济圈"));
        ECONOMIC_CIRCLE_DIVISION.add(new DmDivision("粤港澳大湾区", "粤港澳大湾区"));
        ECONOMIC_CIRCLE_DIVISION.add(new DmDivision("东北经济圈", "东北经济圈"));
        ECONOMIC_CIRCLE_DIVISION.add(new DmDivision("中部经济圈", "中部经济圈"));
        ECONOMIC_CIRCLE_DIVISION.add(new DmDivision("成渝经济圈", "成渝经济圈"));
    }

    public static final String LIBRARY_INTERFACE_NOTE = "企业库传company，人才库传expert，专利库传patent，资讯库传news，政策库传policy";

    /**
     * 直辖市的省级别id
     */
    public static final List<String> zhiXiaShiOfProvince = Arrays.asList("division/110000", "division/120000", "division/310000", "division/500000");

    public static final List<String> zhiXiaShiOfProvinceName = Arrays.asList("上海", "北京", "重庆", "天津");

    /**
     * 直辖市的市级别id
     */
    public static final List<String> zhiXiaShiOfCity = Arrays.asList("division/110100", "division/120100", "division/310100", "division/500100");
    public final static List<String> LEVEL_LIST = Arrays.asList("国家级", "省级", "市级");
    public static final Integer REGION_LEVEL = 0;
    public static final Integer PROVINCE_LEVEL = 1;
    public static final Integer CITY_LEVEL = 2;
    public static final Integer AREA_LEVEL = 3;
    public static final String PROVINCE = "province";
    public static final String CITY = "city";
    public static final String AREA = "area";
    public static final String PROVINCE_CODE = "province_code";
    public static final String CITY_CODE = "city_code";
    public static final String AREA_CODE = "area_code";
    public static final List<String> provinceList = Arrays.asList("浙江省", "江苏省", "广东省", "北京市", "山东省", "上海市", "湖北省", "四川省", "安徽省", "河南省", "天津市", "湖南省", "福建省", "陕西省", "辽宁省", "河北省", "重庆市", "江西省", "山西省", "云南省", "广西壮族自治区", "黑龙江省", "吉林省", "贵州省", "甘肃省", "新疆维吾尔自治区", "宁夏回族自治区", "内蒙古自治区", "海南省", "青海省", "西藏自治区", "香港特别行政区", "澳门特别行政区", "台湾省");
    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";
    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";
    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";
    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";
    /**
     * 注册
     */
    public static final String REGISTER = "Register";
    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";
    // 融资轮次标签
    public static final List<String> FINANCING_ROUND_LIST = new LinkedList<>();
    public static final String MATCH_FUZZY = "模糊";
    public static final String MATCH_EXACT = "精确";
    public static final String MATCH_EQUAL = "=";
    public static final String MATCH_GTE = ">=";
    public static final String MATCH_LE = "<=";
    //企业导航-企业标签(除上市企业外与企业库筛选保持一致)
    public static final List<String> COMPANY_NAVIGATION_TAG = new ArrayList<>();
    /**
     * 上市企业标签
     */
    public static final List<String> COMPANY_LISTED_TAG = new ArrayList<>();
    public static final Map<String, String> PATENT_STATUS_AGG = new HashMap<>();

    public static Long HUNDRED = 100L;
    public static Long THOUSAND = 1000L;
    public static Long TEN_THOUSAND = 10000L;
    public static Long MILLION = 1000000L;

    public static final String INDUSTRY_AI = "industry_ai";

    public static final String FREE_CHAIN_ID = "industry_lowaltitude";

    static {
        FINANCING_ROUND_LIST.add("种子轮");
        FINANCING_ROUND_LIST.add("天使轮");
        FINANCING_ROUND_LIST.add("A轮");
        FINANCING_ROUND_LIST.add("B轮");
        FINANCING_ROUND_LIST.add("C轮");
        FINANCING_ROUND_LIST.add("D轮");
        FINANCING_ROUND_LIST.add("D轮之后");
        FINANCING_ROUND_LIST.add("战略融资");
    }

    static {
        COMPANY_NAVIGATION_TAG.add("高新技术企业");
        COMPANY_NAVIGATION_TAG.add("专精特新企业");
        COMPANY_NAVIGATION_TAG.add("专精特新小巨人企业");
        COMPANY_NAVIGATION_TAG.add("企业技术中心");
        COMPANY_NAVIGATION_TAG.add("科技小巨人");
        COMPANY_NAVIGATION_TAG.add("独角兽企业");
        COMPANY_NAVIGATION_TAG.add("科技型中小企业");
        COMPANY_NAVIGATION_TAG.add("隐形冠军企业");
        COMPANY_NAVIGATION_TAG.add("雏鹰企业");
    }

    static {
        COMPANY_LISTED_TAG.add("A股");
        COMPANY_LISTED_TAG.add("B股");
        COMPANY_LISTED_TAG.add("港股");
        COMPANY_LISTED_TAG.add("中概股");
    }

    static {
        PATENT_STATUS_AGG.put("有效", "有效");
        PATENT_STATUS_AGG.put("有效专利", "有效");
        PATENT_STATUS_AGG.put("PCT-有效期内", "有效");
        PATENT_STATUS_AGG.put("PCT-有效期满", "有效");
        PATENT_STATUS_AGG.put("失效", "无效");
        PATENT_STATUS_AGG.put("失效专利", "无效");
        PATENT_STATUS_AGG.put("专利权届满的专利", "无效");
        PATENT_STATUS_AGG.put("在审超期", "无效");
        PATENT_STATUS_AGG.put("审中", "审核中");
        PATENT_STATUS_AGG.put("在审专利", "审核中");
    }

    /**
     * 获取所属区域
     *
     * @param province
     * @param city
     * @return
     */
    public static String getBelongArea(String province, String city, String area) {
        if (province == null) {
            province = "";
        }
        if (city == null) {
            city = "";
        }
        if (area == null) {
            area = "";
        }
        if (!zhiXiaShiOfProvinceName.contains(province)) {
            return province + city + area;
        }
        return city + area;
    }

}
