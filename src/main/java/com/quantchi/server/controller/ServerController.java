package com.quantchi.server.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.server.model.entity.RecentDeclaration;
import com.quantchi.server.model.vo.ParkDynamicsVO;
import com.quantchi.server.service.IRecentDeclarationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */

@Api(tags = "服务端首页")
@RestController
@RequestMapping("/server")
@Slf4j
@RequiredArgsConstructor
public class ServerController {

    private final IRecentDeclarationService recentDeclarationService;

    @GetMapping("/recentDeclaration")
    @ApiOperation("近期申报")
    @SaIgnore
    public ResultInfo<IPage<RecentDeclaration>> recentDeclaration(
            @ApiParam(value = "页码", required = true) @RequestParam(defaultValue = "1") final int pageNum,
            @ApiParam(value = "每页条数", required = true) @RequestParam(defaultValue = "10") final int pageSize) {
        return ResultConvert.success(recentDeclarationService.page(pageNum, pageSize));
    }

    @GetMapping("/parkDynamics")
    @ApiOperation("园区动态")
    @SaIgnore
    public ResultInfo<IPage<ParkDynamicsVO>> parkDynamics(
            @ApiParam(value = "页码", required = true) @RequestParam(defaultValue = "1") final int pageNum,
            @ApiParam(value = "每页条数", required = true) @RequestParam(defaultValue = "10") final int pageSize) {
        return ResultConvert.success(recentDeclarationService.parkDynamics(pageNum, pageSize));
    }
}
