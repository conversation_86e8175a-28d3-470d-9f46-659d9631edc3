package com.quantchi.zone.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.zone.model.entity.Building;
import com.quantchi.zone.mapper.BuildingMapper;
import com.quantchi.zone.service.IBuildingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 楼栋 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class BuildingServiceImpl extends ServiceImpl<BuildingMapper, Building> implements IBuildingService {

    @Override
    public List<Building> listBuildingsSorted(String parkId) {
        // 查询指定园区的所有楼栋
        List<Building> buildings = this.list(Wrappers.lambdaQuery(Building.class)
                .eq(Building::getParkId, parkId));

        // 按buildingName进行字母数字混合排序
        buildings.sort(new AlphanumericComparator());

        return buildings;
    }

    /**
     * 字母数字混合排序比较器
     * 支持A1 < A2 < A10 < B1这样的排序
     */
    private static class AlphanumericComparator implements Comparator<Building> {
        private static final Pattern ALPHANUMERIC_PATTERN = Pattern.compile("([a-zA-Z]*)([0-9]*)");

        @Override
        public int compare(Building b1, Building b2) {
            String name1 = b1.getBuildingName();
            String name2 = b2.getBuildingName();

            if (name1 == null && name2 == null) return 0;
            if (name1 == null) return -1;
            if (name2 == null) return 1;

            return compareAlphanumeric(name1, name2);
        }

        private int compareAlphanumeric(String s1, String s2) {
            Matcher m1 = ALPHANUMERIC_PATTERN.matcher(s1);
            Matcher m2 = ALPHANUMERIC_PATTERN.matcher(s2);

            while (m1.find() && m2.find()) {
                String alpha1 = m1.group(1);
                String alpha2 = m2.group(1);
                String num1 = m1.group(2);
                String num2 = m2.group(2);

                // 先比较字母部分
                int alphaCompare = alpha1.compareToIgnoreCase(alpha2);
                if (alphaCompare != 0) {
                    return alphaCompare;
                }

                // 字母部分相同，比较数字部分
                if (!num1.isEmpty() && !num2.isEmpty()) {
                    try {
                        int n1 = Integer.parseInt(num1);
                        int n2 = Integer.parseInt(num2);
                        int numCompare = Integer.compare(n1, n2);
                        if (numCompare != 0) {
                            return numCompare;
                        }
                    } catch (NumberFormatException e) {
                        // 如果数字解析失败，按字符串比较
                        int numCompare = num1.compareTo(num2);
                        if (numCompare != 0) {
                            return numCompare;
                        }
                    }
                } else if (!num1.isEmpty()) {
                    return 1; // 有数字的排在后面
                } else if (!num2.isEmpty()) {
                    return -1; // 有数字的排在后面
                }
            }

            // 如果前面都相同，按原字符串比较
            return s1.compareToIgnoreCase(s2);
        }
    }
}
