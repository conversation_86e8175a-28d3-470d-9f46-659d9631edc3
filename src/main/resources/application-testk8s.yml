es:
  cluster:
    name: elasticsearch
    hosts: *************:8199
  username: elastic
  password: -XSNbATzRAznqMOa1bfP

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ************************************************************************************************************************************************************************************************************************************************
    username: nanjing_complex
    password: "!&OlTNgvhf"
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池配置
    hikari:
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接数
      maximum-pool-size: 15
      # 空闲连接超时时间，默认600000（10分钟）
      idle-timeout: 600000
      # 连接最大存活时间，0表示永久存活，默认1800000（30分钟）
      max-lifetime: 1800000
      # 连接超时时间，默认30000（30秒）
      connection-timeout: 30000
      # 用于测试连接是否可用的查询语句
      connection-test-query: SELECT 1
  redis:
    database: 13
    host: ************
    port: 60379
    # Redis服务器连接密码
    password: qS$#ON1z6Ns1g&FJ
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

self:
  address: http://test.nanjing-complex.quant-chi.com:30816

# 阿里云 OSS配置
aliyun:
  oss:
    keyId: LTAI5tFhcd5AKJhpJEXhMo7B
    keySecret: ******************************
    bucketName: oss-prod-njxggx
    endpoint: nanjing-1622348395202064.oss-cn-hangzhou-internal.aliyuncs.com


