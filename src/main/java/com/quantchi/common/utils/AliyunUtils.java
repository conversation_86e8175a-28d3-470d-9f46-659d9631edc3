package com.quantchi.common.utils;

import cn.hutool.core.collection.CollUtil;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.alinlp.model.v20200629.GetEcChGeneralRequest;
import com.aliyuncs.alinlp.model.v20200629.GetEcChGeneralResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.quantchi.common.model.AliyunEchData;
import com.quantchi.common.config.properties.AliyunProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13 14:46
 */
@Component
@Slf4j
public class AliyunUtils {

    @Autowired
    private AliyunProperties aliyunProperties;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 阿里云自然语言处理纠错
     */
    public String aliyunNlpCorrect(final String keyword) {
        /**
         * 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
         * 此处以把AccessKey和AccessKeySecret保存在环境变量为例说明。您也可以根据业务需要，保存到配置文件里。
         * 强烈建议不要把AccessKey和AccessKeySecret保存到代码里，会存在密钥泄漏风险
         */
        final String accessKeyId = aliyunProperties.getKeyId();
        final String accessKeySecret = aliyunProperties.getKeySecret();
        final DefaultProfile defaultProfile = DefaultProfile.getProfile(
                "cn-hangzhou",
                accessKeyId,
                accessKeySecret);
        final IAcsClient client = new DefaultAcsClient(defaultProfile);
        //构造请求参数，其中GetPosChEcom是算法的actionName, 请查找对应的《API基础信息参考》文档并替换为您需要的算法的ActionName，示例详见下方文档中的：更换API请求
        final GetEcChGeneralRequest request = new GetEcChGeneralRequest();
        //固定值，无需更改
        request.setSysEndpoint("alinlp.cn-hangzhou.aliyuncs.com");
        //固定值，无需更改
        request.setServiceCode("alinlp");
        //请求参数, 具体请参考《API基础信息文档》进行替换与填写
        request.setText(keyword);
        final long start = System.currentTimeMillis();
        //获取请求结果，注意这里的GetPosChEcom也需要替换
        GetEcChGeneralResponse response = null;
        try {
            response = client.getAcsResponse(request);
        } catch (final ClientException e) {
            log.error("调用阿里云接口失败");
        }
        if (response == null) {
            return null;
        }
        final String data = response.getData();
        log.info("阿里云纠错接口返回{}", data);
        try {
            final AliyunEchData aliyunEchData = objectMapper.readValue(data, AliyunEchData.class);
            final AliyunEchData.ResultDTO result = aliyunEchData.getResult();
            final List<AliyunEchData.ResultDTO.EditsDTO> edits = result.getEdits();
            if (CollUtil.isNotEmpty(edits)) {
                return result.getTarget();
            }
            return null;
        } catch (final JsonProcessingException e) {
            log.error("序列化阿里云文本纠错结果失败", e);
            return null;
        }
    }
}
