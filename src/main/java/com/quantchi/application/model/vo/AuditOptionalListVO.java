package com.quantchi.application.model.vo;

import com.quantchi.application.model.entity.Application;
import com.quantchi.application.model.entity.ApplicationType;
import com.quantchi.application.model.entity.AuditStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class AuditOptionalListVO {

    @ApiModelProperty("申请事项")
    private List<ApplicationType> applicationTypeList;

    @ApiModelProperty("审批状态")
    private List<AuditStatus> auditStatusList;

}
