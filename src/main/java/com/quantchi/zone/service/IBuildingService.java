package com.quantchi.zone.service;

import com.quantchi.zone.model.entity.Building;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 楼栋 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface IBuildingService extends IService<Building> {

    /**
     * 根据园区ID查询楼栋列表，按buildingName排序
     * @param parkId 园区ID
     * @return 排序后的楼栋列表
     */
    List<Building> listBuildingsSorted(String parkId);

}
