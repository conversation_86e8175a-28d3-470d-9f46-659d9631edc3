package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

@Data
@ApiModel("企业关键信息")
public class CompanyInfoVO implements Serializable {
    @ApiModelProperty("企业ID")
    private String id;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("住所/注册地址")
    private String address;

    @ApiModelProperty("法人")
    private String legalPerson;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("通讯地址")
    private String contactAddress;

    @ApiModelProperty("联系电话")
    private String tel;

    @ApiModelProperty("传真")
    private String fax;

    @ApiModelProperty("电子邮件")
    private String email;
}
