package com.quantchi.company.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InvestmentOverviewVO {

    @ApiModelProperty("资金青睐度")
    private String fundingFavorability;

    @ApiModelProperty("融资总次数")
    private int totalFundingRounds;

    @ApiModelProperty("当前融资轮次")
    private String latestFundingRound = "无";

    @ApiModelProperty("最近融资距今")
    private String latestFundingTime;

    @ApiModelProperty("投资活跃度")
    private String investmentActivity;

    @ApiModelProperty("对外投资企业总数")
    private Long totalInvestedCompanies;

    @ApiModelProperty("投资偏好城市")
    private String preferredCity;

    @ApiModelProperty("投资偏好产业")
    private String preferredIndustry;

}

