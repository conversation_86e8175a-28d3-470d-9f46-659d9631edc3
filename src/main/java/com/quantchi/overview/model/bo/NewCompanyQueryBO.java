package com.quantchi.overview.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新入驻企业查询参数
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@ApiModel(value = "NewCompanyQueryBO", description = "新入驻企业查询参数")
public class NewCompanyQueryBO {

    @ApiModelProperty("园区ID")
    private String parkId;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页记录数")
    private Integer pageSize = 10;
}
