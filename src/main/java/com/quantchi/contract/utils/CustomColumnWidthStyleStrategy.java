package com.quantchi.contract.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * 自定义列宽和样式策略
 * 用于租金明细Excel导出时设置合适的列宽
 */
public class CustomColumnWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {
    
    private final int quarterCount;

    public CustomColumnWidthStyleStrategy(int quarterCount) {
        this.quarterCount = quarterCount;
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cell<PERSON><PERSON><PERSON>ist, 
                                Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            Sheet sheet = writeSheetHolder.getSheet();
            int columnIndex = cell.getColumnIndex();

            // 设置列宽（单位：字符宽度 * 256）
            if (columnIndex == 0) {
                // 合同编号
                sheet.setColumnWidth(columnIndex, 20 * 256);
            } else if (columnIndex == 1) {
                // 承租方
                sheet.setColumnWidth(columnIndex, 25 * 256);
            } else if (columnIndex == 2) {
                // 房号/楼栋/园区
                sheet.setColumnWidth(columnIndex, 30 * 256);
            } else if (columnIndex == 3) {
                // 租金标准
                sheet.setColumnWidth(columnIndex, 30 * 256);
            } else {
                // 动态季度列
                int dynamicColumnIndex = columnIndex - 4;
                int fieldIndex = dynamicColumnIndex % 5;

                switch (fieldIndex) {
                    case 0: // 折扣系数
                        sheet.setColumnWidth(columnIndex, 12 * 256);
                        break;
                    case 1: // 减免金额
                    case 2: // 应收金额
                    case 3: // 已收金额
                    case 4: // 未收金额
                        sheet.setColumnWidth(columnIndex, 20 * 256);
                        break;
                }
            }
        }
    }
}
