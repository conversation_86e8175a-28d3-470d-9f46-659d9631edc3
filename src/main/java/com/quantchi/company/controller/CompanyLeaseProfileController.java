package com.quantchi.company.controller;

import com.quantchi.common.domain.ResultInfo;
import com.quantchi.common.utils.ResultConvert;
import com.quantchi.company.model.vo.CompanyLeaseContractSimpleListVO;
import com.quantchi.company.model.vo.CompanyLeaseContractVO;
import com.quantchi.company.model.vo.CompanyLeaseRentBillVO;
import com.quantchi.company.service.impl.CompanyLeaseProfileServiceImpl;
import com.quantchi.company.model.bo.CompanyLeaseContractEditBO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "企业租赁合同管理")
@RestController
@RequestMapping("/company/leaseProfile")
@RequiredArgsConstructor
public class CompanyLeaseProfileController {

    private final CompanyLeaseProfileServiceImpl companyLeaseProfileService;

    /**
     * 根据companyId查询合同及租赁概况信息
     */
    @ApiOperation(value = "获取企业租赁合同信息")
    @GetMapping("/leaseContract")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<CompanyLeaseContractVO> getLeaseContractInfo(@ApiParam("企业ID") @RequestParam final String companyId) {
        return ResultConvert.success(companyLeaseProfileService.getLeaseContractInfo(companyId));
    }

    /**
     * 编辑企业租赁合同信息
     */
    @ApiOperation(value = "编辑企业租赁合同信息")
    @PutMapping("/edit")
    public ResultInfo<Boolean> editLeaseContractInfo(@ApiParam("企业租赁合同编辑参数") @RequestBody CompanyLeaseContractEditBO editBO) {
        boolean result = companyLeaseProfileService.editLeaseContractInfo(editBO);
        return ResultConvert.success(result);
    }

    /**
     * 查询企业租赁合同简要信息
     */
    @ApiOperation(value = "查询企业租赁合同简要信息列表")
    @GetMapping("/leaseContractSimple")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<CompanyLeaseContractSimpleListVO> getLeaseContractSimple(@ApiParam("企业ID") @RequestParam final String companyId) {
        return ResultConvert.success(companyLeaseProfileService.getLeaseContractSimple(companyId));
    }

    /**
     * 查询企业租金账单
     */
    @ApiOperation(value = "查询企业租金账单")
    @GetMapping("/rentBill")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业id", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public ResultInfo<List<CompanyLeaseRentBillVO>> getCompanyLeaseRentBill(@ApiParam("企业ID") @RequestParam final String companyId) {
        return ResultConvert.success(companyLeaseProfileService.getCompanyLeaseRentBill(companyId));
    }
}
